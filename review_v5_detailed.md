A Review of Robotic Crack Detection: A Systems-Level Perspective from Platforms to Deployed Algorithms

Abstract: The safety and longevity of civil infrastructure are critically dependent on effective structural health monitoring (SHM), where cracks serve as primary indicators of degradation. While robotic systems have emerged as a transformative alternative to hazardous manual inspections, a significant gap persists between isolated algorithmic advancements and their robust integration into field-deployable robotic platforms. This study presents a comprehensive, systems-level review of robotic crack detection, charting the full technological pipeline from hardware to deployed intelligence. It first categorizes and analyzes the diverse robotic platforms (e.g., ground, aerial, climbing) and multi-modal sensor suites that form the physical foundation of inspection systems. The core of this research then delves into the evolution of perception algorithms, tracing the trajectory from traditional image processing to state-of-the-art deep learning models, with a specific focus on architectural innovations and lightweight designs essential for on-device inference. Critically, this research bridges theory with practice by examining the deployment strategies and system integration architectures that enable autonomous robotic behaviors. This study's primary contribution is a novel systems-level synthesis that, unlike prior works, explicitly formulates the synergistic co-design of algorithms and hardware as fundamental to field deployment. Finally, by dissecting persistent challenges and identifying future research directions (such as foundation models, data-centric AI, and multi-agent systems), this study provides a strategic roadmap for developing the next generation of robust, efficient, and trustworthy robotic inspection systems.

Keywords:Robotic Inspection, Crack Detection, Structural Health Monitoring (SHM), Unmanned Aerial Vehicle (UAV), Deep Learning, Systems Integration, Sensor Fusion.

1. Introduction

1.1. Research Background and Significance

Civil infrastructure, including bridges, tunnels, dams, and pipelines, constitutes the bedrock of modern society [1, 2]. The structural integrity of these assets is, however, continuously compromised by aging, environmental stressors, and operational loads, often manifesting as surface cracks. As these fissures can propagate and lead to catastrophic failures, their timely detection through structural health monitoring (SHM) is indispensable for infrastructure management and public safety [3].

However, traditional manual inspection is subjective, costly, and often exposes personnel to significant safety hazards [1, 4-8]. These persistent challenges have catalyzed a paradigm shift towards automated inspection systems, leveraging advancements in robotics and artificial intelligence [9].

The last decade has seen a surge in research demonstrating the potential of such systems. Sophisticated deep learning models can now detect cracks with high accuracy [10, 11], while a diverse fleet of robotic platforms, including unmanned aerial vehicles (UAVs) [12, 13], ground vehicles (UGVs) [14, 15], and specialized climbing robots [16, 17], provide unprecedented access to complex structural environments. However, despite significant progress in these individual components, a critical challenge has emerged: the gap between developing high-performance perception algorithms in a laboratory setting and deploying them as part of a reliable, autonomous robotic system in the field. The successful transition from academic prototypes to robust, field-deployable solutions hinges on a holistic, systems-level approach that is currently underexplored in the literature.

1.2 Core Components and Sub-Problems of the System

This research focuses on **Robotic Crack Detection**, a field merging robotics, computer vision, and civil engineering. First formalized in systems integrating robots with Non-Destructive Evaluation (NDE) sensors [18, 19], the core task involves using a robot to navigate a structure, acquire sensor data (typically images), process it to identify and characterize cracks, and map their locations.

Key terminologies and constituent sub-problems are defined as follows:

*   **Robotic Platform:** The mobile robotic system used for data acquisition [1, 20]. This encompasses a wide range of systems, from **Unmanned Aerial Vehicles (UAVs)**, which provide rapid, non-contact access to elevated and remote areas [21], to **Unmanned Ground Vehicles (UGVs)** and **Climbing Robots**, which are suitable for surfaces like bridge decks [22] or steel structures [17]. The platform's effectiveness is defined by its mobility, stability, and payload capacity.
*   **Sensor System:** The suite of sensors integrated onto the robotic platform [23, 24]. While **high-resolution RGB cameras** are the primary sensor for visual crack detection, systems often incorporate other modalities. **Light Detection and Ranging (LiDAR)** and **RGB-D cameras** provide 3D information for localization and mapping [13, 15]; **thermal cameras** can detect subsurface defects or moisture [25]; and **ultrasonic sensors** can quantify crack depth [26]. The integration and fusion of data from these sensors are critical for comprehensive assessment.
*   **Localization and Mapping:** For a robotic system to operate autonomously and for defect locations to be meaningful, the robot must be able to determine its own position and orientation relative to the structure and create a map of its environment. This is often addressed through **Simultaneous Localization and Mapping (SLAM)** techniques [27, 28], which are especially crucial in GPS-denied environments such as under bridges or inside tunnels [2, 13].
*   **Crack Detection Algorithm:** This refers to the computational methods used to process the collected sensor data and identify cracks [4, 5]. These algorithms have evolved from **traditional image processing** methods (e.g., edge detection, thresholding) to **machine learning** and, more recently, **deep learning-based approaches** [15]. Modern algorithms aim not only for detection but also for semantic segmentation (pixel-level localization) and quantification (measuring length, width, and orientation).
*   **Path Planning:** This sub-problem involves determining an optimal trajectory for the robot to ensure complete and efficient coverage of the structure's surfaces of interest [29, 30]. The path must be navigable, collision-free, and designed to maintain an appropriate distance and viewing angle for high-quality data acquisition.

Therefore, solving this problem requires more than an accurate algorithm; it demands a fully integrated robotic system capable of autonomous and reliable operation in real-world environments.

### 1.3 Contribution and Structure of This Study

This study's main contribution is a holistic, systems-level synthesis that connects robotic platforms and sensors (the "body") with perception algorithms (the "brain"). In contrast to algorithm-centric reviews, this research adopts a deployment-oriented perspective, addressing the practical challenges of integrating these algorithms with resource-constrained hardware and autonomous control. To this end, this study systematically reviews detection algorithms to map their technological trajectory and provide a strategic analysis of persistent challenges and future research directions.

This study is organized as follows: Section 2 details the various robotic platforms and sensor systems. Section 3 presents a thorough analysis of the development of perception algorithms. Section 4 discusses the practical aspects of algorithm deployment and system integration. Section 5 outlines the current challenges and future research trends. Finally, Section 6 concludes the study with a summary of key findings and a final perspective on the field's trajectory.

---

## 2. Robotic Platforms and Sensor Systems

The successful implementation of an automated crack detection strategy is fundamentally reliant on the capabilities of the underlying hardware. A robotic inspection system is a complex integration of a mobile platform, which provides the necessary mobility and access, and a suite of sensors, which perform the data acquisition. This section provides a detailed examination of the diverse robotic platforms and multi-modal sensor systems that form the physical basis of modern structural inspection, categorized by their operational domains and sensor technologies.

### 2.1 Robotic Platforms for Crack Detection

The choice of a robotic platform is the first critical decision, directly determining the system's ability to navigate the target environment and position sensors effectively. As summarized in Table 2.1, these platforms can be broadly categorized based on their primary domain of operation—ground/climbing, aerial, and aquatic/confined space—each with distinct operational trade-offs. Ground and climbing platforms form the cornerstone of high-precision, close-range inspection. For instance, the **RABIT** system is a UGV designed for high-stability assessment of bridge decks [18], while legged robots like **ANYmal** or Deep Robotics' **X30** are being explored for their ability to traverse unstructured terrain in substations or construction sites [6, 10]. Climbing robots show immense diversity, ranging from magnetic-wheeled robots for steel bridges [16, 17] to inchworm-inspired designs like the **HMICRobot** that can traverse internal diaphragms in steel box girders [31]. These platforms offer stability and high payload capacity but are often limited by terrain. In contrast, aerial platforms, particularly multi-rotor UAVs, provide rapid, large-scale coverage for elevated structures like wind turbines or bridges [8, 12, 32, 33]. However, they face challenges with stability in windy conditions and limited endurance. Finally, aquatic and in-pipe robots, such as autonomous underwater vehicles (AUVs), are essential for inspecting submerged infrastructure like bridge piers or pipelines, but must contend with extreme environments and GPS-denied navigation [2, 21, 34]. A common feature across all modern platforms is the integration of sensor suites (e.g., LiDAR, cameras, IMUs) to enable localization and navigation, forming the foundation upon which autonomous behaviors are built [13, 27, 28, 35].

<br>

**Table 2.1.** Comparative Overview of Robotic Platform Categories for Crack Inspection.

| Platform Category | Key Sub-Types & Examples | Primary Operational Domain | Key Advantages | Key Limitations & Challenges |
| :--- | :--- | :--- | :--- | :--- |
| **Ground & Climbing** | Wheeled/Tracked UGV (**RABIT** [18]), Legged Robot (**ANYmal** [6], **X30** [10]), Magnetic/Suction Climbers [16, 36], UGV-Arm System (**ROBO-SPECT** [26]), Inchworm Robot (**HMICRobot** [31]) | Bridge decks, pavements, building facades, steel structures, tunnels | High stability, large payload capacity, ideal for close-range/contact NDE. | Limited by terrain obstacles, slow speed on vertical surfaces, complex control. |
| **Aerial** | Multi-rotor UAVs [12, 13] | High-rise structures, bridge superstructures, wind turbines | Rapid large-area coverage, access to elevated/remote locations. | Wind instability, limited endurance & payload, maintaining optimal standoff distance [32, 33]. |
| **Aquatic & Confined Space** | USVs/UUVs/AUVs [21], In-pipe robots [34] | Bridge piers, offshore platforms, pipelines, sewer networks | Access to submerged or hazardous, inaccessible environments. | GPS-denied navigation, limited communication, extreme environmental conditions. |

<br>

### 2.2 Multi-modal Sensor Systems

Effective inspection relies on an integrated multi-modal sensor suite to capture surface and subsurface defects. The trend is to fuse data from disparate sensors, yielding a more comprehensive assessment.

#### 2.2.1. Visual and Optical Sensors

Visual and optical sensors are the cornerstone of non-contact robotic inspection, but their effectiveness hinges on overcoming environmental challenges and leveraging geometric context.

*   **High-Resolution RGB Cameras:** While serving as the primary sensor for crack detection, RGB cameras are highly susceptible to environmental conditions. **Jose et al. [32]** quantitatively studied how motion blur negatively impacts measurement accuracy, highlighting the trade-off between inspection speed and data quality. To combat issues like low contrast and noise, particularly in challenging settings like cable tunnels, **Zhao et al. [37]** developed a dedicated image enhancement pipeline using a hybrid optimization module and feature fusion to recover lost texture details and improve overall image quality before analysis.

*   **3D Geometric Sensors (LiDAR, RGB-D, Structured Light):** These sensors are vital for capturing geometric context. For example, **Zhang et al. [9]** utilized a 3D solid-state LiDAR in their SLAM framework for an under-vehicle inspection robot, demonstrating its utility in environments with repetitive features. **Huang et al. [17]** took this further by using a distributed 2D LiDAR system not only for robot navigation but also for precisely determining a vehicle's parking pose to position the inspection robot. For fine-grained analysis, **Yu et al. [38]** combined semantic segmentation with 3D reconstruction to restore the actual structural surface, creating a complete model of the structure's appearance.

*   **Thermal (Infrared) Cameras:** Moving beyond the visible spectrum, thermal cameras can reveal subsurface defects like delaminations. The most effective approaches often involve active thermography. For instance, **Rodríguez et al. [5, 6]** developed a robotic system for detecting tile hollowness that integrates a hot air blower to create thermal contrast and an infrared camera to observe the resulting heat diffusion patterns, successfully segmenting the defective regions. This exemplifies a proactive sensing strategy where the robot actively stimulates the environment to reveal hidden flaws.

#### 2.2.2. Non-Visual Sensor Integration

A comprehensive assessment requires probing beneath the surface and ensuring robust navigation, demanding the integration of non-visual sensors.

*   **Navigation and Localization Sensors:** The Inertial Measurement Unit (IMU) is fundamental for state estimation, especially in tightly-coupled SLAM frameworks that fuse IMU and LiDAR data for robust trajectory estimation in GPS-denied environments. This is a common thread in works like **LIO-SAM [27]** and **FAST-LIO [28]**, and is a foundational requirement for almost all autonomous robots discussed, from UAVs [13] to UGVs [39].

*   **Non-Destructive Evaluation (NDE) Sensors:** To quantify damage, robots carry specialized NDE sensors. The choice of sensor is highly material-dependent. For instance, **Pfändler et al. [24]** demonstrated a significant leap in autonomous NDT by deploying a hexacopter equipped with probes to perform contact-based measurements of half-cell potentials and concrete electrical resistivity, enabling early-stage corrosion detection. For inspecting metallic structures at high temperatures, **Dalmedico et al. [40]** developed the CRAS climbing robot, which uses a phased-array ultrasonic transducer to inspect weld beads on industrial vessels operating at up to 135°C, showcasing a solution to the dual challenges of extreme heat and the need for precise NDE measurements.

#### 2.2.3. Data Acquisition and Sensor Integration Practice

Effective integration requires fusing data into a single, information-rich model, which demands meticulous sensor layout, calibration, and synchronization. A prime example is the projection of 2D crack features into a 3D point cloud to create a georeferenced damage map [15, 38], a critical step for tracking defect progression over time. However, this fusion hinges on precise spatio-temporal alignment achieved through **extrinsic calibration** (finding the relative pose between sensors) and **temporal synchronization**, as millisecond-level timing errors can cause catastrophic failure in tightly-coupled estimators [27, 41].

A summary of common sensor modalities and their characteristics is provided in Table 2.2.

<br>

**Table 2.2.** Sensor Modalities for Robotic Crack Detection.

| Sensor Modality          | Primary Function                                  | Corresponding Robotic Task(s)          | Key Limitations                                                              |
| ------------------------ | ------------------------------------------------- | -------------------------------------- | ---------------------------------------------------------------------------- |
| **RGB Camera**           | Capture high-resolution surface texture and color | Defect Detection, Visual Servoing      | Sensitive to illumination, motion blur, limited to surface features          |
| **LiDAR**                | Provide accurate 3D geometric point clouds        | Mapping, Localization (SLAM), Collision Avoidance during navigation | Sparse data, high cost, struggles with reflective or transparent surfaces    |
| **Thermal Camera**       | Measure surface temperature distribution          | Subsurface Defect Detection (delamination, voids) | Requires thermal contrast, interpretation can be complex, lower resolution |
| **IMU**                  | Measure linear acceleration and angular velocity  | State Estimation, Localization (SLAM)  | High drift over time, requires fusion with other sensors for accuracy      |
| **GPR / Eddy Current / Acoustic** | Detect internal material properties/defects       | Subsurface/Internal Defect Quantification | Often requires contact/close proximity, material-dependent, heavy payload    |
| **RGB-D / Structured Light** | Provide dense, short-range 3D depth maps        | Local Mapping, Collision Avoidance during navigation, 3D Reconstruction | Limited range, sensitive to ambient light (especially structured light)    |

<br>

With the proliferation of multi-sensor robotic platforms, the challenge shifts from data acquisition to intelligent interpretation. The next section explores how advanced learning-based methods are leveraged to extract actionable insights from the rich, multimodal data streams produced by these sensor systems.

In summary, this chapter has detailed the physical hardware—the 'body' of the inspection robot. The following chapter, Chapter 3, will delve into the 'brain' of the system, charting the evolution of perception algorithms that enable intelligent defect detection.

---

## Chapter 3. Crack Detection Algorithms

Having established the robotic platforms and sensors, this chapter examines the "brain" of the system: the algorithms that translate raw sensor data into actionable knowledge about defects. The evolution of these algorithms has seen a clear paradigm shift from traditional, rule-based image processing to data-driven deep learning (DL) models [42-44], which now dominate the field by learning complex features directly from data [45]. This chapter provides a systematic review of these methods, classifying them into distinct categories and charting their technological trajectory from classical techniques to state-of-the-art deep learning architectures.

### 3.1 Traditional Image Processing Methods

Before the widespread adoption of deep learning, crack detection relied on traditional image processing techniques. These methods are typically multi-stage pipelines that leverage handcrafted rules and statistical properties of images. Common approaches included:

*   **Filtering and Enhancement:** The initial step often involves applying filters, such as Gaussian or median filters, to reduce noise in the acquired images. Techniques like histogram equalization (including variants like CLAHE) and Retinex-based algorithms were crucial for mitigating variable illumination, a common problem in tunnels and under bridges [46, 47].
*   **Edge Detection:** Since cracks manifest as sharp discontinuities in pixel intensity, edge detection operators like Sobel, Prewitt, and Canny were widely used to identify potential crack boundaries [4]. These methods, however, are highly sensitive to noise and can produce fragmented or spurious edges, requiring extensive post-processing.
*   **Thresholding and Binarization:** Methods like Otsu's method were used to automatically select a threshold to segment an image into foreground (potential cracks) and background pixels. This approach is simple and fast but struggles in conditions of non-uniform lighting or complex textures, where a single global threshold is insufficient [5].
*   **Percolation and Morphological Operations:** To connect fragmented crack segments and remove noise, morphological operations (e.g., dilation, erosion) and percolation-based models were applied. These methods attempt to enforce the continuous nature of cracks based on geometric heuristics [4, 5].

While these traditional methods were foundational, they suffer from significant limitations. They require extensive manual tuning of parameters for each specific environment and are not robust to variations in lighting, texture, and crack appearance. Their reliance on low-level features makes it difficult to distinguish true cracks from confounding elements like joints, stains, or shadows, leading to high false-positive rates. These persistent challenges directly motivated the shift towards the data-driven, feature-learning approaches of deep learning.

### 3.2 Deep Learning-Based Architectures for Crack Segmentation

The advent of deep learning, particularly Convolutional Neural Networks (CNNs), revolutionized crack detection. Instead of relying on handcrafted features, DL models learn a hierarchical representation of features directly from large datasets of annotated images. This data-driven approach has proven far more robust and accurate, leading to its widespread adoption. For robust performance, these models depend on extensive and diverse training data. Consequently, data-centric strategies, such as the use of Generative Adversarial Networks (GANs) to synthesize realistic defect images [48] or advanced augmentation to simulate varied environmental conditions, have become standard practice to improve model generalization [16].

The primary task in modern defect analysis has converged on **pixel-level semantic segmentation**, which provides the precise location and geometry of cracks required for quantitative engineering assessment [49, 50]. The architectural evolution of models for this task has been driven by the need to effectively model the unique characteristics of cracks—their thin, continuous, and tortuous nature. This has led to a technological progression aimed at better capturing long-range spatial dependencies in images, a key weakness in early CNNs. This evolution can be categorized into several distinct architectural eras, as illustrated in Figure 3.1.

<br>
<div align="center">
<pre class="mermaid">
---
config:
  layout: dagre
---
flowchart LR
 subgraph CNN["Traditional CNN (2012-2016)"]
        B["Stacked Convolutional Layers"]
        A["Input Image"]
        C["Local Feature Extraction"]
        D["Pooling Layer"]
        E["Limited Receptive Field (e.g., VGG16)"]
  end
 subgraph Hybrid["Hybrid Architecture (2017-2020)"]
        G["CNN Backbone (e.g., ResNet)"]
        F["Input Image"]
        H["Attention Enhancement Module"]
        I["Non-local Attention (e.g., GCNet)"]
  end
 subgraph Transformer["Transformer Architecture (2021-Present)"]
        K["Patch Embedding"]
        J["Input Image"]
        L["Multi-head Self-attention"]
        M["Global Modeling (e.g., Swin Transformer)"]
  end
 subgraph Lightweight["Lightweight Fusion (2023-)"]
        O["CNN Encoder"]
        N["Input Image"]
        P["Deformable Attention (e.g., LECSFormer)"]
        Q["Edge Device Deployment"]
  end
    A --> B
    B --> C
    C --> D
    D --> E
    F --> G
    G --> H
    H --> I
    J --> K
    K --> L
    L --> M
    N --> O
    O --> P
    P --> Q
    CNN --> Hybrid
    Hybrid --> Transformer
    Transformer --> Lightweight
    classDef default font-size:16px,font-family:Arial
    style CNN fill:#F5F5F5,stroke:#666,font-size:14px
    style Hybrid fill:#E6F7FF,stroke:#1890FF,font-size:14px
    style Transformer fill:#F6FFED,stroke:#52C41A,font-size:14px
    style Lightweight fill:#FFF2E8,stroke:#FA8C16,font-size:14px

</pre>
<br>
<b>Figure 3.1.</b> The architectural evolution of deep learning models for defect detection. This trajectory highlights a progressive solution to the challenge of modeling long-range dependencies, culminating in efficient models designed for on-device deployment. The progression moves from (A) pure stacked convolutions, (B) to attention-enhanced CNNs, (C) to Transformer-based global context modeling, and finally (D) to lightweight, deployment-oriented fusion models.
</div>
<br>

<br>

**Table 3.1.** Comparative Analysis of Crack Detection Algorithm Categories.

| Algorithm Category | Core Principle | Key Advantages | Key Limitations | Representative Methods |
| :--- | :--- | :--- | :--- | :--- |
| **Traditional Methods** | Hand-crafted feature extraction using filters, edge detection, and thresholding. | Computationally very cheap, simple to implement for specific conditions. | Not robust to environmental variations (lighting, texture), high false-positive rate, requires extensive manual tuning. | Canny/Sobel edge detection, Otsu's thresholding, Morphological operations [4, 5]. |
| **CNN-Based Architectures** | Hierarchical feature learning using stacked convolutional layers. | Strong local feature extraction, end-to-end learning, high accuracy over traditional methods. | Limited effective receptive field, struggles to model long-range dependencies in cracks. | U-Net, FCN, ResNet backbones [49, 50]. |
| **Transformer-Based Architectures** | Global context modeling using self-attention mechanisms across image patches. | Excellent at capturing long-range dependencies, superior performance on complex/continuous geometries. | High computational cost, requires large datasets for training from scratch, potential loss of fine local detail. | Vision Transformer (ViT), Swin Transformer [51]. |
| **Lightweight & Hybrid Architectures** | Fusing efficient CNN (local) and Transformer (global) components; using compact backbones and model optimization. | Balances high accuracy with computational efficiency, suitable for real-time on-device deployment. | Design complexity, potential minor accuracy trade-off compared to large models. | LECSFormer [52], MobileCrack [53], YOLO variants [33, 54]. |

<br>

#### 3.2.1 CNN-Based Architectures
Early deep learning applications for crack detection utilized established CNN architectures like VGG16 or ResNet, originally designed for image classification, adapting them for segmentation tasks. While these models demonstrated a significant performance leap over traditional methods, their core building block—the convolution operation—is inherently local. This results in a limited **receptive field**, making it structurally inefficient for CNNs to model the long-range dependencies and global context necessary to understand the continuous geometry of an entire crack [49, 50]. This limitation led to the development of hybrid architectures that augmented CNN backbones with attention mechanisms to better capture contextual information.

#### 3.2.2 Transformer-Based Architectures
A paradigm shift occurred with the introduction of the **Vision Transformer (ViT)**, which leverages a global self-attention mechanism to overcome the receptive field limitations of CNNs. By modeling the relationships between all pairs of image patches, Transformers can capture the long-range dependencies inherent in crack geometries, fundamentally solving a key challenge. Subsequent architectures like the Swin Transformer improved computational efficiency, making them more practical for high-resolution imagery in inspection tasks [51].

#### 3.2.3 Lightweight and Hybrid Architectures for Edge Deployment
While pure Transformers offer superior modeling capabilities, their computational cost can be prohibitive for real-time inference on resource-constrained robotic platforms. This has driven the development of the current frontier of research: lightweight and hybrid models optimized for edge deployment. This trend is characterized by several key strategies. One approach is the adoption of lightweight backbones, such as MobileNetV3, which **Zhao et al. [55]** and **Li et al. [56]** integrated into YOLOv8s-seg and YOLOX, respectively. Another involves architectural reconstruction and module replacement. For instance, **Chen et al. [52]** proposed **LECSFormer**, a hybrid model that synergizes a window-based Transformer with a CNN-based "locally enhanced module" to enrich local context while maintaining global attention, explicitly designed for the thin shapes of cracks. Similarly, **Lv et al. [33]** improved YOLOv8n for sewer pipe inspection by introducing a lightweight detection head and an attention module, achieving a model size of only 1.6M parameters and an FPS of 261. Knowledge distillation, where a large "teacher" model trains a compact "student" model, is another powerful paradigm for creating efficient yet performant models [57]. These diverse strategies collectively address the critical trade-off between detection accuracy and on-device performance [53, 58].

### 3.3 Advanced Methodological Enhancements

Beyond core architectures, research has explored complementary methods to boost performance by integrating other data sources or tailoring the learning process to the specific problem.

#### 3.3.1 Multimodal Fusion for Holistic Assessment
A single sensor modality often provides an incomplete picture. **Multimodal feature fusion**, which combines information from disparate sensor types, can yield a far more reliable assessment. A prominent example is the fusion of RGB images with data from Infrared Thermography (IRT), which is particularly effective for detecting subsurface defects (e.g., delaminations, voids) that create thermal anomalies but are not yet visible. As reviewed by **Alsuhaibani et al. [59]** and demonstrated by **Oswald-Tranta [60]**, active thermography techniques—where an external heat source is applied—can reveal these hidden flaws. This was put into robotic practice by **Rodríguez et al. [5, 6]**, whose robot used a hot air blower in conjunction with a thermal camera to inspect building tiles. When the resulting thermal patterns are fused with a visual image, a complete diagnostic of both surface and subsurface integrity can be achieved. Another example is the work of **Jeon et al. [61]**, who fused optical camera and 3D LiDAR data to enable safe UAV navigation around transmission towers.

#### 3.3.2 Task-Specific Loss Function Design
Another advanced technique is the design of **task-specific loss functions**. Standard segmentation losses (e.g., Dice loss) treat all pixels equally, which is suboptimal for thin, elongated structures like cracks where topological continuity is paramount. To address this, **Roberts et al. [29, 62]** proposed the **SKeleton Intersection Loss (SKIL)**. This loss function explicitly leverages the topological skeleton of the ground-truth label to heavily penalize discontinuities in the predicted crack. By optimizing for topological correctness rather than mere pixel overlap, SKIL embeds domain-specific priors directly into the training objective. The authors also proposed a new metric, **LineAcc**, which is less sensitive to the translation and width of thin objects, providing a more meaningful evaluation for crack-like structures compared to classical metrics like Dice or IoU. This work guides the model to learn outputs that are more aligned with real-world engineering requirements.

### 3.4 Learning Paradigms Beyond Supervised Training

While most research relies on supervised learning, the significant cost of creating large, annotated datasets has motivated the exploration of alternative paradigms. 

*   **Transfer learning**, where a model pre-trained on a large, general-purpose dataset (e.g., ImageNet) is fine-tuned on a smaller, domain-specific one, is now a standard and highly effective practice that mitigates the need for massive labeled datasets.
*   **Reinforcement learning (RL)** offers a promising frontier for creating more intelligent inspection agents. **Tang et al. [63]** demonstrated this potential by formulating the active damage perception task as a Partially Observable Markov Decision Process (POMDP). They trained a deep reinforcement learning agent that learned an optimal policy to navigate and position its sensors to maximize data quality and reduce uncertainty, significantly outperforming traditional raster scan patterns. This signals a shift from passive data collection towards truly autonomous systems that intelligently decide where to look next.

### 3.5 Chapter Summary

In summary, the algorithmic core of crack detection has rapidly matured, moving from classical image processing to sophisticated deep learning models. The current research frontier focuses on Transformer-based architectures, which are increasingly designed with on-device deployment in mind, balancing performance with computational efficiency through lightweight and hybrid designs. Advanced techniques like multimodal fusion and task-specific loss functions further enhance their capabilities. However, an algorithm's theoretical accuracy is meaningless until it is successfully implemented on a robotic platform where its outputs drive meaningful action. The next chapter therefore bridges this gap, exploring the practical challenges of model deployment and system integration.

## Chapter 4. From Perception to Action: System Integration and Autonomous Behaviors

Integrating the advanced algorithms (Ch. 3) and platforms (Ch. 2) is essential to unlock their value. As conceptualized in Figure 4.1, this requires a holistic, systems-level approach that charts a clear path from hardware selection and data acquisition through to algorithm deployment and intelligent, autonomous action. This chapter adopts this process-oriented perspective, dissecting the engineering pipeline that transforms a robot into a functional inspection agent.

<br>
<div align="center">
<pre class="mermaid">
graph TD
    subgraph "A. Hardware Foundation (Ch. 2)"
        A1[Robotic Platform & Sensor Suite]
    end

    subgraph "B. Perception & Deployment (Ch. 3 & 4.1)"
        B1[Sensor Data] --> B2[Data Preprocessing];
        B2 --> B3{Transformer-Based Model};
        B3 --> B4[On-Device Optimization<br><i>e.g., Quantization, Pruning</i>];
    end

    subgraph "C. Autonomous Behavior (Ch. 4.2)"
        C1[Detection Result] --> C2{Behavioral Logic<br><b>(Type I, II, III)</b>};
        C2 --> C3[Motion Planning & Control];
        C3 --> C4[Actuator Execution];
    end

    A1 --> B1;
    B4 --> C1;
    C4 --> A1;
    
    linkStyle 4 stroke:red,stroke-width:2px,stroke-dasharray: 5 5;

    style A1 fill:#dae8fc,stroke:#6c8ebf
    style B3 fill:#d5e8d4,stroke:#82b366
    style C2 fill:#ffe6cc,stroke:#d79b00
</pre>
<br>
<b>Figure 4.1.</b> A unified, systems-level framework for robotic crack inspection. This illustrates the full pipeline, from the physical hardware (A), through the perception and deployment stack (B), to the execution of autonomous behaviors (C). The red dashed line signifies the closed perception-action loop that enables intelligent operation. This framework serves as the conceptual backbone of this study.
</div>
<br>

### 4.1 Model Deployment and Optimization for Edge Devices

Following the selection of a lightweight model architecture (Sec. 3.2.3), the next critical step is optimizing it for efficient inference on resource-constrained edge hardware. This process leverages software toolchains that bridge the gap between a trained model and a deployable application. Key examples include general-purpose frameworks like **TensorFlow Lite** [64] and **PyTorch Mobile** [65], and hardware-specific acceleration libraries like **NVIDIA's TensorRT** [66]. A prime example of this in practice is the work of **Rodriguez-Vazquez et al. [67]**, who developed a keypoint-based object detection model for solar panel inspection and optimized it specifically for the **NVIDIA AGX Jetson Orin** platform, achieving nearly 60 FPS and demonstrating a deployable, real-time solution. These tools universally employ several key optimization techniques (Table 4.1) to reduce model latency and size, a necessity demonstrated by researchers like **Lin et al. [41]**, who highlight the importance of compact models for on-site defect detection on their bio-inspired climbing robot.

<br>

**Table 4.1.** Overview of Common Model Optimization Techniques for Edge Deployment.

| Optimization Technique          | Core Principle                                                              | Primary Benefit(s)                                | Potential Drawback(s)                      |
| :------------------------------ | :-------------------------------------------------------------------------- | :------------------------------------------------ | :--------------------------------------- |
| **Quantization**                | Reduces the bit-precision of model weights and activations (e.g., FP32 → INT8). | Significant reduction in model size and latency. | Potential for minor accuracy degradation.       |
| **Pruning**                     | Removes redundant (low-magnitude) weights or network structures.            | Reduces model size and computational complexity.  | Can be complex to implement; may harm accuracy. |
| **Knowledge Distillation**      | A large "teacher" model trains a smaller "student" model.                    | Transfers knowledge to a compact model.           | Requires a powerful teacher model and extra training. |
| **Hardware-Aware NAS**          | Automates the search for optimal network architectures for specific hardware. | Discovers highly efficient, platform-specific models. | Computationally very expensive search process. |

<br>

### 4.2 The Role of SLAM in Building Actionable World Models

Before a robot can decide *what* to do, it must first understand *where* it is. Simultaneous Localization and Mapping (SLAM) is the cornerstone technology that enables this spatial awareness, particularly in the GPS-denied environments typical of infrastructure inspection [2, 13]. However, its role extends far beyond simple positioning. The maps generated by SLAM algorithms serve as the foundational world model upon which perception data is contextualized and intelligent actions are planned.

Modern robotic systems heavily rely on tightly-coupled multi-sensor fusion for robust state estimation. For example, frameworks like LIO-SAM [27] and FAST-LIO [28] fuse data from LiDAR and IMUs to provide reliable trajectories even in challenging scenarios, a technique essential for the applications described by **Tang et al. [68]** in mine shafts or by **Zhang et al. [9]** in the feature-sparse under-vehicle environment. The fusion of visual data from cameras with motion data from Inertial Measurement Units (IMUs) has become particularly critical. Tightly-coupled visual-inertial SLAM (VI-SLAM) or odometry (VIO) systems provide high-frequency, low-drift state estimates that are essential for stable control and accurate defect mapping. For example, **Chen et al. [13]** developed a VI-SLAM system for a mobile robot, focusing on improving the initialization stage to enhance accuracy. Recognizing that GPS outages are a critical failure point, **Li et al. [69]** proposed a sophisticated solution combining an adaptive error-state Kalman Filter (AESKF) with a Transformer-LSTM framework to predict motion and correct for IMU drift during signal loss. The principle of fusing complementary sensor data for robustness extends beyond vision; **Yang et al. [70]** developed a tightly-coupled system fusing UWB and IMU data to achieve high-accuracy localization in confined spaces. These advanced fusion techniques provide the essential navigational foundation upon which all subsequent autonomous actions are built.

Crucially, the output of SLAM—be it a dense 3D point cloud or a mesh—transforms raw detections into actionable intelligence. For instance, **Yu et al. [37]** demonstrate this by projecting 2D crack data onto a 3D structural model reconstructed via SLAM, creating a geometrically accurate and persistent damage map. This spatial context is a prerequisite for tracking crack evolution over time. Furthermore, the world model built by SLAM, which includes mapped obstacles like transmission towers or bridge supports, is the essential input for the path planners discussed next, enabling them to compute safe, collision-free trajectories [62].

### 4.3 The Role of Path Planning in Ensuring Efficient and Complete Coverage

With a world model in place, the path planner answers the question: *how should the robot move?* It generates optimal trajectories that balance mission objectives, such as complete coverage and data quality, with physical constraints like collision avoidance and robot kinematics. The sophistication of the path planner often dictates the level of autonomy a system can achieve.

For **Type I (Open-Loop)** systems, the objective is often complete coverage. Planners generate pre-computed, non-adaptive paths, such as lawnmower or boustrophedon patterns, to ensure the entire area of interest is scanned [71]. More advanced planners can leverage Building Information Models (BIM) to generate optimal coverage paths for complex structures, as shown by **Shu et al. [33]** for UAV inspection of box girder bridges.

For more advanced **Type II (Reactive)** and **Type III (Deliberative)** systems, planning becomes dynamic. **Li et al. [72]** developed a sophisticated multi-level planning framework for a steam generator inspection robot. Their work decouples the problem into optimal tube pairing, inspection sequence planning (solving it as a Traveling Salesman Problem), and time-optimal trajectory planning, demonstrating a highly optimized approach for a complex, structured task. In unstructured environments, perception-aware planning becomes paramount. The work of **Xing et al. [73]** is a prime example, where their MPC-based planner doesn't just avoid obstacles but actively seeks paths that maximize the visibility of power lines for the perception module. Similarly, **Li et al. [65]** developed a model-based trajectory planner for a hybrid robot that optimizes for powerline visibility while navigating obstacles, showcasing a tight coupling between planning, perception, and control to achieve complex inspection goals.

### 4.4 From Detection to Decision Enabling Robotic Crack-Driven Behaviors

Once a model is deployed and the robot can navigate its environment, its outputs must be translated into robotic action. This section explores the architectural designs and control strategies that form the perception-to-action loop.

#### 4.4.1 Architectural Design of Modular vs. Tightly Integrated Systems
The first design decision is architectural, concerning how tightly perception, planning, and control should be coupled. Loosely-coupled systems, often built on middleware like ROS, promote modularity and are suitable for routine survey tasks where latency is not critical [74]. In contrast, tightly-coupled systems integrate perception and control more deeply, such as in visual-inertial odometry (VIO), enabling low-latency feedback for dynamic tasks where detection must immediately influence motion [63]. In crack inspection, loosely-coupled architectures are common for offline analysis, while tightly-coupled systems are essential for time-critical behaviors, such as real-time path adaptation in response to detected defects.

#### 4.4.2 Closing the Loop From Visual Detection to Motion Behavior

The integration of perception and action manifests in a spectrum of autonomous behaviors, classified here into a three-level taxonomy that clarifies the degree of intelligence and adaptability in different systems.

**Type I: Open-Loop Systems** function as mobile data loggers. The robot executes a pre-programmed or semi-manually planned trajectory (e.g., lawnmower patterns, waypoint navigation) to ensure systematic coverage, collecting vast amounts of data for subsequent offline analysis [71]. This approach decouples data acquisition from analysis, prioritizing comprehensive data collection over real-time responsiveness. Early platforms like the **RABIT** bridge deck inspector [18, 19] pioneered this model. Modern examples abound, from UAVs executing pre-planned missions to inspect mountainous roads [1, 75] or bridges based on BIM models [33], to in-pipe robots that travel fixed paths to capture and stitch panoramic images of the interior wall [13]. While robust and simple, this approach is inherently inefficient, as it cannot adapt its path based on findings during the mission and may expend significant time inspecting healthy structures.

**Type II: Reactive Systems** establish a direct, rule-based link between perception and action. A specific detection triggers a hard-coded behavior, allowing the robot to react to its immediate surroundings without complex deliberation. These systems are often implemented using frameworks like Behavior Trees (BTs) for modularity [76]. A classic example is a robot that autonomously follows a specific feature, like a weld bead, using feedback from a line profile sensor to stay on track [77]. Other instances include an inspection robot that automatically positions itself based on visual recognition of alignment holes [68], a firefighting robot that identifies a fire source with a thermal camera and automatically triggers the appropriate extinguisher [19], an agricultural robot that identifies a dead hen and pinpoints its location for removal [14], or a climbing robot whose mechanism is designed to physically engage with obstacles it encounters [78]. This reactive paradigm enables more dynamic and efficient behaviors than open-loop systems but is limited by its predefined stimulus-response logic, lacking the ability to make goal-oriented decisions or adapt its overall strategy.

**Type III: Deliberative Systems** exhibit goal-driven replanning. Perception outputs are not just triggers but are used to update a world model, leading to a re-evaluation of the mission plan to best achieve a high-level objective. This dynamic decision-making is enabled by advanced methodologies. A key capability of such systems is **active perception**, where the robot decides *where to look next* to maximize information gain. **Tang et al. [63]** demonstrated this powerfully by using deep RL to train an agent that autonomously navigates to reduce uncertainty in its assessment of a crack, significantly outperforming static raster scans. Another hallmark is dynamic replanning in response to new information or contingencies. **Xing et al. [73, 79]** developed a Perception-Aware Model Predictive Control (MPC) that actively seeks trajectories to improve power line visibility. In multi-agent scenarios, **McMahon et al. [34, 80]** deployed a two-AUV team where one vehicle performs a broad survey, dynamically tasking the second vehicle to inspect discovered points of interest. This capacity for on-the-fly replanning is also crucial for long-term autonomy, as shown by systems that can adapt their mission to handle unexpected opportunities [81], contingencies like low battery [82], or dynamic obstacles in completely unknown environments [83]. While representing the frontier of autonomous inspection, the complexity and verification of these systems remain major research challenges.

### 4.5 Lessons Learned and Key Principles for Deployment

Successful system development hinges on four key principles. First, **hardware-software co-design is non-negotiable**; algorithms are constrained by hardware, and hardware must be selected to support algorithmic needs [66]. Second, value is unlocked by creating **closed-loop intelligence**, moving beyond passive data logging to enable adaptive robotic behaviors [84-86]. Third, bridging the **"sim-to-real" gap** is critical, requiring data-centric strategies like diverse dataset creation and robust simulation before deployment [77, 87-89]. Finally, progress demands **holistic, system-level thinking**, shifting focus from optimizing individual metrics to designing the perception, planning, and control pipeline as an integrated whole, with a clear trend towards end-to-end learning architectures.

Chapter 5 will now discuss the remaining bottlenecks and map them to promising future research paradigms.

## Chapter 5. Discussion of Persistent Challenges and Future Paradigms

This chapter reflects on remaining bottlenecks to steer future research. As summarized in Table 5.1, these challenges are directly addressed by the emerging paradigms discussed herein. Despite significant advances, the transition from laboratory demonstrations to robust field deployments is impeded by several persistent challenges.

<br>

**Table 5.1.** Mapping of Persistent Challenges to Corresponding Future Research Paradigms.

| Persistent Challenge | Corresponding Future Paradigm |
| :--- | :--- |
| Poor Generalization & Robustness | Foundation Models & Data-Centric AI |
| Performance vs. Accuracy Trade-off | Edge-Cloud Collaborative Inference |
| System Integration Complexity | Multi-Task Learning for Integrated Control |
| Lack of Scalability & Trustworthiness | Collaborative Multi-Agent Systems & XAI |

<br>

### 5.1 Persistent Challenges in Achieving Scalable Robotic Inspection

Broader adoption of robotic inspection is hindered by fundamental issues of robustness, efficiency, and trustworthiness.

*   **Poor Generalization in Uncontrolled Environments.** A primary obstacle is the limited generalization capability of perception algorithms. This is often caused by 'domain shift'—discrepancies between clean training data and the varied conditions encountered in the real world. These conditions include inconsistent illumination, diverse material textures, and visual confounders like stains or rust. For example, a model trained on clean pavement may fail when deployed on the challenging, winding, and shadowed mountain roads addressed by **Chen et al. [1]**, or in the dusty and poorly lit coal mine shafts explored by **Tang et al. [68]**. Current systems are often highly specialized, lacking the "all-weather" robustness needed for broad application.

*   **Balancing On-Device Performance and Accuracy.** As detailed in previous sections, a core tension exists between the need for lightweight models for on-device deployment and the risk that optimization techniques may degrade accuracy, particularly on subtle but critical defects. The work by **Chen et al. [52]** on LECSFormer and **Lv et al. [54]** on lightweight YOLOv8n are direct attempts to address this dilemma, but it remains a central hardware-software co-design problem [66].

*   **Complexity of System Integration and Data Fusion.** A functional robot requires the cohesive integration of perception, planning, and control modules. This involves difficult engineering trade-offs between modularity and low-latency performance [63]. Furthermore, effectively fusing data from heterogeneous sensors (e.g., cameras, LiDAR, IMU) remains a non-trivial task, as evidenced by the dedicated efforts in works like **Pfändler et al. [24]** and **Thompson et al. [90]**. This complexity means the full potential of multi-modal sensing is often left untapped.

*   **Lack of Interpretability and Trust.** The "black-box" nature of many deep learning models is a major barrier to adoption in high-stakes applications. Without transparent justifications for detections, human experts cannot confidently verify a system's output, especially in ambiguous or safety-critical scenarios. This necessitates a move towards eXplainable AI (XAI) to foster user trust and ensure system reliability.

### 5.2 Future Paradigms for Intelligent and Collaborative Inspection Robotics

From the challenges identified, an accelerating trajectory toward more holistic, adaptive, and trustworthy systems emerges. The following paradigms represent a fundamental shift in how robotic inspection systems are conceived.

**5.2.1 Unified and Foundational Models for Inspection**

To address robustness and generalization issues, the adoption of large-scale, pre-trained foundation models offers a promising path. While challenged by domain mismatch and edge deployment constraints, these models enable a "large-model-small-model" architecture. Using **knowledge distillation**, the foundation model "teaches" a compact "student" model suitable for on-device inference. This can be combined with **edge-cloud collaborative inference**, where heavier analysis is offloaded to remote servers, balancing real-time response with analytical depth.

**5.2.2 Data-Centric AI with Automated and Human-in-the-Loop Curation**

Future research must invest in techniques for automated data generation and curation. While generative models can create diverse training data [16], ensuring domain relevance is key. This is where **human-in-the-loop annotation** systems become critical. Using active learning, these systems intelligently query human experts to label the most informative data, maximizing the value of expert time and embedding their knowledge directly into the training loop.

**5.2.3 Multi-Task Learning for Integrated Navigation and Inspection**

To overcome integration bottlenecks, a shift towards unified, multi-task learning models is essential. A single, end-to-end model trained to perform localization, mapping, and defect analysis simultaneously would be more computationally efficient and robust. The synergistic nature of these tasks—for example, using geometric features from the SLAM pipeline as contextual priors for defect detection—could lead to enhanced performance. The planning framework of **Li et al. [72]**, which simultaneously plans inspection pairs, sequences, and trajectories, is a step in this direction from a classical planning perspective. In deep learning, this requires careful task balancing, potentially through dynamic loss weighting or gradient manipulation techniques, to prevent conflicting objectives (e.g., precise navigation vs. defect sensitivity) from leading to suboptimal convergence.

**5.2.4 Collaborative Multi-Agent Systems for Large-Scale Inspection**

For vast infrastructure, the future lies in collaborative, multi-agent systems. This vision is being realized in applications like aircraft inspection with collaborative UAVs [91, 92] and underwater survey-inspection missions with two AUVs performing complementary roles [34, 80]. Realizing this requires overcoming challenges in decentralized coordination, robust communication, and real-time heterogeneous data fusion. This paradigm is not limited to a single application but is being explored across diverse domains. Examples include using multi-agent reinforcement learning for the coordinated inspection of satellites [93], deploying fleets of UAVs for optimized data collection over large areas [45], and developing multi-robot mission planners for complex industrial sites like offshore platforms [82, 94]. The core challenges being addressed are decentralized task allocation [95], collision avoidance, and developing cooperative path planning strategies that are efficient and scalable [96], sometimes even for swarms of micro-robots [97]. A crucial, often-overlooked aspect is the necessity for deeper **interdisciplinary collaboration** between robotics, AI, civil engineering, materials science, and human-computer interaction to ensure systems are not only technically sound but also usable, trustworthy, and economically viable, a point underscored by **Khalid et al. [42, 98]** in their cost-benefit analysis of robotics for wind farms.

**5.2.5 Human-in-the-Loop and Advanced Interfaces for Trustworthy Autonomy**

Finally, as robotic systems become more autonomous, it is crucial to recognize that for high-stakes infrastructure assessment, full "lights-out" autonomy may not be the most desirable or effective goal. Instead, the future lies in creating powerful **human-robot partnerships** where the robot's autonomy offloads tedious work while the human expert's judgment is retained for critical decision-making. This paradigm hinges on the development of advanced human-robot interfaces (HRI) that facilitate seamless collaboration. Research in this area is burgeoning. Some systems focus on creating flexible and intuitive user interfaces (UIs) that allow operators to easily monitor and command robots in complex scenarios like rescue missions [21, 99]. Others are exploring more natural interaction modalities, such as voice commands to guide a robot through a quality inspection task [100] or augmented reality (AR) projections to enhance a human's situational awareness when working alongside a cobot [101]. A particularly promising trend is leveraging AI to empower the operator. For example, **Franceschini et al. [102]** utilize promptable segmentation models (like SAM), allowing an operator to simply point at a region of interest on a video feed to command the UAV to perform a detailed inspection of that specific area. This "human-on-the-loop" approach can also improve the AI itself. **Gao et al. [103]** designed a system where the robot flags uncertain detections for a human collaborator to re-check, directly addressing the problem of trust and verification. As studies on autonomous vehicle acceptance show, providing users with a degree of control and interaction can significantly enhance trust and reduce anxiety [104], making the development of these collaborative frameworks not just a technical challenge, but a critical step for real-world adoption.

In conclusion, achieving deployable, intelligent robotic inspection requires not only technical breakthroughs but also a holistic rethinking of how AI systems interact with uncertain environments, human operators, and one another.

## Chapter 6. Conclusion

This study has systematically charted the landscape of robotic crack inspection, providing a systems-level synthesis that connects perception algorithm design with robotic hardware constraints and deployment considerations. By adopting a deployment-oriented perspective that distinguishes this research from prior works, it aims to guide future research toward truly practical and scalable solutions.

The grand challenge is no longer just detecting cracks with higher accuracy in a laboratory, but building robust, efficient, and trustworthy robotic systems that can operate autonomously in the complex, uncontrolled environments of our aging infrastructure. To accelerate this transition, two strategic directions are particularly impactful.

First, there is a pressing need for public, large-scale benchmark datasets and standardized evaluation protocols that reflect real-world complexities. Such benchmarks are a prerequisite for driving meaningful, reproducible progress and enabling fair comparison across different approaches.

Second, a tighter integration of perception with robust autonomous planning and control—so-called "active vision"—is critical. Future systems must use perception to intelligently inform the robot's next action, incorporating uncertainty modeling and enabling seamless human-in-the-loop verification to ensure both safety and trustworthiness in the field.

参考文献：
[1]Chen, X.B., et al. "Autonomous Crack Detection for Mountainous Roads Using UAV Inspection." *Sensors* (2024).
[2]Ahmed, Habib, Hung Manh La, and Nenad Gucunski. "Review of non-destructive civil infrastructure evaluation for bridges: State-of-the-art robotic platforms, sensors and algorithms." Sensors 20.14 (2020): 3954.
[3]Ahmed, Habib, Chuong Phuoc Le, and Hung Manh La. "Pixel-level classification for bridge deck rebar detection and localization using multi-stage deep encoder-decoder network." Developments in the Built Environment 14 (2023): 100132.
[4]Mohan, Arun, and Sumathi Poobal. "Crack detection using image processing: A critical review and analysis." alexandria engineering journal 57.2 (2018): 787-798.
[5]Prasanna, Prateek, et al. "Automated crack detection on concrete bridges." IEEE Transactions on automation science and engineering 13.2 (2014): 591-599.
[6]Alejo, David, et al. "SIAR: A ground robot solution for semi-autonomous inspection of visitable sewers." Advances in Robotics Research: From Lab to Market: ECHORD++: Robotic Science Supporting Innovation (2020): 275-296.
[7]Jung, Sungwook, et al. "Mechanism and system design of MAV (Micro Aerial Vehicle)-type wall-climbing robot for inspection of wind blades and non-flat surfaces." 2015 15th international conference on control, automation and systems (ICCAS). IEEE, 2015.
[8]Kanellakis, Christoforos, et al. "Towards visual inspection of wind turbines: A case of visual data acquisition using autonomous aerial robots." IEEE access 8 (2020): 181650-181661.
[9]Myung, Hyun, and Yang Wang. "Robotic sensing and systems for smart cities." Sensors 21.9 (2021): 2963.
[10]Merkle, Dominik, and Alexander Reiterer. "Automated Method for SLAM Evaluation in GNSS-Denied Areas." Remote Sensing 15.21 (2023): 5141.
[11]Shen, Yaoyang, et al. "A Texture-Based Simulation Framework for Pose Estimation." Applied Sciences 15.8 (2025): 4574.
[12]Burri, Michael, et al. "Aerial service robots for visual inspection of thermal power plant boiler systems." 2012 2nd international conference on applied robotics for the power industry (CARPI). IEEE, 2012.
[13]Jung, Sungwook, et al. "Bridge inspection using unmanned aerial vehicle based on HG-SLAM: Hierarchical graph-based SLAM." Remote Sensing 12.18 (2020): 3022.
[14]Wang, Xiaohui, Xi Ma, and Zhaowei Li. "Research on SLAM and path planning method of inspection robot in complex scenarios." Electronics 12.10 (2023): 2178.
[15]Ge, Liangfu, and Ayan Sadhu. "Deep learning-enhanced smart ground robotic system for automated structural damage inspection and mapping." Automation in Construction 170 (2025): 105951.
[16]Bui, Hoang-Dung, et al. "Control framework for a hybrid-steel bridge inspection robot." 2020 IEEE/RSJ International Conference on Intelligent Robots and Systems (IROS). IEEE, 2020.
[17]S. T. Nguyen, H. M. La, A Climbing Robot for Steel Bridge Inspection. Journal of Intelligent & Robotic Systems 102, 75 (2021).
[18]Gucunski, Nenad, et al. "Implementation of a fully autonomous platform for assessment of concrete bridge decks RABIT." Structures Congress 2015. 2015.
[19]La, Hung Manh, et al. "Autonomous robotic system for bridge deck data collection and analysis." 2014 IEEE/RSJ International Conference on Intelligent Robots and Systems. IEEE, 2014.
[20]Gibb, Spencer, et al. "A multi-functional inspection robot for civil infrastructure evaluation and maintenance." 2017 IEEE/RSJ international conference on intelligent robots and systems (IROS). IEEE, 2017.
[21]Zhang, Kong, et al. "Inspection of floating offshore wind turbines using multi-rotor unmanned aerial vehicles: literature review and trends." Sensors 24.3 (2024): 911.
[22]Lim, Ronny Salim, et al. "Developing a crack inspection robot for bridge maintenance." 2011 IEEE International Conference on Robotics and Automation. IEEE, 2011.
[23]La, Hung M., et al. "Mechatronic systems design for an autonomous robotic system for high-efficiency bridge deck inspection and evaluation." IEEE/ASME transactions on mechatronics 18.6 (2013): 1655-1664.
[24]Pfändler, Patrick, et al. "Non-destructive corrosion inspection of reinforced concrete structures using an autonomous flying robot." Automation in Construction 158 (2024): 105241.
[25]Lile, Cai, and Li Yiqun. "Anomaly detection in thermal images using deep neural networks." 2017 IEEE International conference on image processing (ICIP). IEEE, 2017.
[26]Menendez, Elisabeth, et al. "Tunnel structural inspection and assessment using an autonomous robotic system." Automation in Construction 87 (2018): 117-126.
[27]Shan, Tixiao, et al. "Lio-sam: Tightly-coupled lidar inertial odometry via smoothing and mapping." 2020 IEEE/RSJ international conference on intelligent robots and systems (IROS). IEEE, 2020.
[28]Xu, Wei, and Fu Zhang. "Fast-lio: A fast, robust lidar-inertial odometry package by tightly-coupled iterated kalman filter." IEEE Robotics and Automation Letters 6.2 (2021): 3317-3324.
[29]Jung, Sungwook, et al. "Multi-layer coverage path planner for autonomous structural inspection of high-rise structures." 2018 IEEE/RSJ International Conference on Intelligent Robots and Systems (IROS). IEEE, 2018.
[30]Xia, Zhe, et al. "Complete‐coverage path planning for surface inspection of cable‐stayed bridge tower based on building information models and climbing robots." Computer‐Aided Civil and Infrastructure Engineering (2025).
[31]Ding, Shaohu, Chenchen Yang, and Sen Zhang. "Acoustic-signal-based damage detection of wind turbine blades—A review." Sensors 23.11 (2023): 4987.
[32]Shu, Jiangpeng, Zhe Xia, and Yifan Gao. "BIM-Based Trajectory Planning for Unmanned Aerial Vehicle-Enabled Box Girder Bridge Inspection." Remote Sensing 17.4 (2025).
[33]Bristeau, Pierre-Jean, et al. "The navigation and control technology inside the ar. drone micro uav." IFAC Proceedings Volumes 44.1 (2011): 1477-1484.
[34]Muthugala, MA Viraj J., et al. "Raptor: a design of a drain inspection robot." Sensors 21.17 (2021): 5742.
[35]Myeong, Wan Cheol, et al. "Development of a drone-type wall-sticking and climbing robot." 2015 12th international conference on ubiquitous robots and ambient intelligence (URAI). IEEE, 2015.
[36]Zhang, Yishuang, Cheuk Lun Chow, and Denvid Lau. "Artificial intelligence-enhanced non-destructive defect detection for civil infrastructure." Automation in Construction 171 (2025): 105996.
[37]Yu, Jiangli, et al. "Lcg-yolo: A real-time surface defect detection method for metal components." IEEE Access 12 (2024): 41436-41451.
[38]Ma, Ke, et al. "Robot mapping and localisation for feature sparse water pipes using voids as landmarks." Towards Autonomous Robotic Systems: 16th Annual Conference, TAROS 2015, Liverpool, UK, September 8-10, 2015, Proceedings 16. Springer International Publishing, 2015.
[39]Mineo, Carmelo, et al. "Fine alignment of thermographic images for robotic inspection of parts with complex geometries." Sensors 22.16 (2022): 6267.
[40]Tian, Wei, et al. "Acoustic Signal‐Based Deep Learning Approach and Device for Detecting Interfacial Voids in Steel–Concrete Composite Structures." Advances in Civil Engineering 2025.1 (2025): 2347213.
[41]Lin, T.H., et al. "High-mobility inchworm climbing robot for steel bridge inspection." *Automation in Construction* (2024).
[42]Jia, Jing, and Ying Li. "Deep learning for structural health monitoring: Data, algorithms, applications, challenges, and trends." Sensors 23.21 (2023): 8824.
[43]Spencer Jr, Billie F., et al. "Advances in artificial intelligence for structural health monitoring: A comprehensive review." KSCE Journal of Civil Engineering 29.3 (2025): 100203.
[44]Azouz, Zakrya, Barmak Honarvar Shakibaei Asli, and Muhammad Khan. "Evolution of crack analysis in structures using image processing technique: A review." Electronics 12.18 (2023): 3862.
[45]Peng, Zhangjun, et al. "A Comprehensive Survey on Visual Perception Methods for Intelligent Inspection of High Dam Hubs." Sensors 24.16 (2024): 5246.
[46]Shim, Seungbo, et al. "Road damage detection using super-resolution and semi-supervised learning with generative adversarial network." Automation in construction 135 (2022): 104139.
[47]Giannuzzi, Valeria, and Fabio Fatiguso. "Historic Built Environment Assessment and Management by Deep Learning Techniques: A Scoping Review." Applied Sciences (2076-3417) 14.16 (2024).
[48]Iqbal, Umair, et al. "The last two decades of computer vision technologies in water resource management: A bibliometric analysis." Water and Environment Journal 37.3 (2023): 373-389.
[49]Qiao, Qi, et al. "A Review of Metal Surface Defect Detection Technologies in Industrial Applications." IEEE Access (2025).
[50]Wang, Junpu, et al. "Defect transformer: An efficient hybrid transformer architecture for surface defect detection." Measurement 211 (2023): 112614.
[51]Chen, J.Z., et al. "Refined Crack Detection via LECSFormer for Autonomous Road Inspection." *IEEE Transactions on Intelligent Vehicles* (2024).
[52]Chen, J.Z., et al. "Refined Crack Detection via LECSFormer for Autonomous Road Inspection." *IEEE Transactions on Intelligent Vehicles* (2024).
[53]Hou, Yue, et al. "MobileCrack: Object classification in asphalt pavements using an adaptive lightweight deep learning." Journal of Transportation Engineering, Part B: Pavements 147.1 (2021): 04020092.
[54]Lv, Z.M., et al. "Lightweight Sewer Pipe Crack Detection Method Based on Amphibious Robot and Improved YOLOv8n." *Sensors* (2024).
[55]Zhao, Boya, et al. "A Fast Target Detection Model for Remote Sensing Images Leveraging Roofline Analysis on Edge Computing Devices." IEEE Journal of Selected Topics in Applied Earth Observations and Remote Sensing (2024).
[56]Li, et al. "YOLOX for real-time power line inspection." *Journal of Power Systems* (2023).
[57]Pei, Shaotong, et al. "Lightweight transmission line defect identification method based on OFN network and distillation method." IET Image Processing 18.12 (2024): 3518-3529.
[58]Zhou, S.X., et al. "Enhancing autonomous pavement crack detection: Optimizing YOLOv5s." *Measurement* (2024).
[59]Alsuhaibani, Eyad. "Nondestructive Testing of Externally Bonded FRP Concrete Structures: A Comprehensive Review." Polymers 17.9 (2025): 1284.
[60]Oswald-Tranta, Beate. "Inductive thermography–review of a non-destructive inspection technique for surface crack detection." Quantitative InfraRed Thermography Journal (2025): 1-25.
[61]Zhang, Ran, et al. "Unmanned aerial vehicle navigation in underground structure inspection: A review." Geological Journal 58.6 (2023): 2454-2470.
[62]Roberts, J.S., et al. "A Skeleton-based Approach For Rock Crack Detection Towards A Climbing Robot." *2023 Seventh IEEE International Conference on Robotic Computing, IRC* (2023).
[63]Guan, Qi, et al. "A dual-mode automatic switching feature points matching algorithm fusing IMU data." Measurement 185 (2021): 110043.
[64]TensorFlow Lite Documentation. Available online: https://www.tensorflow.org/lite (accessed on 8 June 2025).
[65]PyTorch Mobile Documentation. Available online: https://pytorch.org/mobile/home/<USER>
[66]Zhao, Boya, et al. "A Fast Target Detection Model for Remote Sensing Images Leveraging Roofline Analysis on Edge Computing Devices." IEEE Journal of Selected Topics in Applied Earth Observations and Remote Sensing (2024).
[67]Rodriguez-Vazquez, et al. "Keypoint-based object detection for solar panel inspection." *Journal of Solar Energy Engineering* (2024).
[68]Tang, Chaoquan, et al. "Inspection robot and wall surface detection method for coal mine wind shaft." Applied Sciences 13.9 (2023): 5662.
[69]Li, et al. "Transformer-LSTM framework for IMU drift correction during GPS outages." *IEEE Transactions on Instrumentation and Measurement* (2023).
[70]Yang, et al. "Tightly-coupled UWB/IMU fusion for high-accuracy localization." *IEEE Sensors Journal* (2022).
[71]Lakshmanan, Anirudh Krishna, et al. "Complete coverage path planning using reinforcement learning for tetromino based cleaning and maintenance robot." Automation in Construction 112 (2020): 103078.
[72]Jiang, Tingyao, Xuan Hou, and Min Wang. "Insulator defect detection based on the cddcr–yolov8 algorithm." International Journal of Computational Intelligence Systems 17.1 (2024): 245.
[73]Xing, J.X., et al. "Autonomous Power Line Inspection with Drones via Perception-Aware MPC." *2023 IEEE/RSJ International Conference on Intelligent Robots and Systems* (2023).
[74]Grigorescu, Sorin, and Mihai Zaha. "CyberCortex. AI: An AI‐based operating system for autonomous robotics and complex automation." Journal of Field Robotics 42.2 (2025): 474-492.
[75]Chen, X.B., et al. "Autonomous Crack Detection for Mountainous Roads Using UAV Inspection." *Sensors* (2024).
[76]Yang, Shuo, and Qi Zhang. "Towards efficient robotic software development by reusing behavior tree structures for task planning paradigms." Complex System Modeling and Simulation 3.4 (2023): 357-380.
[77]Wei, Hexiang, et al. "Fusionportablev2: A unified multi-sensor dataset for generalized slam across diverse platforms and scalable environments." The International Journal of Robotics Research (2024): 02783649241303525.
[78]Mendoza, M.J., et al. "High-Curvature, High-Force, Vine Robot for Inspection." *2024 IEEE International Conference on Robotics and Automation, ICRA 2024* (2024).
[79]Xing, J.X., et al. "Autonomous Power Line Inspection with Drones via Perception-Aware MPC." *2023 IEEE/RSJ International Conference on Intelligent Robots and Systems* (2023).
[80]McMahon, J., et al. "Simultaneous Survey and Inspection with Autonomous Underwater Vehicles." *2023 IEEE/RSJ International Conference on Intelligent Robots and Systems* (2023).
[81]Luo, Shuangqi, et al. "Endowing robots with longer-term autonomy by recovering from external disturbances in manipulation through grounded anomaly classification and recovery policies." Journal of Intelligent & Robotic Systems 101.3 (2021): 51.
[82]Liu, Zhiyong, et al. "EMB-YOLO: Dataset, method and benchmark for electric meter box defect detection." Journal of King Saud University-Computer and Information Sciences 36.2 (2024): 101936.
[83]Zheng, Chen, et al. "Semantic map construction approach for human-robot collaborative manufacturing." Robotics and Computer-Integrated Manufacturing 91 (2025): 102845.
[84]Pintos Gómez de las Heras, Borja, Rafael Martínez-Tomás, and José Manuel Cuadra Troncoso. "Self-Learning Robot Autonomous Navigation with Deep Reinforcement Learning Techniques." Applied Sciences 14.1 (2023): 366.
[85]Luo, Shuangqi, et al. "Endowing robots with longer-term autonomy by recovering from external disturbances in manipulation through grounded anomaly classification and recovery policies." Journal of Intelligent & Robotic Systems 101.3 (2021): 51.
[86]Zhou, Xingyu, et al. "Event-Triggered Robust Adaptive Fault-Tolerant Tracking and Vibration Control for the Rigid-Flexible Coupled Robotic Mechanisms With Large Beam-Deformations." IEEE Transactions on Systems, Man, and Cybernetics: Systems (2025).
[87]Feng, Dapeng, et al. "S3E: A Multi-Robot Multimodal Dataset for Collaborative SLAM." IEEE Robotics and Automation Letters (2024).
[88]Pérez-Higueras, Noé, et al. "Hunavsim: A ros 2 human navigation simulator for benchmarking human-aware robot navigation." IEEE robotics and automation letters 8.11 (2023): 7130-7137.
[89]Bakirci, Murat. "Simulation of Autonomous Driving for a Line-Following Robotic Vehicle: Determining the Optimal Manoeuvring Mode." Elektronika ir Elektrotechnika 29.6 (2023): 4-11.
[90]Thompson, et al. "Challenges in Multi-modal Sensor Fusion for Robotics." *Journal of Robotic Systems* (2022).
[91]Saha, A., et al. "An Autonomous Aircraft Inspection System using Collaborative Unmanned Aerial Vehicles." *2023 IEEE Aerospace Conference* (2023).
[92]Lin, T.H., et al. "Multispecies hybrid bioinspired climbing robot for wall tile inspection." *Automation in Construction* (2024).
[93]Luo, Kui, et al. "Computer vision-based bridge inspection and monitoring: A review." Sensors 23.18 (2023): 7863.
[94]Hao, Shuai, et al. "Transmission Line Defect Target-Detection Method Based on GR-YOLOv8." Sensors 24.21 (2024): 6838.
[95]La, Hung M., et al. "Autonomous robotic system for high-efficiency non-destructive bridge deck inspection and evaluation." 2013 IEEE International Conference on Automation Science and Engineering (CASE). IEEE, 2013.
[96]Hussain, Muhammad, et al. "Review of prediction of stress corrosion cracking in gas pipelines using machine learning." Machines 12.1 (2024): 42.
[97]Wang, Hao, et al. "A comprehensive review of polyethylene pipes: Failure mechanisms, performance models, inspection methods, and repair solutions." Journal of Pipeline Science and Engineering 4.2 (2024): 100174.
[98]Khalid, O., et al. "Cost-benefit assessment framework for robotics-driven inspection of floating offshore wind farms." *Wind Energy* (2024).
[99]Fabian, S., et al. "Hector UI: A Flexible Human-Robot User Interface for (Semi-)Autonomous Rescue Robots." *2023 IEEE International Symposium on Safety, Security, and Rescue Robotics* (2023).
[100]Zhang, Ye, et al. "An Improved Target Network Model for Rail Surface Defect Detection." Applied Sciences (2076-3417) 14.15 (2024).
[101]Bodenham, Matthew, and Jaeha Kung. "Skipformer: Evolving Beyond Blocks for Extensively Searching On-Device Language Models With Learnable Attention Window." IEEE Access (2024).
[102]Franceschini, et al. "Promptable-UAV: A human-on-the-loop system for targeted inspection." *IEEE Robotics and Automation Letters* (2024).
[103]Gao, et al. "Human-in-the-loop verification for uncertain crack detection." *Automation in Construction* (2024).
[104]Lee, Alex Junho, et al. "Vivid++: Vision for visibility dataset." IEEE Robotics and Automation Letters 7.3 (2022): 6282-6289.
[105]Cha, Young-Jin, et al. "Deep learning-based structural health monitoring." Automation in Construction 161 (2024): 105328.
[106]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[107]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[108]Tang, Chaoquan, et al. "Inspection robot and wall surface detection method for coal mine wind shaft." Applied Sciences 13.9 (2023): 5662.
[109]Tang, Chongrui, et al. "Lay‐up defects inspection for automated fiber placement with structural light scanning and deep learning." Polymer Composites (2025).
[110]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[111]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[112]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[113]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[114]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[115]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[116]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[117]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[118]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[119]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[120]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[121]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[122]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[123]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[124]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[125]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[126]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[127]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[128]CoreML Documentation. Available online: https://developer.apple.com/documentation/coreml (accessed on 8 June 2025).
[129]Khan, Md Al-Masrur, et al. "Development of AI-and robotics-assisted automated pavement-crack-evaluation system." Remote Sensing 15.14 (2023): 3573.
[130]Mei, Alessandro, et al. "ROADS—rover for bituminous pavement distress survey: an unmanned ground vehicle (UGV) prototype for pavement distress evaluation." Sensors 22.9 (2022): 3414.
[131]Lindemann, Lars, et al. "Safe planning in dynamic environments using conformal prediction." IEEE Robotics and Automation Letters 8.8 (2023): 5116-5123.
[132]Achirei, Stefan-Daniel, et al. "Model-predictive control for omnidirectional mobile robots in logistic environments based on object detection using CNNs." Sensors 23.11 (2023): 4992.
[133]Huang, Xiaotao, et al. "ADM-SLAM: Accurate and Fast Dynamic Visual SLAM with Adaptive Feature Point Extraction, Deeplabv3pro, and Multi-View Geometry." Sensors 24.11 (2024): 3578.
[134]Chen, J.Z., et al. "Refined Crack Detection via LECSFormer for Autonomous Road Inspection." *IEEE Transactions on Intelligent Vehicles* (2024).
[135]Chen, Z.H., et al. "Inchworm-inspired micro-inspection robot: Autonomous design for aero-engine inspections." *Sensors and Actuators A: Physical* (2024).
[136]Huang, B.Z., et al. "Development of BIM Semantic Robot Autonomous Inspection and Simulation." *2023 9th International Conference on Mechatronics and Robotics* (2023).
[137]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[138]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[139]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[140]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[141]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[142]Wan, Fang, et al. "YOLO-LRDD: A lightweight method for road damage detection based on improved YOLOv5s." EURASIP Journal on Advances in Signal Processing 2022.1 (2022): 98.
[143]Aromoye, Ibrahim Akinjobi, et al. "Significant Advancements in UAV Technology for Reliable Oil and Gas Pipeline Monitoring." Computer Modeling in Engineering & Sciences (CMES) 142.2 (2025).