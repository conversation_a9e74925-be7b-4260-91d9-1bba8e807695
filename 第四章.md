220、Edge Computing for AI-Based Brain MRI Applications: A Critical
   Evaluation of Real-Time Classification and Segmentation
Abtract: Medical imaging plays a pivotal role in diagnostic medicine with technologies like Magnetic Resonance Imagining (MRI), Computed Tomography (CT), Positron Emission Tomography (PET), and ultrasound scans being widely used to assist radiologists and medical experts in reaching concrete diagnosis. Given the recent massive uplift in the storage and processing capabilities of computers, and the publicly available big data, Artificial Intelligence (AI) has also started contributing to improving diagnostic radiology. Edge computing devices and handheld gadgets can serve as useful tools to process medical data in remote areas with limited network and computational resources. In this research, the capabilities of multiple platforms are evaluated for the real-time deployment of diagnostic tools. MRI classification and segmentation applications developed in previous studies are used for testing the performance using different hardware and software configurations. Cost-benefit analysis is carried out using a workstation with a NVIDIA Graphics Processing Unit (GPU), Jetson Xavier NX, Raspberry Pi 4B, and Android phone, using MATLAB, Python, and Android Studio. The mean computational times for the classification app on the PC, Jetson Xavier NX, and Raspberry Pi are 1.2074, 3.7627, and 3.4747 s, respectively. On the low-cost Android phone, this time is observed to be 0.1068 s using the Dynamic Range Quantized TFLite version of the baseline model, with slight degradation in accuracy. For the segmentation app, the times are 1.8241, 5.2641, 6.2162, and 3.2023 s, respectively, when using JPEG inputs. The Jetson Xavier NX and Android phone stand out as the best platforms due to their compact size, fast inference times, and affordability.

221、Module Lightweighting and Path Transferring in Vision-Language Models
   for Efficient Edge Deployment
AB We propose an efficient lightweight fine-tuning method that simplifies model design and reduces parameters, focusing on optimizing Visual-Language Models (VLMs) for edge deployment. As VLMs evolve, the parameter size becomes increasingly challenging for edge devices. To overcome this limitation, we combine lightweighting and fine-tuning into a single step. We decompose large linear layers in the vision encoder and introduce smaller matrices in parallel, creating a new path. During fine tuning, performance is improved by reducing the matrix size and increasing the depth, gradually phasing out the original path. We deploy the lightened and fine-tuned model on a Jetson TX2 and shows comparable performance compared to VLMs with larger parameters.

222、Skipformer: Evolving Beyond Blocks for Extensively Searching On-Device
   Language Models With Learnable Attention Window
Abtract: Deployment of language models to resource-constrained edge devices is an uphill battle against their ever-increasing size. The task transferability of language models makes deployment to the edge an attractive application. Prior neural architecture search (NAS) works have produced hardware-efficient transformers, but often overlook some architectural features in favor of efficient NAS. We propose a novel evolutionary NAS with large and flexible search space to encourage the exploration of previously unexplored transformer architectures. Our search space allows architectures to vary through their depth and skip connections to transfer information anywhere inside the architecture; Skipformer, the top searched model, displays these novel architectural features. To further increase Skipformer efficiency, we learn a CUDA-accelerated attention window size at each self-attention layer during training. Skipformer achieves 23.3% speed up and requires 19.2% less memory on NVIDIA Jetson Nano with negligible accuracy loss on GLEU benchmark compared to GPT-2 Small.

223、JIGSAW: Edge-based Streaming Perception over Spatially Overlapped
   Multi-Camera Deployments
Abtract: We present JIGSAW, a novel system that performs edge-based streaming perception over multiple video streams, while additionally factoring in the redundancy offered by the spatial overlap often exhibited in urban, multi-camera deployments. To assure high streaming throughput, JIGSAW extracts and spatially multiplexes multiple regions-of-interest from different camera frames into a smaller canvas frame. Moreover, to ensure that perception stays abreast of evolving object kinematics, JIGSAW includes a utility-based weighted scheduler to preferentially prioritize and even skip object-specific tiles extracted from an incoming stream of camera frames. Using the CityflowV2 traffic surveillance dataset, we show that JIGSAW can simultaneously process 25 cameras on a single Jetson TX2 with a 66.6% increase in accuracy and a simultaneous 18x (1800%) gain in cumulative throughput (475 FPS), far outperforming competitive baselines.

224、Efficient Oil Tank Detection Using Deep Learning: A Novel Dataset and
   Deployment on Edge Devices
Abtract: This paper investigates the usage of advanced deep learning (DL) approaches in detecting oil storage tanks, which are widely used in the energy sector. It focuses on the evaluation of the latest emerging YOLOv7 and YOLOv8 object detection models and assesses their performance when deployed on embedded edge devices, marking the first deployment of its kind in this field. A comprehensive dataset of 12,948 images and 171,809 annotations is created, incorporating high-resolution Google Earth captures and images from public datasets, including Satellite pour l'Observation de la Terre (SPOT) images. This represents the most extensive collection for this application. The analysis covers YOLOv8 variants from nano to extra-large, and YOLOv7 large, standard, and tiny models. Findings reveal that the YOLOv7 standard model surpasses the YOLOv8 extra-large variant, achieving a precision of 92.41% and average precision (AP) of 86.63% at an intersection over union (IoU) of 50%, indicating a balanced precision-recall trade-off ideal for detecting oil tanks. The models consistently deliver accurate detections even in the presence of significant variations in environmental conditions and object presentation, reinforcing their reliability for comprehensive and adaptable oil tank detection across varied operational scenarios. Additionally, the study examines the models' effectiveness on high-end GPUs and embedded devices, such as the NVIDIA Jetson Nano and Xavier NX. The latter demonstrates adaptability with up to 16.55 frames per second (FPS) using YOLOv8 nano, indicating efficient real-time monitoring capabilities. This comparison of YOLOv7 and YOLOv8 models advances object detection technology in the energy sector, providing valuable insights for enhancing infrastructure monitoring and risk assessment with a focus on accuracy, efficiency, and flexibility.

225、Energy-Efficient Uncertainty-Aware Biomass Composition Prediction at the
   Edge
Abtract: Clover fixates nitrogen from the atmosphere to the ground, making grass-clover mixtures highly desirable to reduce external nitrogen fertilization. Herbage containing clover additionally promotes higher food intake, resulting in higher milk production. Herbage probing however remains largely unused as it requires a time-intensive manual laboratory analysis. Without this information, farmers are unable to perform localized clover sowing or take targeted fertilization decisions. Deep learning algorithms have been proposed with the goal to estimate the dry biomass composition from images of the grass directly in the fields. The energy-intensive nature of deep learning however limits deployment to practical edge devices such as smartphones. This paper proposes to fill this gap by applying filter pruning to reduce the energy requirement of existing deep learning solutions. We report that although pruned networks are accurate on controlled, high-quality images of the grass, they struggle to generalize to real-world smartphone images that are blurry or taken from challenging angles. We address this challenge by training filter-pruned models using a variance attenuation loss so they can predict the uncertainty of their predictions. When the uncertainty exceeds a threshold, we re-infer using a more accurate unpruned model. This hybrid approach allows us to reduce energy-consumption while retaining a high accuracy. We evaluate our algorithm on two datasets: the GrassClover and the Irish clover using an NVIDIA Jetson Nano edge device. We find that we reduce energy reduction with respect to state-of-the-art solutions by 50% on average with only 4% accuracy loss.

226、A Fast Target Detection Model for Remote Sensing Images Leveraging
   Roofline Analysis on Edge Computing Devices
Abtract: Deploying image target detection algorithms on embedded devices is critical. Previous studies assumed that fewer model parameters and computations improved the inference speed. However, many models with few parameters and computations have slow inference speeds. Therefore, developing a remote sensing image target detection model that can perform real-time inference on embedded devices is required. We propose a fast target detection model for remote sensing images leveraging roofline analysis on edge computing devices (FTD-RLE). It comprises three parts: (1) We analyze the hardware characteristics of embedded devices using RoofLine and incorporate global features to design a model structure based on the operational intensity (OI) and arithmetic intensity (AI) of embedded devices. (2) The mirror ring convolution (MRC) is designed for extracting global features. The global information-aware module (GIAM) extracts local features from key areas using the global feature guidance model. The global-local feature pyramid module (GLFPM) is proposed to combine global and local features. (3) Additionally, hardware deployment and inference acceleration technologies are implemented to enable the model's deployment on edge devices. TensorRT and quantization methods are used to ensure fast inference speed. The proposed algorithm achieves an average detection accuracy of 92.3% on the VHR-10 dataset and 95.2% on the RSOD dataset. It has 1.26 M model parameters, and the inference time for processing one image on Jetson Orin Nx is 8.43ms, which is 1.90 ms and 1.98 ms faster than the mainstream lightweight algorithms ShufflenetV2 and GhostNet, respectively.

227、A low-power biomimetic collision detector based on an in-memory
   molybdenum disulfide photodetector
Abtract: Accurately detecting a potential collision and triggering a timely escape response is critical in the field of robotics and autonomous vehicle safety. The lobula giant movement detector (LGMD) neuron in locusts can detect an approaching object and prevent collisions within a swarm of millions of locusts. This single neuronal cell performs nonlinear mathematical operations on visual stimuli to elicit an escape response with minimal energy expenditure. Collision avoidance models based on image processing algorithms have been implemented using analogue very-large-scale-integration designs, but none is as efficient as this neuron in terms of energy consumption or size. Here we report a nanoscale collision detector that mimics the escape response of the LGMD neuron. The detector comprises a monolayer molybdenum disulfide photodetector stacked on top of a non-volatile and programmable floating-gate memory architecture. It consumes a small amount of energy (in the range of nanojoules) and has a small device footprint (similar to 1 mu m x 5 mu m).
   By integrating a MoS(2)photodetector with a floating-gate memory device, a nanoscale collision detector can be created that mimics the escape response of the lobula giant movement detector neuron.

228、Reactive direction control for a mobile robot: a locust-like control of
   escape direction emerges when a bilateral pair of model locust visual
   neurons are integrated
Abtract: Locusts possess a bilateral pair of uniquely identifiable visual neurons that respond vigorously to the image of an approaching object. These neurons are called the lobula giant movement detectors (LGMDs). The locust LGMDs have been extensively studied and this has lead to the development of an LGMD model for use as an artificial collision detector in robotic applications. To date, robots have been equipped with only a single, central artificial LGMD sensor, and this triggers a non-directional stop or rotation when a potentially colliding object is detected. Clearly, for a robot to behave autonomously, it must react differently to stimuli approaching from different directions. In this study, we implement a bilateral pair of LGMD models in Khepera robots equipped with normal and panoramic cameras. We integrate the responses of these LGMD models using methodologies inspired by research on escape direction control in cockroaches. Using 'randomised winner-take-all' or 'steering wheel' algorithms for LGMD model integration, the Khepera robots could escape an approaching threat in real time and with a similar distribution of escape directions as real locusts. We also found that by optimising these algorithms, we could use them to integrate the left and right DCMD responses of real jumping locusts offline and reproduce the actual escape directions that the locusts took in a particular trial. Our results significantly advance the development of an artificial collision detection and evasion system based on the locust LGMD by allowing it reactive control over robot behaviour. The success of this approach may also indicate some important areas to be pursued in future biological research.

229、Highly Efficient Back-End-of-Line Compatible Flexible Si-Based Optical
   Memristive Crossbar Array for Edge Neuromorphic Physiological Signal
   Processing and Bionic Machine Vision
Abtract: A novel optoelectronic synapse compatible with existing chip technology is demonstrated. This device excels in mimicking memory and learning functions using light, making it ideal for future neuromorphic computing in biomedicine.Our design surpasses previous models with its ability to switch between short-term and long-term memory states using light pulses.Experiments on real-world biomedical data (electroencephalogram, electromyography, electrocardiogram) showed significant improvement in classification accuracy. This highlights the device's potential for advanced physiological signal processing and wearable health monitoring systems.
   The emergence of the Internet-of-Things is anticipated to create a vast market for what are known as smart edge devices, opening numerous opportunities across countless domains, including personalized healthcare and advanced robotics. Leveraging 3D integration, edge devices can achieve unprecedented miniaturization while simultaneously boosting processing power and minimizing energy consumption. Here, we demonstrate a back-end-of-line compatible optoelectronic synapse with a transfer learning method on health care applications, including electroencephalogram (EEG)-based seizure prediction, electromyography (EMG)-based gesture recognition, and electrocardiogram (ECG)-based arrhythmia detection. With experiments on three biomedical datasets, we observe the classification accuracy improvement for the pretrained model with 2.93% on EEG, 4.90% on ECG, and 7.92% on EMG, respectively. The optical programming property of the device enables an ultra-low power (2.8 x 10-13 J) fine-tuning process and offers solutions for patient-specific issues in edge computing scenarios. Moreover, the device exhibits impressive light-sensitive characteristics that enable a range of light-triggered synaptic functions, making it promising for neuromorphic vision application. To display the benefits of these intricate synaptic properties, a 5 x 5 optoelectronic synapse array is developed, effectively simulating human visual perception and memory functions. The proposed flexible optoelectronic synapse holds immense potential for advancing the fields of neuromorphic physiological signal processing and artificial visual systems in wearable applications.

230、Nano-Micron Combined Hydrogel Microspheres: Novel Answer for Minimal
   Invasive Biomedical Applications
Abtract: Hydrogels, key in biomedical research for their hydrophilicity and versatility, have evolved with hydrogel microspheres (HMs) of micron-scale dimensions, enhancing their role in minimally invasive therapeutic delivery, tissue repair, and regeneration. The recent emergence of nanomaterials has ushered in a revolutionary transformation in the biomedical field, which demonstrates tremendous potential in targeted therapies, biological imaging, and disease diagnostics. Consequently, the integration of advanced nanotechnology promises to trigger a new revolution in the realm of hydrogels. HMs loaded with nanomaterials combine the advantages of both hydrogels and nanomaterials, which enables multifaceted functionalities such as efficient drug delivery, sustained release, targeted therapy, biological lubrication, biochemical detection, medical imaging, biosensing monitoring, and micro-robotics. Here, this review comprehensively expounds upon commonly used nanomaterials and their classifications. Then, it provides comprehensive insights into the raw materials and preparation methods of HMs. Besides, the common strategies employed to achieve nano-micron combinations are summarized, and the latest applications of these advanced nano-micron combined HMs in the biomedical field are elucidated. Finally, valuable insights into the future design and development of nano-micron combined HMs are provided.
   Dive into the cutting-edge world of biomedical research! Explore the evolution of hydrogel microspheres (HMs) and their fusion with nanomaterials, promising a revolutionary shift in healthcare. Uncover the potential of multifaceted functionalities-from targeted therapy to micro-robotics. Join in this comprehensive journey through nanomaterials, HMs preparation, and future applications. The future of biomedical innovation is now! image

231、HaptiTemp: A Next-Generation Thermosensitive GelSight-Like Visuotactile
   Sensor
Abtract: This study describes the creation of a new type of compact skin-like silicone-based thermosensitive visuotactile sensor based on GelSight technology. The easy integration of this novel sensor into a complex visuotactile system capable of very rapid detection of temperature change (30 degrees C/s) is unique in providing a system that parallels the withdrawal reflex of the human autonomic system to extreme heat. To the best of authors' awareness, this is the first time a sensor that can trigger a sensory impulse like a withdrawal reflex of humans in robotics community. To attain this, we used thermochromic pigments color blue, orange, and black with a threshold of 31 degrees C, 43 degrees C, and 50 degrees C, respectively on the gel material. Each pigment has the property of becoming translucent when its temperature threshold is reached, making it possible to stack thermochromic pigments of different colors and thresholds. The pigments were air-brushed on a low-cost commercially available transparent silicone sponge. We used MobileNetV2 and transfer learning to simulate tactile preprocessing in order to recognize five different objects. The new thermosensitive visuotactile sensor helped to achieve 97.3% tactile image classification accuracy of five different objects. Our novel thermosensitive visuotactile sensor could be of benefit in material texture analysis, telerobotics, space exploration, and medical applications.

232、The future of plant based green carbon dots as cancer Nanomedicine: From
   current progress to future Perspectives and beyond
Abtract: Background: The emergence of carbon dots (CDs) as anticancer agents had sparked a transformation in cancer research and treatment strategies. These fluorescent CDs, initially introduced in the early 2000 s, possess exceptional biocompatibility, tunable fluorescence, and surface modification capabilities, positioning them as promising tools in biomedical applications. Aim of Review: The review encapsulates the transformative trajectory of green CDs as future anticancer nanomedicine, poised to redefine the strategies employed in the ongoing fight against cancer. Key Scientific Concepts of Review: The versatility of CDs was rooted in their various synthesis approaches and sustainable strategies, enabling their adaptability for diverse therapeutic uses. In vitro studies had showcased CDs' selective cytotoxicity against cancer cells while sparing healthy counterparts, forming the basis for targeted therapeutic potential. This selectivity had been attributed to the reactive oxygen species (ROS) generation, which opened avenues for targeted interventions. The role of CDs in combination therapies, synergizing with chemotherapy, radiotherapy, and targeted approaches was then investigated to heighten their anticancer efficacy. Notably, in vivo studies highlight CDs' remarkable biocompatibility and minimal side effects, endorsing their translational promise. Integration with conventional cancer treatments such as chemotherapy, radiotherapy, and immunotherapy amplified the versatility and effectiveness of CDs. The exploration of CDs' applications in photo-induced treatments further solidified their significance, positioning them as photosensitizers (PS) in photodynamic therapy (PDT) and photothermal agents (PA) in photothermal therapy (PTT). In PDT, CDs triggered the generation of ROS upon light exposure, facilitating cancer cell elimination, while in PTT, they induced localized hyperthermia within cancer cells, enhancing therapeutic outcomes. In vitro and in vivo investigations validated CDs' efficacy in PDT and PTT, affirming their potential for integration into combination therapies. Looking ahead, the future of CDs in anticancer treatment encompasses bioavailability, biocompatibility, synergistic treatments, tumor targeting, artificial intelligence (AI) and robotics integration, personalized medicine, and clinical translation. This transformative odyssey of CDs as future anticancer agents is poised to redefine the paradigm of cancer treatment strategies. (c) 2023 The Authors. Published by Elsevier B.V. on behalf of Cairo University. This is an open access article under the CC BY-NC-ND license (http://creativecommons.org/licenses/by-nc-nd/4.0/).

233、A dual-mode automatic switching feature points matching algorithm fusing
   IMU data
Abtract: Feature point extraction and matching work together and play a crucial role in many machine vision tasks, such as visual simultaneous localization and mapping (SLAM) which is the basis of robotics. However, feature matching has not been fully studied as much as feature extraction. The most widely used feature matching method is still the classical brute-force matching. In this paper, by fusing the data of inertial measurement unit (IMU), a novel feature points matching algorithm with online learning fault detection, where the global matcher and local matcher operate alternately and switch automatically, is proposed to improve matching accuracy. Taking the uncertainties of the initial states of camera and IMU into account in the initialization phase, the system starts with the global mode to accomplish the matching task and initialize the state variables which are used to align the data of two sensors. Then, once completing the initialization, the algorithm will automatically switch to our new-designed local matcher with feature compensation, which bounds the search space and improves the matching accuracy via performing on the support region according to the results of IMU pre-integration rather than the whole image. In addition, for the purpose of effectively dealing with the misestimation of support region induced by IMU failures, an online learning fault detector based on support vector machines is newly developed, which can trigger the action of mode switcher and runs simultaneously in another thread. Furthermore, a timer is attached to the fault detector in order to activate the detection periodically, which can further reduces the cost of computing resources. Finally, the performance of the present algorithm is validated on public datasets.

234、Endowing Robots with Longer-term Autonomy by Recovering from External
   Disturbances in Manipulation Through Grounded Anomaly Classification and
   Recovery Policies
Abtract: Robots are poised to interact with humans in unstructured environments. Despite increasingly robust control algorithms, failure modes arise whenever the underlying dynamics are poorly modeled, especially in unstructured environments. We contribute a set of recovery policies to deal with anomalies produced by external disturbances. The recoveries work when various different types of anomalies are triggered any number of times at any point in the task, including during already running recoveries. Our recovery critic stands atop of a tightly-integrated, graph-based online motion-generation and introspection system. Policies, skills, and introspection models are learned incrementally and contextually over time. Recoveries are studied via a collaborative kitting task where a wide range of anomalous conditions are experienced in the system. We also contribute an extensive analysis of the performance of the tightly integrated anomaly identification, classification, and recovery system under extreme anomalous conditions. We show how the integration of such a system achieves performances greater than the sum of its parts.

235、BMIVPOT, a Fully Automated Version of the Intravenous Pole: Simulation,
   Design, and Evaluation
Abtract: Robotic intravenous poles are automated supportive instrument that needs to be triggered by patients to hold medications and needed supplies. Healthcare engineering of robotic intravenous poles is advancing in order to improve the quality of health services to patients worldwide. Existing intravenous poles in the market were supportive to patients, yet they constrained their movement, consumed the time of both the patient and the nurse, and they were expensive in regard to what they offer. Although robotic poles overcame some of the movement limitations of the commercial/market poles, they were partially automated and did not offer additional technological features. The aim of our work was to develop a fully automated Biomedical Intravenous Pole Robot (BMIVPOT) to resolve the aforementioned limitations and to offer new technological features to intravenous poles, thereby promoting the health services. Several sensors and build-up materials were empirically chosen to be cost-effective and fulfill our needs. The new prototype was divided into three steps: simulated prototype, real implementation of the prototype, and testing and evaluation. Simulation results showed the best qualitative way to fit all the specifications in the robotic system, such as the shape, sensors, and connections in order to provide the proper functionality of the system. Experimental and real results provided the manufactured parts, implemented sensors, and the final robot. Testing the tracking and the flow sensor performances were provided. Evaluation of our Biomedical Intravenous Pole Robot with alternatives showed that our robot outperforms the other poles in many aspects including the features it offers, the percentage of interventions it comprised, the reliability, and cost-effectiveness. The overall percentage of features offered by our Biomedical Intravenous Pole Robot was 60% higher than that offered by peer research poles and 80% higher than that of the market poles. In addition, the average percentage of integration of interventions (architecture, sensor, wireless, tracking, and mechanical) in the Biomedical Intravenous Pole Robot was at least 56% higher than that of the alternative poles. According to the results, Biomedical Intravenous Pole Robot offers a cost-effective price as compared to the others. As a future prospect, we intend to add more features to this prototype in order to enhance it, such as vital signs detection, and improve the tracking system.


237、Instantaneous Tiltmeter Triggered by Dynamic Wetting Behavior
Abtract: A novel instantaneous tiltmeter with dynamic and static monitoring functions is reported that is based on liquid metal dynamic wetting behavior in a bio-fabricated anisotropic microchannel. The proposed system achieves instantaneous tiltmeter functionality, offering a broad detection range (-90 degrees-90 degrees) with high precision (0.05 degrees), a rapid reaction time (0.11 s), and enhanced durability. Moreover, a seamless integration has enabled water wave detection, language programming, and human limb monitoring. Especially, the integration of tiltmeter and a t3D motion platform results in a surface structure scanning system capable of effectively performing large area (>200 cm(2)) and height difference scanning functions. This innovative approach holds great potential for transformative changes in the fields of advanced manufacturing, flexible robotics, and the flexible sensing, further facilitating widespread adoption.

238、Optimizing Data Capture Through Object Recognition for Efficient Sensor
   and Camera Management with a Quadruped Robot
Abtract: This paper explores the integration of object recognition to advance autonomous mobile robotics, with a specific focus on employing sensor fusion in quadrupedal swarm units, exemplified by the Unitree GOl robotic dog. The proposed system utilizes an RGB camera and the YOLOv8 machine learning module to enable the robotic dog to identify objects, subsequently triggering predefined actions or activating additional hyper-spectral sensors and cameras. This approach enhances the robot's environmental interaction, allowing it to optimize power efficiency by activating sensors selectively, resulting in a more focused dataset. The technology's applications span various domains, including threat detection in home defense, surveillance of civic structures, and monitoring industrial components. The paper delves into the customization potential through the creation of bespoke datasets and model training, incorporating techniques such as transfer learning and domain adaptation to tailor the system to user-specific requirements. Beyond its immediate applications, the implications of object recognition and AI-assisted robotics extend to diverse scientific communities, offering a versatile tool for a wide array of applications.

239、Event-Triggered Robust Adaptive Fault-Tolerant Tracking and Vibration
   Control for the Rigid-Flexible Coupled Robotic Mechanisms With Large
   Beam-Deformations
Abtract: A detailed modeling approach that utilizes the virtual work idea is developed for modeling the dynamical formulas of the rigid-flexible coupled robotic mechanisms (RFCRMs) with large beam-deformations across the horizontal plane. To follow the required angular positions of RFCRMs, a virtual robust linear quadratic state feedback (RLQSF) input is constructed using the converted full-actuated model in conjunction with an event-triggered robust adaptive fault-tolerant control (ETRAFTC) approach. The integration of virtual input and the proposed RLQSF law design enables simultaneous angular tracking and vibration elimination. To make up for the defective actuators with part loss of efficacy and evaluate the unknown fault parameters, an adaptive estimation law with a projection mapping operator is adopted. With the help of the Lyapunov direct approach, the angular position tracking errors and the flexible vibration of RFCRMs are demonstrated to converge to a tiny confined compact set with fewer communications. At last, the performance of the designed ETRAFTC is presented via three numerical scenarios.

240、Real-Time Mobile Robot Obstacles Detection and Avoidance Through EEG
   Signals
Abtract: Background/Objectives: The study explores the integration of human feedback into the control loop of mobile robots for real-time obstacle detection and avoidance using EEG brain-computer interface (BCI) methods. The goal is to assess the possible paradigms applicable to the most current navigation system to enhance safety and interaction between humans and robots. Methods: The research explores passive and active brain-computer interface (BCI) technologies to enhance a wheelchair-mobile robot's navigation. In the passive approach, error-related potentials (ErrPs), neural signals triggered when users comment or perceive errors, enable automatic correction of the robot navigation mistakes without direct input or command from the user. In contrast, the active approach leverages steady-state visually evoked potentials (SSVEPs), where users focus on flickering stimuli to control the robot's movements directly. This study evaluates both paradigms to determine the most effective method for integrating human feedback into assistive robotic navigation. This study involves experimental setups where participants control a robot through a simulated environment, and their brain signals are recorded and analyzed to measure the system's responsiveness and the user's mental workload. Results: The results show that a passive BCI requires lower mental effort but suffers from lower engagement, with a classification accuracy of 72.9%, whereas an active BCI demands more cognitive effort but achieves 84.9% accuracy. Despite this, task achievement accuracy is higher in the passive method (e.g., 71% vs. 43% for subject S2) as a single correct ErrP classification enables autonomous obstacle avoidance, whereas SSVEP requires multiple accurate commands. Conclusions: This research highlights the trade-offs between accuracy, mental load, and engagement in BCI-based robot control. The findings support the development of more intuitive assistive robotics, particularly for disabled and elderly users.

241、Brain tumor segmentation and detection in MRI using convolutional neural
   networks and VGG16
Abtract: Background In this research, we explore the application of Convolutional Neural Networks (CNNs) for the development of an automated cancer detection system, particularly for MRI images. By leveraging deep learning and image processing techniques, we aim to build a system that can accurately detect and classify tumors in medical images. The system's performance depends on several stages, including image enhancement, segmentation, data augmentation, feature extraction, and classification. Through these stages, CNNs can be effectively trained to detect tumors in MRI images with high accuracy. This automated cancer detection system can assist healthcare professionals in diagnosing cancer more quickly and accurately, improving patient outcomes. The integration of deep learning and image processing in medical diagnostics has the potential to revolutionize healthcare, making it more efficient and accessible.Methods In this paper, we examine the failure of semantic segmentation by predicting the mean intersection over union (mIoU), which is a standard evaluation metric for segmentation tasks. mIoU calculates the overlap between the predicted segmentation map and the ground truth segmentation map, offering a way to evaluate the model's performance. A low mIoU indicates poor segmentation, suggesting that the model has failed to accurately classify parts of the image. To further improve the robustness of the system, we introduce a deep neural network capable of predicting the mIoU of a segmentation map. The key innovation here is the ability to predict the mIoU without needing access to ground truth data during testing. This allows the system to estimate how well the model is performing on a given image and detect potential failure cases early in the process. The proposed method not only predicts the mIoU but also uses the expected mIoU value to detect failure events. For instance, if the predicted mIoU falls below a certain threshold, the system can flag this as a potential failure, prompting further investigation or triggering a safety mechanism in the autonomous vehicle. This mechanism can prevent the vehicle from making decisions based on faulty segmentation, improving safety and performance. Furthermore, the system is designed to handle imbalanced data, which is a common challenge in training deep learning models. In autonomous driving, certain objects, such as pedestrians or cyclists, might appear much less frequently than other objects like vehicles or roads. The imbalance can cause the model to be biased toward the more frequent objects. By leveraging the expected mIoU, the method can effectively balance the influence of different object classes, ensuring that the model does not overlook critical elements in the scene. This approach offers a novel way of not only training the model to be more accurate but also incorporating failure prediction as an additional layer of safety. It is a significant step forward in ensuring that autonomous systems, especially self-driving cars, operate in a safe and reliable manner, minimizing the risk of accidents caused by misinterpretations of visual data. In summary, this research introduces a deep learning model that predicts segmentation performance and detects failure events by using the mIoU metric. By improving both the accuracy of semantic segmentation and the detection of failures, we contribute to the development of more reliable autonomous driving systems.
   Moreover, the technique can be extended to other domains where segmentation plays a critical role, such as medical imaging or robotics, enhancing their ability to function safely and effectively in complex environments.Results and Discussion Brain tumor detection from MRI images is a critical task in medical image analysis that can significantly impact patient outcomes. By leveraging a hybrid approach that combines traditional image processing techniques with modern deep learning methods, this research aims to create an automated system that can segment and identify brain tumors with high accuracy and efficiency. Deep learning techniques, particularly CNNs, have proven to be highly effective in medical image analysis due to their ability to learn complex features from raw image data. The use of deep learning for automated brain tumor segmentation provides several benefits, including faster processing times, higher accuracy, and more consistent results compared to traditional manual methods. As a result, this research not only contributes to the development of advanced methods for brain tumor detection but also demonstrates the potential of deep learning in revolutionizing medical image analysis and assisting healthcare professionals in diagnosing and treating brain tumors more effectively.Conclusion In conclusion, this research demonstrates the potential of deep learning techniques, particularly CNNs, in automating the process of brain tumor detection from MRI images. By combining traditional image processing methods with deep learning, we have developed an automated system that can quickly and accurately segment tumors from MRI scans. This system has the potential to assist healthcare professionals in diagnosing and treating brain tumors more efficiently, ultimately improving patient outcomes. As deep learning continues to evolve, we expect these systems to become even more accurate, robust, and widely applicable in clinical settings. The use of deep learning for brain tumor detection represents a significant step forward in medical image analysis, and its integration into clinical workflows could greatly enhance the speed and accuracy of diagnosis, ultimately saving lives. The suggested plan also includes a convolutional neural network-based classification technique to improve accuracy and save computation time. Additionally, the categorization findings manifest as images depicting either a healthy brain or one that is cancerous. CNN, a form of deep learning, employs a number of feed-forward layers. Additionally, it functions using Python. The Image Net database groups the images. The database has already undergone training and preparation. Therefore, we have completed the final training layer. Along with depth, width, and height feature information, CNN also extracts raw pixel values.We then use the Gradient decent-based loss function to achieve a high degree of precision. We can determine the training accuracy, validation accuracy, and validation loss separately. 98.5% of the training is accurate. Similarly, both validation accuracy and validation loss are high.

242、Robotic Integration of Massage and Real-Time Tissue Assessment: A
   Technological Framework
Abtract: Compared with traditional soft tissue assessment devices, the massage robot equipped with a stiffness assessment system that leverages integrated sensors, enables real-time monitoring of soft tissue conditions during massage, thus integrates detection and treatment seamlessly. In this study, we investigate the foundation and applications of the technique for utilizing a massage robot for soft tissue assessment. Specifically, we investigate the technique for stiffness evaluation using finger pressing actions, analogous to the indentation test. Furthermore, machine learning algorithms are employed to classify materials with varying stiffness levels during finger rubbing actions. The results demonstrate the technical superiority of the system in soft tissue assessment and its promising potential for future clinical applications.

243、Biomimetic Plant-Root-Inspired Robotic Sensor System
AB There are many examples in nature in which the ability to detect is combined with decision-making, such as the basic survival instinct of plants and animals to search for food. We can technically translate this innate function via the use of robotics with integrated sensors and artificial intelligence. However, the integration of sensing capabilities into robotics has traditionally been neglected due to the significant associated technical challenges. Inspired by plant-root chemotropism, we present a miniaturized electrochemical array integrated into a robotic tip, embedding a customized micro-potentiometer. The system contains solid-state sensors fitted to the tip of the robotic root to three-dimensionally monitor potassium and pH changes in a moist, soil-like environment, providing an integrated electronic readout. The sensors measure a range of parameters compatible with realistic soil conditions. The sensors' response can trigger the movement of the robotic root with a control algorithm inspired by the behavior of the plant root that determines the optimal path toward root growth, simulating the decision-making process of a plant. This nature-inspired technology may lead, in the future, to the realization of robotic devices with the potential for monitoring and exploring the soil autonomously.

244、Data Collection, Heat Map Generation for Crack Detection Using Robotic
   Dog Fused with FLIR Sensor
Abtract: The imperative need to monitor critical infrastructure, encompassing power plant transformers and essential civil structures, is paramount for ensuring their sustained longevity and safety. A notable progression in this domain arises from the widespread availability of Forward -Looking Infrared (FLIR) cameras, made possible by reductions in manufacturing costs. These FLIR cameras, distinguished by their remarkable precision in temperature data collection and visualization, hold significant potential across diverse fields. This paper underscores the pioneering application of FLIR cameras in infrastructure monitoring, with a specific focus on power transformers and aging civil structures like bridges. In the realm of transformers, the FLIR camera exhibits the capability to delve into and record internal temperatures, facilitating the early identification of deviations from standard operational temperature ranges. Detection of such anomalies serves as a trigger for preemptive measures, thereby minimizing operational disruptions. Similarly, for structures such as bridges, routine FLIR analyses prove instrumental in discerning temperature variations that may indicate potential wear or fractures. This timely identification allows for proactive maintenance, ensuring structural integrity and averting potential hazards. To augment the safety and efficiency of data collection, this research advocates for the integration of FLIR cameras with mobile robots, exemplified by the Unitree GoAirl. This strategic amalgamation enables precise yet remote surveillance, mitigating risks to operators while maximizing the accuracy of infrastructure health assessments. The proposed approach not only advances the capabilities of FLIR technology but also heralds a paradigm shift in the realm of infrastructure monitoring, promising enhanced reliability and safety standards.

245、Inspection Robot and Wall Surface Detection Method for Coal Mine Wind Shaft
Abstract: The coal mine wind shaft is an important ventilation channel in coal mines, and it is of great significance to ensure its long-term safety. At present, the inspection of wind shafts still depends on manual work, which has low reliability and high risk. There are two main problems in the shaft wall detection of ventilation shafts: (1) The humidity and dust concentration in ventilation shafts are high, which makes imaging difficult; (2) the cracks on the shaft wall are long and irregular, so it is impossible to acquire the information of the whole crack from a single photo. Firstly, the mapping analysis between the concentration of water vapor and dust in the wind shaft and the image definition is determined by experiments. Then, the inspection robot is designed to move along the axial and circumferential directions to get close to the shaft wall, and the rack-and-rail drive design is adopted to ensure the real-time position feedback of the robot. Then, through the crack parameter detection method based on depth learning, the movement direction of the robot is controlled according to the crack direction so as to ensure that the complete crack parameters are obtained. Finally, the crack detection algorithm is verified by experiments.

246、 ROBOT SYSTEM FOR PAVEMENT CRACK INSPECTION BASED CNN MODEL
Abstract：Maintaining the excellent state of the road is critical to secure driving and is an obligation of both transportation and regulatory maintenance authorities. For a safe driving environment, it is essential to inspect road surfaces for defects or degradation frequently. This process is found to be labor-intensive and necessitates primary expertise. Therefore, it is challenging to examine road cracks visually; thus, we must effectively employ computer visualization and robotics tools to support this mission. This research provides our initial idea of simulating an Autonomous Robot System (ARS) to perform pavement assessments. The ARS for crack inspection is a camera-equipped mobile robot (i.e., an Android phone) to collect images on the road. The proposed system is simulated using an mBot robot armed with an Android phone that gathers video streams to be processed on a server that has a pre-training Convolutional Neural Networks (CNN) that can recognize crack existence. The proposed CNN model attained 99.0% accuracy in the training case and 97.5% in the testing case. The results of this research are suitable for application with a commercial mobile robot as an autonomous platform for pavement inspections. 


248、Development of AI- and Robotics-Assisted Automated Pavement-Crack-Evaluation System 
Abstract: Crack inspection is important to monitor the structural health of pavement structures and make the repair process easier. Currently, pavement crack inspection is conducted manually, which is inefficient and costly at the same time. To solve the problem, this work has developed a robotic system for automated data collection and analysis in real-time. The robotic system navigates the pavement and collects visual images from the surface. A deep-learning-based semantic segmentation framework named RCDNet was proposed. The RCDNet was implemented on the onboard computer of the robot to identify cracks from the visual images. The encoder-decoder architecture was utilized as the base framework of the proposed RCDNet. The RCDNet comprises a dual-channel encoder and a decoder module. The encoder and decoder parts contain a context-embedded channel attention (CECA) module and a global attention module (GAM), respectively. Simulation results show that the deep learning model obtained 96.29% accuracy for predicting the images. The proposed robotic system was tested in both indoor and outdoor environments. The robot was observed to complete the inspection of a 3 m × 2 m grid within 10 min and a 2.5 m × 1 m grid within 6 min. This outcome shows that the proposed robotic method can drastically reduce the time of manual inspection. Furthermore,a severity map was generated using the visual image results. This map highlights areas that require greater attention for repair in the test grid.


250、Robots in Inspection and Monitoring of Buildings and Infrastructure: A Systematic Review
Abstract: Regular inspection and monitoring of buildings and infrastructure, that is collectively called the built environment in this paper, is critical. The built environment includes commercial and residential buildings, roads, bridges, tunnels, and pipelines. Automation and robotics can aid
in reducing errors and increasing the efficiency of inspection tasks. As a result, robotic inspection and monitoring of the built environment has become a significant research topic in recent years.This review paper presents an in-depth qualitative content analysis of 269 papers on the use of
robots for the inspection and monitoring of buildings and infrastructure. The review found nine different types of robotic systems, with unmanned aerial vehicles (UAVs) being the most common,followed by unmanned ground vehicles (UGVs). The study also found five different applications of
robots in inspection and monitoring, namely, maintenance inspection, construction quality inspection,construction progress monitoring, as-built modeling, and safety inspection. Common research areas investigated by researchers include autonomous navigation, knowledge extraction, motion
control systems, sensing, multi-robot collaboration, safety implications, and data transmission. The findings of this study provide insight into the recent research and developments in the field of robotic inspection and monitoring of the built environment and will benefit researchers, and construction and facility managers, in developing and implementing new robotic solutions.

251、ROADS—Rover for Bituminous Pavement Distress Survey: An Unmanned Ground Vehicle (UGV) Prototype for Pavement Distress Evaluation
Abstract: Maintenance has a major impact on the financial plan of road managers. To ameliorate road conditions and reduce safety constraints, distress evaluation methods should be efficient and should avoid being time consuming. That is why road cadastral catalogs should be updated periodically,
and interventions should be provided for specific management plans. This paper focuses on the setting of an Unmanned Ground Vehicle (UGV) for road pavement distress monitoring, and the Rover for bituminOus pAvement Distress Survey (ROADS) prototype is presented in this paper. ROADS has a multisensory platform fixed on it that is able to collect different parameters. Navigation and environment sensors support a two-image acquisition system which is composed of a high-resolution digital camera and a multispectral imaging sensor. The Pavement Condition Index (PCI) and the Image Distress Quantity (IDQ) are, respectively, calculated by field activities and image computation.The model used to calculate the IROADS index from PCI had an accuracy of 74.2%. Such results show that the retrieval of PCI from image-based approach is achievable and values can be categorized as “Good”/“Preventive Maintenance”, “Fair”/“Rehabilitation”, “Poor”/“Reconstruction”, which are ranges of the custom PCI ranting scale and represents a typical repair strategy.

252、Concept of an autonomous mobile robotic system for bridge inspection
ABSTRACT：In the next decade, many old bridges will be exposed to increasing traffic loads and destructive environmental conditions. Measurement methods like laser scanning, infrared thermography, photogrammetry, ground penetrating radar, or ultrasonic scanning are used on single robotic systems to partially support the inspectors.However, time-consuming manual inspections for crack detection, measurement, and documentation are still
necessary. This paper describes the concept of an autonomous mobile robotic bridge inspection system. The proposed concept for an unmanned ground vehicle (UGV) is achieved by a trade-off of different mobile platforms, sensor systems for mapping, localization and inspection, and first tests assessing the feasibility. We use a small concrete bridge in Freiburg (Germany) with various cracks for testing the sensors, the UGV concept,
and initial tests of the mobile platform. This results in the choice of selecting the weatherproof version of the mobile robotic platform Husky from Clearpath Robotics. It is equipped with Swift Navigation’s Duro real-time kinematic (RTK) system, a heading system, an inertial measurement unit (IMU), a base station, and software for semi-autonomous navigation. In the next step, we compare different sensor systems. For mapping and localization, we decide to use the 360◦ spherical camera Ladybug 5+ from FLIR Systems and a Velodyne VLP-16 light detection and ranging (LiDAR). High-resolution cameras allow recording damages on the bridge’s surface. We perform first tests using monochrome and colour cameras. After evaluating different sensor integration concepts,we present a preliminary design of the UGV including integrated sensors.

253、Automatic Crack Detection on Road Pavements Using Encoder-Decoder Architecture
Abstract: Automatic crack detection from images is an important task that is adopted to ensure road safety and durability for Portland cement concrete (PCC) and asphalt concrete (AC) pavement.Pavement failure depends on a number of causes including water intrusion, stress from heavy loads,
and all the climate effects. Generally, cracks are the first distress that arises on road surfaces and proper monitoring and maintenance to prevent cracks from spreading or forming is important.Conventional algorithms to identify cracks on road pavements are extremely time-consuming and high cost. Many cracks show complicated topological structures, oil stains, poor continuity, and low contrast, which are difficult for defining crack features. Therefore, the automated crack detection algorithm is a key tool to improve the results. Inspired by the development of deep learning in computer vision and object detection, the proposed algorithm considers an encoder-decoder architecture with hierarchical feature learning and dilated convolution, named U-Hierarchical Dilated Network (U-HDN), to perform crack detection in an end-to-end method. Crack characteristics with multiple context information are automatically able to learn and perform endto-end crack detection. Then, a multi-dilation module embedded in an encoder-decoder architecture is proposed. The crack features of multiple context sizes can be integrated into the multi-dilation module by dilation convolution with different dilatation rates, which can obtain much more cracks information. Finally, the hierarchical feature learning module is designed to obtain a multi-scale
features from the high to low- level convolutional layers, which are integrated to predict pixel-wise crack detection. Some experiments on public crack databases using 118 images were performed and the results were compared with those obtained with other methods on the same images. The results
show that the proposed U-HDN method achieves high performance because it can extract and fuse different context sizes and different levels of feature maps than other algorithms.

254、A lightweight encoder–decoder network for automatic pavement crack detection
Abstract：Cracks are the most common damage type on the pavement surface. Usually,pavement cracks, especially small cracks, are difficult to be accurately identified due to background interference. Accurate and fast automatic road crack detection play a vital role in assessing pavement conditions. Thus, this paper proposes an efficient lightweight encoder–decoder network for automatically detecting pavement cracks at the pixel level. Taking advantage of a novel encoder–decoder architecture integrating a new type of hybrid attention blocks and residual blocks (RBs), the proposed network can achieve an extremely lightweight model with more accurate detection of pavement crack pixels. An image dataset consisting of 789 images of pavement cracks acquired by a self-designed mobile robot is built and utilized to train and evaluate the proposed network. Comprehensive experiments demonstrate that the proposed network performs better than the state-of-the-art methods on the self-built dataset as well as three other public datasets (CamCrack789, Crack500, CFD, and DeepCrack237), achieving F1 scores of 94.94%, 82.95%, 95.74%, and 92.51%, respectively. Additionally, ablation studies validate the effectiveness of integrating the RBs and the proposed hybrid attention mechanisms. By introducing depth-wise separable convolutions, an even more lightweight version of the proposed network is created, which has a comparable performance and achieves the fastest inference speed with a model parameter size of only 0.57 M. The developed mobile robot system can effectively detect pavement cracks in real scenarios at a speed of 25 frames per second.

255、Use of Remote Structural Tap Testing Devices Deployed via Ground Vehicle for Health Monitoring of Transportation Infrastructure
Abstract: Transportation infrastructure is an integral part of the world’s overall functionality; however, current transportation infrastructure has aged since it was first developed and implemented.Consequently, given its condition, preservation has become a main priority for transportation agencies. Billions of dollars annually are required to maintain the United States’ transportation system;however, with limited budgets the prioritization of maintenance and repairs is key. Structural Health Monitoring (SHM) methods can efficiently inform the prioritization of preservation efforts. This paper presents an acoustic monitoring SHM method, deemed tap testing, which is used to detect signs of deterioration in structural/mechanical surfaces through nondestructive means. This method is proposed as a tool to assist bridge inspectors, who already utilize a costly form of SHM methodology when conducting inspections in the field. Challenges arise when it comes to this method of testing, especially when SHM device deployment is done by hand, and when the results are based solely upon a given inspector’s abilities. This type of monitoring solution is also, in general, only available to experts, and is associated with special cases that justify their cost. With the creation of a low-cost, cyber–physical system that interrogates and classifies the mechanical health of given surfaces, we lower the cost of SHM, decrease the challenges faced when conducting such tests, and enable communities with a revolutionary solution that is adaptable to their needs. The authors of this paper created and tested a low-cost, interrogating robot that informs users of structural/mechanical defects. This research describes the further development, validation of, and experimentation with, a tap testing device that utilizes remote technology.

256、Influence of Smart Sensors on Structural Health Monitoring Systems and Future Asset Management Practices
Abstract: Recent developments in networked and smart sensors have significantly changed the way Structural Health Monitoring (SHM) and asset management are being carried out. Since the sensor networks continuously provide real-time data from the structure being monitored, they constitute a more realistic image of the actual status of the structure where the maintenance or repair work can be scheduled based on real requirements. This review is aimed at providing a wealth of knowledge from the working principles of sensors commonly used in SHM, to artificial-intelligence-based digital twin systems used in SHM and proposes a new asset management framework. The way this paper is structured suits researchers and practicing experts both in the fields of sensors as well as in asset management equally.

257、Machine learning techniques for robotic and autonomous inspection of mechanical systems and civil infrastructure
Abstract：Machine learning and in particular deep learning techniques have demonstrated the most efficacy in training,learning, analyzing, and modelling large complex structured and unstructured datasets. These techniques have recently been commonly deployed in different industries to support robotic and autonomous system (RAS) requirements and applications ranging from planning and navigation to machine vision and robot manipulation in complex environments. This paper reviews the state-of-the-art with regard to RAS technologies (including unmanned marine robot systems, unmanned ground robot systems, climbing and crawler robots, unmanned aerial vehicles, and space robot systems) and their application for the inspection and monitoring of mechanical systems and civil infrastructure. We explore various types of data provided by such systems and the analytical techniques being adopted to process and analyze these data. This paper provides a brief overview of machine learning and deep learning techniques, and more importantly, a classification of the literature which have reported the deployment of such techniques for RAS-based inspection and monitoring of utility pipelines, wind turbines, aircrafts,power lines, pressure vessels, bridges, etc. Our research provides documented information on the use of advanced data-driven technologies in the analysis of critical assets and examines the main challenges to the applications of such technologies in the industry.

258、A machine learning approach to road surface anomaly assessment using smartphone sensors
Abstract：Road surface quality is essential for improving driving experience and reducing traffic accidents. Traditional road condition monitoring systems are limited in their temporal (speed) and spatial (coverage) responses needed for maintaining overall road quality. Several alternative systems have been proposed that utilize sensors mounted on vehicles. In particular, with the ubiquitous use of smartphones for navigation, smartphone-based road condition assessment has emerged as a promising new approach. In this paper, we propose to analyze different multiclass supervised machine learning techniques to effectively classify road surface conditions using accelerometer, gyroscope and GPS data collected from smartphones. Our work focuses on classification of three main class labels- smooth road, potholes, and deep transverse cracks. We hypothesize that using features from all three axes of the sensors provides more accurate results as compared to using features from only one axis. We also investigate the performance of deep neural networks to classify road conditions with and without explicit manual feature extraction. Our results indicate that models trained with features from all axes of the smartphone sensors outperform models that use only one axis. We also observe that the use of neural networks provides a significantly improved data classification. The machine learning approach discussed here can be implemented on a larger scale to monitor roads for defects that present a safety risk to commuters as well as to provide maintenance information to relevant authorities.

259、Strategies for autonomous robots to inspect pavement distresses
Abstract：The distress survey is an important task for pavement maintenance and rehabilitation (M&R) activities. As distress surveys require tremendous human resources, many investigators have begun to develop automatic inspection methods with the aim of increasing the efficiency and accuracy of inspections. After assessment of distress surveys on pavements using an autonomous robot (P3-AT), this research aims at developing motion strategies for executing distress surveys using robots under project-level practices. Three motion strategies were specifically developed: (1) Strategy I: random survey (R); (2) Strategy II: random survey with map recording (R+M); (3) Strategy III: random survey with map recording and vision guidance (R+M+V). To validate these three strategies, we developed a test field in a virtual environment. The test field included five distress types, including an alligator crack, a small patching, a pothole, a rectangular manhole and a circular manhole. We also developed a virtual robot to navigate the test field autonomously. The three survey strategies were then implemented by the virtual robot and their performances were compared with the current traffic-directional survey strategy.


264、Evaluation of the feasibility of Common Mid-Point approach for air-coupled GPR applied to road pavement assessment
Abstract：Ground Penetrating Radar (GPR) is commonly used in pavement assessment, manly for measuring pavement layer thickness. Asphalt layer thickness is the most relevant input for flexible pavements assessment. The present study evaluates the feasibility of the Common Mid-Point (CMP) method, for a coreless GPR approach, using air-coupled antennas. Three test sections, with different asphalt layer thicknesses, were evaluated using a pair of 1.8 GHz air-coupled antennas. Results highlight the satisfactory performance of the CMP method with air-coupled antennas. The estimation of the asphalt layer thickness leads to a linear coefficient of determination greater than 0.95 when compared with the measured thickness from cores. The CMP method presented lower average absolute errors for the thinner asphalt layers (about 6 cm) in comparison with the SRM method (6% vs 18%). For thicker layers (from 11 to 13 cm), the average absolute errors for both methods were similar (approximately 7%).


267、Characterizing pavement surface distress conditions with hyper-spatial resolution natural color aerial photography
Abstract：Roadway pavement surface distress information is critical for effective pavement asset management, and subsequently, transportation management agencies at all levels (i.e., federal, state, and local) dedicate a large amount of time and money to routinely evaluate pavement surface distress conditions as the core of their asset management programs. However, currently adopted ground-based evaluation methods for pavement surface conditions have many disadvantages, like being time-consuming and expensive. Aircraft-based evaluation methods, although getting more attention, have not been used for any operational evaluation programs yet because the acquired images lack the spatial resolution to resolve finer scale pavement surface distresses. Hyper-spatial resolution natural color aerial photography (HSR-AP) provides a potential method for collecting pavement surface distress information that can supplement or substitute for currently adopted evaluation methods. Using roadway pavement sections located in the State of New Mexico as an example, this research explored the utility of aerial triangulation (AT) technique and HSR-AP acquired from a low-altitude and low-cost small-unmanned aircraft system (S-UAS), in this case a tethered helium weather balloon, to permit characterization of detailed pavement surface distress conditions. The Wilcoxon Signed Rank test, Mann-Whitney U test, and visual comparison were used to compare detailed pavement surface distress rates measured from HSR-AP derived products (orthophotos and digital surface models generated from AT) with reference distress rates manually collected on the ground using standard protocols. The results reveal that S-UAS based hyper-spatial resolution imaging and AT techniques can provide detailed and reliable primary observations suitable for characterizing detailed pavement surface distress conditions comparable to the ground-based manual measurement, which lays the foundation for the future application of HSR-AP for automated detection and assessment of detailed pavement surface distress conditions.


269、Identification of rut and pothole by using multirotor unmanned aerial vehicle (UAV)
Abstract：Road development is very important in many countries. Many technologies have been used in road maintenance. The unmanned aerial vehicle (UAV) promises a rapid and cost saving technology and is widely used in the mapping industry. The aim of this study is to assess rut and pothole on road surface by using UAV. The methodology consists of four phases, namely site reconnaissance and planning, data acquisition, data processing and result and data analysis. Data acquisition is based on difference altitudes to determine the effect of resolution in rut and pothole extraction. In this study, the data processing is by using a photogrammetric software which is based on structure from motion. The accuracy assessment in this study is based on actual and measured data comparison, which only concentrates on rut and pothole samples. It was found that low altitude gives a better result as compared to high altitude. In conclusion, this study demonstrated the capability of multirotor UAV image for rut and pothole extraction. This system provides a detailed and accurate measurement of road rut and pothole, and thus improves the efficiency of road condition monitoring.


271、Detection of asphalt pavement potholes and cracks based on the unmanned aerial vehicle multispectral imagery
Abstract：Asphalt roads are the basic component of a land transportation system, and the quality of asphalt roads will decrease during the use stage because of the aging and deterioration of the road surface. In the end, some road pavement distresses may appear on the road surface, such as the most common potholes and cracks. In order to improve the efficiency of pavement inspection, currently some new forms of remote sensing data without destructive effect on the pavement are widely used to detect the pavement distresses, such as digital images, light detection and ranging, and radar. Multispectral imagery presenting spatial and spectral features of objects has been widely used in remote sensing application. In our study, the multispectral pavement images acquired by unmanned aerial vehicle (UAV) were used to distinguish between the normal pavement and pavement damages (e.g., cracks and potholes) using machine learning algorithms, such as support vector machine, artificial neural network, and random forest. Comparison of the performance between different data types and models was conducted and is discussed in this study, and indicates that a UAV remote sensing system offers a new tool for monitoring asphalt road pavement condition, which can be used as decision support for road maintenance practice.


273、Mapping asphaltic roads' skid resistance using imaging spectroscopy
Abstract：The purpose of this study is to evaluate a realistic feasibility of using hyperspectral remote sensing (also termed imaging spectroscopy) airborne data for mapping asphaltic roads’ transportation safety. This is done by quantifying the road-tire friction, an attribute responsible for vehicle control and emergency stopping. We engaged in a real-life operational scenario, where the roads’ friction was modeled against the reflectance information extracted directly from the image. The asphalt pavement’s dynamic friction coefficient was measured by a standardized technique using a Dynatest 6875H (Dynatest Consulting Inc., Westland, MI, USA) Friction Measuring System, which uses the common test-wheel retardation method. The hyperspectral data was acquired by the SPECIM AisaFenix 1K (Specim, Spectral Imaging Ltd., Oulu, Finland) airborne system, covering the entire optical range (350–2500 nm), over a selected study site, with roads characterized by different aging conditions. The spectral radiance data was processed to provide apparent surface reflectance using ground calibration targets and the ACORN-6 atmospheric correction package. Our final dataset was comprised of 1370 clean asphalt pixels coupled with geo-rectified in situ friction measurement points. We developed a partial least squares regression model using PARACUDA-II spectral data mining engine, which uses an automated outlier detection procedure and dual validation routines—a full cross-validation and an iterative internal validation based on a Latin Hypercube sampling algorithm. Our results show prediction capabilities of R2 = 0.632 for full cross-validation and R2 = 0.702 for the best available model in internal validation, both with significant results (p < 0.0001). Using spectral assignment analysis, we located the spectral bands with the highest weight in the model and discussed their possible physical and chemical assignments. The derived model was applied back on the hyperspectral image to predict and map the friction values of every road pixel in the scene. Combining the standard method with imaging spectroscopy may provide the required expansion of the available data to furnish decision makers with a full picture of the roads’ status. This technique’s limitations originate mainly in compositional variations between different roads, and the requirement for the application of multiple calibrations between scenes. Possible improvements could be achieved by using more spectral regions (e.g., thermal) and additional remote sensing techniques (e.g., LIDAR) as well as new platforms (e.g., UAV).

274、Review of robotic infrastructure inspection systems
Abstract：In order to minimize the costs, risks, and disruptions associated with structural inspections, robotic systems have increasingly been studied as an enhancement to current inspection practices. Combined with the increasing variety of commercially available robots, the last two decades have seen dramatic growth in the application of such systems. The use of these systems spans the breadth of civil infrastructure works, and the variety of implemented robotic systems is growing rapidly. However, the highly interdisciplinary nature of research in this field means that results are disseminated across a broad variety of publications. This review paper aggregates these studies in an effort to distill the state of the art in inspection robotics, as well as to assess outstanding challenges in the field and possibilities for the future. Overall, analysis of these studies illustrates that the design of inspection robots is often a case-specific compromise between competing needs for sophisticated inspection sensing and for flexible locomotion in challenging field environments. This review also points toward the growing use of robots as a platform to deploy advanced nondestructive evaluation (NDE) technologies, as well as the expanded use of commercially available robotic systems. Two key outstanding challenges for future researchers are suggested as well. The first is the need for more sophisticated, and inspection-driven, robot autonomy. The other is the need to process and manipulate the massive data sets that modern robots generate.


276、Construction inspection & monitoring with quadruped robots in future human-robot teaming: A preliminary study
Abstract：Construction inspection and monitoring are key activities in construction projects. Automation of inspection tasks can address existing limitations and inefficiencies of the manual process to enable systematic and consistent construction inspection. However, there is a lack of an in-depth understanding of the process of construction inspection and monitoring and the tasks and sequences involved to provide the basis for task delegation in a human-technology partnership. The purpose of this research is to study the conventional process of inspection and monitoring of construction work currently implemented in construction projects and to develop an alternative process using a quadruped robot as an inspector assistant to overcome the limitations of the conventional process. This paper explores the use of quadruped robots for construction inspection and monitoring with an emphasis on a human-robot teaming approach. Technical development and testing of the robotic technology are not in the scope of this study. The results indicate how inspector assistant quadruped robots can enable a human-technology partnership in future construction inspection and monitoring tasks. The research was conducted through on-site experiments and observations of inspectors during construction inspection and monitoring followed by a semi-structured interview to develop a process map of the conventional construction inspection and monitoring process. The study also includes on-site robot training and experiments with the inspectors to develop an alternative process map to depict future construction inspection and monitoring work with the use of an inspector assistant quadruped robot. Both the conventional and alternative process maps were validated through interview surveys with industry experts against four criteria including, completeness, accuracy, generalizability, and comprehensibility. The findings suggest that the developed process maps reflect existing and future construction inspection and monitoring work.

277、Design of robot based work progress monitoring system for the building construction site
Abstract：The building construction project has plan, budget and timeline and the construction work should be done according to the work schedule predefined during the design period. But many construction projects are delayed due to the human work error and this leads to the over budget. To reduce the delay of construction work, the work progress should be monitored periodically and the delayed part should be managed early and fast. For this purpose, the construction industry usually sends the project manager to the construction site with clipboards and camera. However checking the progress by human can cause error and have limitation. To reduce the error and automate the progress monitoring we propose the robot based work progress monitoring system for the building construction site. We combine the drone with propellers and usual robot with wheels. The propeller is used to avoid the obstacle or floor change and the wheel is used to go around on the flat area. The monitoring robot has the VR camera for the video recoding and the LiDAR for the laser scanning. The route for the robot is generated based on the timeline and 3D design data such as 3D CAD or BIM. Progress monitoring is done based on the comparison between the design data and collected data by the robot.

278、New robot for power line inspection
Abstract：Power line inspection is of the utmost importance for the reliability and stability of electric power distribution. However, manual inspection is a hazardous, slow, expensive and unreliable task. Therefore, new highly specialized robots are required to improve the overall quality and safety of the power line inspection. The research conducted so far has been mainly focused on the development of climbing and flying robots. This paper first addresses the main achievements in the field of robotic power line inspection. The proposed solutions are critically assessed and the associated problems are outlined. Based on these findings, a new concept for robot-assisted power line inspection, combining both climbing and flying principles, is proposed in the second part of the paper. The proposed concept is critically assessed and related to the other established concepts so as to demonstrate its advantages and feasibility for a routine power line inspection.


281、Inspection robotic system: design and simulation for indoor and outdoor surveys
Abstract：In this paper, the design and simulation are presented for a wheeled robot designed for indoor and outdoor inspections. In particular, for a large number of cases, an automatic or tele-operated survey can be performed by wheeled mobile robots, which represent the most efficient solution in terms of power consumption, control, robustness and overall costs. Referring to the analysis of structures and infrastructure, such as bridges and pipelines, wheeled robots must be able to move on horizontal or sloped surfaces and overpass obstacles that in most of cases are steps, i.e. longitudinal internal stiffeners. In this paper, we present a mechatronic design and simulations of a wheeled robot being used in indoor and outdoor inspections, taking as illustrative example an infrastructure inspection. In particular, the wheeled robot is equipped with suitable sensors in order to take information on the main structural elements, avoiding the need of experienced personnel to get directly inside the site to be inspected.

282、Nondestructive evaluation sensor fusion with autonomous robotic system for civil infrastructure inspection
Abstract：Civil infrastructure inspection is crucial to maintaining the quality of that infrastructure, which has a great impact on the economy. Performing this inspection is costly work that requires workers to be trained on how to use varying technologies, which can be error prone when performed manually and can result in damage to the infrastructure in some cases. For this reason, nondestructive evaluation (NDE) sensors are preferred for civil infrastructure inspection as they can perform the necessary inspection without damaging the infrastructure. In this paper, we develop a fully autonomous robotic system capable of real‐time data collection and quasi‐real‐time data processing. The robotic system is equipped with several NDE sensors that allow for a sensor fusion method to be developed that successfully minimizes inspection time while performing adequate inspection of areas that require more in‐depth data to be collected. A detailed discussion of the inspection framework developed for this robotic system, and the dual navigation modes for both indoor and outdoor autonomous navigation is presented. The developed robotic system is deployed to inspect several infrastructures (e.g., parking garages, bridges) at and near by the University of Nevada, Reno campus.


284、SLAM-driven intelligent autonomous mobile robot navigation for construction applications
Abstract：The demand for construction site automation with mobile robots is increasing due to its advantages in potential cost-saving, productivity, and safety. To be realistically deployed in construction sites, mobile robots must be capable of navigating in unstructured and cluttered environments. Furthermore, mobile robots should recognize both static and dynamic obstacles to determine drivable paths. However, existing robot navigation methods are not suitable for construction applications due to the challenging environmental conditions in construction sites. This study introduces an autonomous as-is 3D spatial data collection and perception method for mobile robots specifically aimed for construction job sites with many spatial uncertainties. The proposed Simultaneous Localization and Mapping (SLAM)-based navigation and object recognition methods were implemented and tested with a custom-designed mobile robot platform, Ground Robot for Mapping Infrastructure (GRoMI), which uses multiple laser scanners and a camera to sense and build a 3D environment map. Since SLAM did not detect uneven surface conditions and spatiotemporal objects on the ground, an obstacle detection algorithm was developed to recognize and avoid obstacles and the highly uneven terrain in real time. Given the 3D real-time scan map generated by 3D laser scanners, a path-finding algorithm was developed for autonomous navigation in an unknown environment with obstacles. Overall, the 3D color-mapped point clouds of construction sites generated by GRoMI were of sufficient quality to be used for many construction management applications such as construction progress monitoring, safety hazard identification, and defect detection.

285、A multi-functional inspection robot for civil infrastructure evaluation and maintenance
Abstract：Satisfactory operation of civil infrastructure is of critical importance to an economy. In order to maintain performance, infrastructure needs to be properly maintained. Inspecting infrastructure is inherently labor-intensive work and costly. In this paper, we propose a solution to cost-effective infrastructure inspection by developing a multi-functional inspection robot. The robot is equipped with several state-of-the-art non-destructive evaluation (NDE) sensors to perform inspection. The robot is able to perform selected inspection methods in certain areas based on multiple sensor data fusion. With this, the overall inspection time is reduced, which in turn reduces maintenance cost. An inspection framework based on multiple NDE data sensor fusion is proposed. Detailed discussions include robot design, robot navigation and sensor data fusion.


287、Self-reconfigurable façade-cleaning robot equipped with deep-learning-based crack detection based on convolutional neural networks
Abstract：Despite advanced construction technologies that are unceasingly filling the city-skylines with glassy high-rise structures, maintenance of these shining tall monsters has remained a high-risk labor-intensive process. Thus, nowadays, utilizing façade-cleaning robots seems inevitable. However, in case of navigating on cracked glass, these robots may cause hazardous situations. Accordingly, it seems necessary to equip them with crack-detection system to eventually avoid cracked area. In this study, benefitting from convolutional neural networks developed in TensorFlow™, a deep-learning-based crack detection approach is introduced for a novel modular façade-cleaning robot. For experimental purposes, the robot is equipped with an on-board camera and the live video is loaded using OpenCV. The vision-based training process is fulfilled by applying two different optimizers utilizing a sufficiently generalized data-set. Data augmentation techniques and also image pre-processing also apply as a part of process. Simulation and experimental results show that the system can hit the milestone on crack-detection with an accuracy around 90%. This is satisfying enough to replace human-conducted on-site inspections. In addition, a thorough comparison between the performance of optimizers is put forward: Adam optimizer shows higher precision, while Adagrad serves more satisfying recall factor, however, Adam optimizer with the lowest false negative rate and highest accuracy has a better performance. Furthermore, proposed CNN's performance is compared to traditional NN and the results provide a remarkable difference in success level, proving the strength of CNN.

288、Inspection of surface defects on stay cables using a robot and transfer learning
Abstract：In-service stay cables suffer from surface scratch and crack defects, which may cause corrosion inside cables, and fracture damage is likely to occur when those defects are exposed to long-term rain and sunshine environments. Current methods such as manual inspection and bridge inspection vehicles are inefficient, costly and risky. However, traditional image processing technologies (e.g., Canny) and convolutional neural networks may not be able to obtain accurate surface defect information. This paper proposes a novel and cost-effective method for identifying stay cable surface defects combining a cable inspection robot and transfer learning on a cascade mask region conventional neural network (Cascade Mask RCNN). This automatic procedure not only precisely identifies the defects but also locates and measures the defects that can be used for further maintenance strategies. Comparison work and on-site testing were conducted to evaluate the proposed model performance, and the validity of cable defects identification and measurement.An automatic and cost-effective inspection method is proposed for cable surface defect detection. Transfer learning with Cascade Mask RCNN model is presented for defect identification and location. The IoU index can reach up to 0.743, comparison work with other networks and on-site test was implemented to validate the validity and accuracy of cable surface defect detection and measurement.

289、Development of hanger-rope inspection robot for suspension bridges
Abstract：This paper describes the development of a hangerrope inspection robot for suspension bridges. The inspection robot inspects the hanger rope by going up and down the rope installed vertically downward from the main cable of a suspension bridge. The going-upand-down mechanism of the robot consists of a drive roller driven by a motor and a non-excitation electromagnetic brake, and the robot can safely descend after climbing the rope at high speed. The developed robot is small in size and light in weight, and an inspection worker can easily install the robot on the rope. In addition, the robot can be wirelessly controlled with ease from the controller. First, this paper describes the hanger-rope inspection strategy of the suspension bridge. Then, the developed prototype 2 robot is introduced. Next, the result of the hanger-rope inspection in an actual suspension bridge and the problems are clearly revealed by experiment. Finally, the newly developed prototype 3 robot is introduced, and the result of the going-up-and-down experiment is described.

290、Complete coverage path planning using reinforcement learning for tetromino based cleaning and maintenance robot
Abstract：Tiling robotics have been deployed in autonomous complete area coverage tasks such as floor cleaning, building inspection, and maintenance, surface painting. One class of tiling robotics, polyomino-based reconfigurable robots, overcome the limitation of fixed-form robots in achieving high-efficiency area coverage by adopting different morphologies to suit the needs of the current environment. Since the reconfigurable actions of these robots are produced by real-time intelligent decisions during operations, an optimal path planning algorithm is paramount to maximize the area coverage while minimizing the energy consumed by these robots. This paper proposes a complete coverage path planning (CCPP) model trained using deep blackreinforcement learning (RL) for the tetromino based reconfigurable robot platform called hTetro to simultaneously generate the optimal set of shapes for any pretrained arbitrary environment shape with a trajectory that has the least overall cost. To this end, a Convolutional Neural Network (CNN) with Long Short Term Memory (LSTM) layers is trained using Actor Critic Experience Replay (ACER) reinforcement learning algorithm. The results are compared with existing approaches which are based on the traditional tiling theory model, including zigzag, spiral, and greedy search schemes. The model is also compared with the Travelling salesman problem (TSP) based Genetic Algorithm (GA) and Ant Colony Optimization (ACO) schemes. The proposed scheme generates a path with lower cost while also requiring lesser time to generate it. The model is also highly robust and can generate a path in any pretrained arbitrary environments.

291、Fundamentals and prospects of four-legged robot application in construction progress monitoring
Abstract：Progress monitoring in the construction industry is mostly a manual process through in-person visual inspection and it leads to inconsistent, time-consuming, labor-intensive, and error-prone data acquisition. The key in progress monitoring is accurate, timely, and regular data collection and analysis during the construction process. Automating the process of regular data collection in progress monitoring can enable systematic recording of construction progress. To achieve this goal, mobile robots with autonomous navigation and high-performance locomotion capabilities can potentially navigate dynamically changing construction workspaces to perform regular data collection. This study identifies fundamentals of robot-enabled procedures for automated construction progress monitoring and explores the opportunities and challenges of utilizing a legged robot in this process. Through collaboration between academia and industry, this study conducts a set of experiments with a four-legged robot equipped with a 360 image capture technology to automate data collection in construction progress monitoring. The results of these experiments have identified the opportunities and operating procedure for robot-enabled image capturing. The study has also discussed current limitations in the automated construction progress monitoring including safety limitations, operation limitations, and mission limitations.

292、Adaptive locomotion control of hexapod walking robot for traversing rough terrains with position feedback only
Abstract：Traversing rough terrains is one of the domains where multi-legged walking robots benefit from their relatively more complex kinematics in comparison to wheeled robots. The complexity of walking robots is usually related not only to mechanical parts but also to servomotors and the necessary electronics to efficiently control such a robotic system. Therefore, large, middle, but even small walking robots capable of traversing rough terrains can be very costly because of all the required equipment. On the other hand, using intelligent servomotors with the position control and feedback, affordable hexapod walking robots are becoming increasingly available. However, additional sensors may still be needed to stabilize the robot motion on rough terrains, e.g., inclinometers or inertial measurement units, force or tactile sensors to detect the ground contact point of the leg foot-tip. In this work, we present a minimalistic approach for adaptive locomotion control using only the servomotors position feedback. Adaptive fine-tuning of the proposed controller is supported by a dynamic model of the robot leg accompanied by the model of the internal servomotor controller. The models enable timely detection of the leg contact point with the ground and reduce developed stress and torques applied to the robot construction and servomotors without any additional sensor feedback. The presented results support that the proposed approach reliably detects the ground contact point, and thus enable traversing rough terrains with small, affordable hexapod walking robot.


294、BIM-driven mission planning and navigation for automatic indoor construction progress detection using robotic ground platform
Abstract：Reconstructing a complete and accurate 3D representation of indoor construction scenes is an important step towards automated visual monitoring of construction projects. For fast access to construction’s as-built visual data, construction drones are programmed to autonomously navigate the outdoor space and collect the data. However, due to limited satellite signal indoors, ground rovers provide safer and more reliable autonomous navigation inside the narrow indoor navigable space. In this paper we present a novel pipeline for 4D BIM-driven mapping of the as- built state of indoor construction using 2D Light Detection and Ranging (LiDAR) sensors mounted on an Unmanned Ground Vehicle (UGV). The developed method consists of (1) BIM-driven data collection planning; (2) automatic mission navigation; (3) LiDAR data collection and (4) dynamic obstacle avoidance. Experiments show the applicability of the developed data collection strategy and the improved safety of automatic mission execution using UGV.

297、QuicaBot: Quality inspection and assessment robot
Abstract：Quality assessment during postconstruction of buildings is an indispensable procedure in construction industry. This paper describes the design and development of a quality inspection and assessment robot (QuicaBot) that can autonomously scan the entire room using cameras and laser scanners to pick up building defects, such as hollowness, crack, evenness, alignments, and inclination. A robotic system consisting of four types of sensors and a mobile platform as well as the corresponding five types of assessment algorithms is proposed. To the best of our knowledge, this paper is the first attempt to have a complete robotic system for postconstruction quality assessment of buildings. The aim of the developed system is twofold: first, to systematize the manual inspection work through automation resulting in more reliable and objective inspection reports, and then, to speed up the inspection process resulting in a more efficient end product. Based on our experimental on-site tests, the developed novel robot takes only half of the manual inspection time when inspecting the same room. We have also observed that the autonomous assessment results have better inspection accuracy when compared to manual assessments. Last but not least, the results provided by QuicaBot have more consistent measurement accuracy when compared to a manual assessor. Motivated by the initial successful on-site tests and as being a practical mechatronic system illustrating how sensing, sensor fusion, and actuation can be integrated to achieve an intelligent system for building defects assessment, we believe that the QuicaBot-like robots are going to become an integral part of construction industry in the near future.

298、Human-robot collaboration and sensor-based robots in industrial applications and construction
Abstract：This paper presents technologies for human-robot collaborative and sensor-based applications for robotics in construction. Principles, safety and control technologies of human-robot collaboration are outlined and sensor-assisted control of industrial robots as well as a dynamic safety system for industrial robots are described in more details. Applicability of sensor-based robotics in building construction and potential of robotics in building construction in general are also evaluated.

299、Urban road pavements monitoring and assessment using bike and e-scooter as probe vehicles
Abstract：Comfort and safe-mobility are two aspects that should be provided to non-motorized users that are an increasing component of the urban micro-mobility (e.g. bikes and e-scooters). For these road users, road pavement unevenness, cracks, potholes and other surface defects can make riding uncomfortable and potentially hazardous. Traditionally, road managers use standardized surveys and Key Performance Indicators (KPIs) of pavement to make decision for maintenance programs and pavement management of road urban network poses challenges in the survey of pavement surface for International Roughness Index (IRI) and distress assessment both for operational and cost constrains. Moreover, there are theoretically limitations of IRI model for low speed urban roads and low-damped vehicles like bikes and e-scooters. In such framework aims of the paper is to investigate the use of smartphone sensors to collect data for the assessment of pavement conditions and definition of KPIs for bike and e-scooter users’ ride comfort and safety. A controlled experiment was performed with repeated runs of a bicycle and e-scooter equipped with a smartphone and an android application was used to collect acceleration and position data. Detailed pavement conditions have been identified with an advanced survey equipment. After data treatments for removing signal noise, adjustment for speed variability and outlier detection, root mean square (RMS) of vertical acceleration signal and weighted frequency content of the vibrations according to ISO 2631–1, have been confirmed suitable KPIs of pavement conditions and comfort rating that can be collected by bikes and e-scooter. Results confirmed the lack of correlation of vibrations in bikes and e-scooter with analogous parameters collected with car as probe vehicle and with IRI standard values, as well. Instead, pavement monitoring by bikes and e-scooter can provide effective detection of typologies and severities of distresses not detectable by similar approaches in damped vehicles. Good correlations have been identified between RMS and medium severity alligator, longitudinal and transversal cracks. High severity pavement distress and potholes have been identified by outliers in RMS.

300、Autonomous road detection and modeling for UGVs using vision-laser data fusion
Abstract：Autonomous road detection and modeling play a key role for UGVs navigating in complex outdoor environments. This paper investigates road detection and description for UGVs in various outdoor scenes under different weather conditions. A novel environment perception system that includes two cameras and multiple laser range finders is introduced firstly. Taking classification accuracy and time-cost into account, 8-dimensional features are selected from a 91-dimensional candidate feature set using Adaboost algorithm. To adapt to the diversity of road scenes under different weather conditions in different seasons, an online classifier based on SVM is proposed to replace the fixed one. Moreover, a road region adjusting algorithm is present to eliminate misclassified regions especially when the roads have fuzzy boundaries or obstacles. Finally, a RANSAC spline fitting algorithm is adopted to provide an accurate road border model for UGVs’ autonomous path planning and navigation. A series of experiments are conducted by using a self-built UGV platform and experimental results show the validity and practicality of the proposed approaches.


301、A Survey on Active Simultaneous Localization and Mapping: State of the
   Art and New Frontiers
Abstract： Active Simultaneous Localization and Mapping (SLAM) is the problem of planning and controlling the motion of a robot to build the most accurate and complete model of the surrounding environment. Since the first foundational work in active perception appeared, more than three decades ago, this field has received increasing attention across different scientific communities. This has brought about many different approaches and formulations, and makes a review of the current trends necessary and extremely valuable for both new and experienced researchers. In this work, we survey the state-of-the-art in active SLAM and take an in-depth look at the open challenges that still require attention to meet the needs of modern applications. After providing a historical perspective, we present a unified problem formulation and review the well-established modular solution scheme, which decouples the problem into three stages that identify, select, and execute potential navigation actions. We then analyze alternative approaches, including belief-space planning and deep reinforcement learning techniques, and review related work on multi-robot coordination. The manuscript concludes with a discussion of new research directions, addressing reproducible research, active spatial perception, and practical applications, among other topics.

302、Safe Planning in Dynamic Environments Using Conformal Prediction
Abstract： We propose a framework for planning in unknown dynamic environments with probabilistic safety guarantees using conformal prediction. Particularly, we design a model predictive controller (MPC) that uses i) trajectory predictions of the dynamic environment, and ii) prediction regions quantifying the uncertainty of the predictions. To obtain prediction regions, we use conformal prediction, a statistical tool for uncertainty quantification, that requires availability of offline trajectory data - a reasonable assumption in many applications such as autonomous driving. The prediction regions are valid, i.e., they hold with a user-defined probability, so that the MPC is provably safe. We illustrate the results in the self-driving car simulator CARLA at a pedestrian-filled intersection. The strength of our approach is compatibility with state of the art trajectory predictors, e.g., RNNs and LSTMs, while making no assumptions on the underlying trajectory-generating distribution. To the best of our knowledge, these are the first results that provide valid safety guarantees in such a setting.

303、An Unmanned Surface Vehicle (USV): Development of an Autonomous Boat
   with a Sensor Integration System for Bathymetric Surveys
Abstract： A reliable yet economical unmanned surface vehicle (USV) has been developed for the bathymetric surveying of lakes. The system combines an autonomous navigation framework, environmental sensors, and a multibeam echosounder to collect submerged topography, temperature, and wind speed and monitor the vehicle's status during prescribed path-planning missions. The main objective of this research is to provide a methodological framework to build an autonomous boat with independent decision-making, efficient control, and long-range navigation capabilities. Integration of sensors with navigation control enabled the automatization of position, orientation, and velocity. A solar power integration was also tested to control the duration of the autonomous missions. The results of the solar power compared favorably with those of the standard LiPO battery system. Extended and autonomous missions were achieved with the developed platform, which can also evaluate the danger level, weather circumstances, and energy consumption through real-time data analysis. With all the incorporated sensors and controls, this USV can make self-governing decisions and improve its safety. A technical evaluation of the proposed vehicle was conducted as a measurable metric of the reliability and robustness of the prototype. Overall, a reliable, economic, and self-powered autonomous system has been designed and built to retrieve bathymetric surveys as a first step to developing intelligent reconnaissance systems that combine field robotics with machine learning to make decisions and adapt to unknown environments.

304、Multi-Risk-RRT: An Efficient Motion Planning Algorithm for Robotic
   Autonomous Luggage Trolley Collection at Airports
Abstract： Robots have become increasingly prevalent in dynamic and crowded environments such as airports and shopping malls. In these scenarios, the critical challenges for robot navigation are reliability and timely arrival at predetermined destinations. While existing risk-based motion planning algorithms effectively reduce collision risks with static and dynamic obstacles, there is still a need for significant performance improvements. Specifically, the dynamic environments demand more rapid responses and robust planning. To address this gap, we introduce a novel risk-based multi-directional sampling algorithm, Multi-directional Risk-based Rapidly-exploring Random Tree (Multi-Risk-RRT). Unlike traditional algorithms that solely rely on a rooted tree or double trees for state space exploration, our approach incorporates multiple sub-trees. Each sub-tree independently explores its surrounding environment. At the same time, the primary rooted tree collects the heuristic information from these sub-trees, facilitating rapid progress toward the goal state. Our evaluations, including simulation and real-world environmental studies, demonstrate that Multi-Risk-RRT outperforms existing unidirectional and bi-directional risk-based algorithms in planning efficiency and robustness.

305、Stability Analysis and Navigational Techniques of Wheeled Mobile Robot:
   A Review
Abstract： Wheeled mobile robots (WMRs) have been a focus of research for several decades, particularly concerning navigation strategies in static and dynamic environments. This review article carefully examines the extensive academic efforts spanning several decades addressing navigational complexities in the context of WMR route analysis. Several approaches have been explored by various researchers, with a notable emphasis on the inclusion of stability and intelligent capabilities in WMR controllers attracting the attention of the academic community. This study traces historical and contemporary WMR research, including the establishment of kinetic stability and the construction of intelligent WMR controllers. WMRs have gained prominence in various applications, with precise navigation and efficient control forming the basic prerequisites for their effective performance. The review presents a comprehensive overview of stability analysis and navigation techniques tailored for WMRs. Initially, the exposition covers the basic principles of WMR dynamics and kinematics, explaining the different wheel types and their associated constraints. Subsequently, various stability analysis approaches, such as Lyapunov stability analysis and passivation-based control, are discussed in depth in the context of WMRs. Starting an exploration of navigation techniques, the review highlights important aspects including path planning and obstacle avoidance, localization and mapping, and trajectory tracking. These techniques are carefully examined in both indoor and outdoor settings, revealing their benefits and limitations. Finally, the review ends with a comprehensive discussion of the current challenges and possible routes in the field of WMR. The discourse includes the fusion of advanced sensors and state-of-the-art control algorithms, the cultivation of more robust and reliable navigation strategies, and the continued exploration of novel WMR applications. This article also looks at the progress of mobile robotics during the previous three decades. Motion planning and path analysis techniques that work with single and multiple mobile robots have been discussed extensively. One common theme in this research is the use of soft computing methods to give mobile robot controllers cognitive behaviors, such as artificial neural networks (ANNs), fuzzy logic control (FLC), and genetic algorithms (GAs). Nevertheless, there is still a dearth of applications for mobile robot navigation that leverage nature-inspired algorithms, such as firefly and ant colony algorithms. Remarkably, most studies have focused on kinematics analysis, with a small number also addressing dynamics analysis.

306、Self-supervised prediction of the intention to interact with a service
   robot
Abstract： A service robot can provide a smoother interaction experience if it has the ability to proactively detect whether a nearby user intends to interact, in order to adapt its behavior e.g. by explicitly showing that it is available to provide a service. In this work, we propose a learning-based approach to predict the probability that a human user will interact with a robot before the interaction actually begins; the approach is self-supervised because after each encounter with a human, the robot can automatically label it depending on whether it resulted in an interaction or not. We explore different classification approaches, using different sets of features considering the pose and the motion of the user. We validate and deploy the approach in three scenarios. The first collects 3442 natural sequences (both interacting and non-interacting) representing employees in an office break area: a real-world, challenging setting, where we consider a coffee machine in place of a service robot. The other two scenarios represent researchers interacting with service robots (200 and 72 sequences, respectively). Results show that, even in challenging real-world settings, our approach can learn without external supervision, and can achieve accurate classification (i.e. AUROC greater than 0.9) of the user's intention to interact with an advance of more than 3 s before the interaction actually occurs.

307、Model-Predictive Control for Omnidirectional Mobile Robots in Logistic
   Environments Based on Object Detection Using CNNs
Abstract： Object detection is an essential component of autonomous mobile robotic systems, enabling robots to understand and interact with the environment. Object detection and recognition have made significant progress using convolutional neural networks (CNNs). Widely used in autonomous mobile robot applications, CNNs can quickly identify complicated image patterns, such as objects in a logistic environment. Integration of environment perception algorithms and motion control algorithms is a topic subjected to significant research. On the one hand, this paper presents an object detector to better understand the robot environment and the newly acquired dataset. The model was optimized to run on the mobile platform already on the robot. On the other hand, the paper introduces a model-based predictive controller to guide an omnidirectional robot to a particular position in a logistic environment based on an object map obtained from a custom-trained CNN detector and LIDAR data. Object detection contributes to a safe, optimal, and efficient path for the omnidirectional mobile robot. In a practical scenario, we deploy a custom-trained and optimized CNN model to detect specific objects in the warehouse environment. Then we evaluate, through simulation, a predictive control approach based on the detected objects using CNNs. Results are obtained in object detection using a custom-trained CNN with an in-house acquired data set on a mobile platform and in the optimal control for the omnidirectional mobile robot.

308、HuNavSim: A ROS 2 Human Navigation Simulator for Benchmarking
   Human-Aware Robot Navigation
Abstract： This work presents the Human Navigation Simulator (HuNavSim), a novel open-source tool for the simulation of different human-agent navigation behaviors in scenarios with mobile robots. The tool, the first programmed under the ROS 2 framework, can be used together with different well-known robotics simulators like Gazebo. The main goal is to facilitate the development and evaluation of human-aware robot navigation systems in simulation. In addition to a general human-navigation model, HuNavSim includes, as a novelty, a rich set of individual and varied human navigation behaviors and an comprehensive set of metrics for social navigation benchmarking.


310、Optimizing Mobile Robot Navigation Based on A-Star Algorithm for
   Obstacle Avoidance in Smart Agriculture
Abstract： The A-star algorithm (A*) is a traditional and widely used approach for route planning in various domains, including robotics and automobiles in smart agriculture. However, a notable limitation of the A-star algorithm is its tendency to generate paths that lack the desired smoothness. In response to this challenge, particularly in agricultural operations, this research endeavours to enhance the evaluation of individual nodes within the search procedure and improve the overall smoothness of the resultant path. So, to mitigate the inherent choppiness of A-star-generated paths in agriculture, this work adopts a novel approach. It introduces utilizing Bezier curves as a postprocessing step, thus refining the generated paths and imparting their smoothness. This smoothness is instrumental for real-world applications where continuous and safe motion is imperative. The outcomes of simulations conducted as part of this study affirm the efficiency of the proposed methodology. These results underscore the capability of the enhanced technique to construct smooth pathways. Furthermore, they demonstrate that the generated paths enhance the overall planning performance. However, they are also well suited for deployment in rural conditions, where navigating complex terrains with precision is a critical necessity.

311、Smart Wheelchair Controlled Through a Vision-Based Autonomous System
Abstract： People are preoccupied with their own duties in today's environment, making it tough to oversee and care for disabled people. Paralyzed and disabled people, on the other hand, have a powerful desire to move around freely. Different initiatives have been taken in the past to improve and preserve the self-esteem of such people, and various technologies have also been created to assist them in a better way. The primary advantage of a vision-based autonomous wheelchair is that it can provide greater independence to users with limited mobility. The methodology of other smart wheelchairs varies depending on the specific technology used. Some smart wheelchairs may use sensors to detect obstacles and provide feedback to the user, while others may incorporate GPS and other navigation technologies to assist with indoor and outdoor navigation. Our study proposes a revolutionary implementation of an autonomous system for fully handicapped people, allowing them to drive wheelchairs using just their eyes. The wheelchair's orientation will be determined by the orientation of the eye. The camera will follow the movement of the eyes. Furthermore, sensors will be installed on the front side of the wheelchair to identify any obstacles along its path. The calibration of the camera with the human eye, without obstructing its vision, is the most challenging task in this research, besides controlling the wheels of the wheelchair.

312、Enabling Autonomous Navigation on the Farm: A Mission Planner for
   Agricultural Tasks
Abstract： This study presents the development of a route planner, called Mission Planner, for an agricultural weeding robot that generates efficient and safe routes both in the field and on the farm using a graph-based approach. This planner optimizes the robot's motion throughout the farm and performs weed management tasks tailored for high-power laser devices in narrow-row crops (wheat, barley, etc.) and wide-row crops (sugar beet, maize, etc.). Three main algorithms were integrated: Dijkstra's algorithm to find the most optimal route on the farm, the VRMP (Visibility Road-Map Planner) method to select the route within cultivated fields when roads are not visible, and an improved version of the Hamiltonian path to find the best route between the crop lines. The results support the effectiveness of the strategies implemented, demonstrating that a robot can safely and efficiently navigate through the entire farm and perform an agricultural treatment, in this case study, in laser-based weed management. In addition, it was found that the route planner reduced the robot's operation time, thus improving the overall efficiency of precision agriculture.

313、Egocentric Computer Vision for Hands-Free Robotic Wheelchair Navigation
Abstract： In this paper, we present an approach for navigating a robotic wheelchair that provides users with multiple levels of autonomy and navigation capabilities to fit their individual needs and preferences. We focus on three main aspects: (i) egocentric computer vision based motion control to provide a natural human-robot interface to wheelchair users with impaired hand usage; (ii) techniques that enable user to initiate autonomous navigation to a location, object or person without use of the hands; and (iii) a framework that learns to navigate the wheelchair according to its user's, often subjective, criteria and preferences. These contributions are evaluated qualitatively and quantitatively in user studies with several subjects demonstrating their effectiveness. These studies have been conducted with healthy subjects, but they still indicate that clinical tests of the proposed technology can be initiated.

314、Self-Learning Robot Autonomous Navigation with Deep Reinforcement
   Learning Techniques
Abstract： Complex and high-computational-cost algorithms are usually the state-of-the-art solution for autonomous driving cases in which non-holonomic robots must be controlled in scenarios with spatial restrictions and interaction with dynamic obstacles while fulfilling at all times safety, comfort, and legal requirements. These highly complex software solutions must cover the high variability of use cases that might appear in traffic conditions, especially when involving scenarios with dynamic obstacles. Reinforcement learning algorithms are seen as a powerful tool in autonomous driving scenarios since the complexity of the algorithm is automatically learned by trial and error with the help of simple reward functions. This paper proposes a methodology to properly define simple reward functions and come up automatically with a complex and successful autonomous driving policy. The proposed methodology has no motion planning module so that the computational power can be limited like in the reactive robotic paradigm. Reactions are learned based on the maximization of the cumulative reward obtained during the learning process. Since the motion is based on the cumulative reward, the proposed algorithm is not bound to any embedded model of the robot and is not being affected by uncertainties of these models or estimators, making it possible to generate trajectories with the consideration of non-holonomic constrains. This paper explains the proposed methodology and discusses the setup of experiments and the results for the validation of the methodology in scenarios with dynamic obstacles. A comparison between the reinforcement learning algorithm and state-of-the-art approaches is also carried out to highlight how the methodology proposed outperforms state-of-the-art algorithms.

315、Task space control for on-orbit space robotics using a new ROS-based
   framework
Abstract： This paper proposes several task space control approaches for complex on-orbit high degrees of freedom robots. These approaches include redundancy resolution and take the non-linear dynamic model of the on-orbit robotic systems into account. The suitability of the proposed task space control approaches is explored in several on-orbit servicing operations requiring visual servoing tasks of complex humanoid robots. A unified open-source framework for space-robotics simulations, called OnOrbitROS, is used to evaluate the proposed control systems and compare their behaviour with state-of-the-art existing ones. The adopted framework is based on ROS and includes and reproduces the principal environmental conditions that eventual space robots and manipulators could experience in an on-orbit servicing scenario. The architecture of the different software modules developed and their application on complex space robotic systems is presented. Efficient real-time implementations are achieved using the proposed OnOrbitROS framework. The proposed controllers are applied to perform the guidance of a humanoid robot. The robot dynamics are integrated into the definition of the controllers and an analysis of the results and practical properties are described in the results section.


317、Navigation of Multiple Disk-Shaped Robots with Independent Goals within
   Obstacle-Cluttered Environments
AB In this work, we propose a hybrid control scheme to address the navigation problem for a team of disk-shaped robotic platforms operating within an obstacle-cluttered planar workspace. Given an initial and a desired configuration of the system, we devise a hierarchical cell decomposition methodology which is able to determine which regions of the configuration space need to be further subdivided at each iteration, thus avoiding redundant cell expansions. Furthermore, given a sequence of free configuration space cells with an arbitrary connectedness and shape, we employ harmonic transformations and harmonic potential fields to accomplish safe transitions between adjacent cells, thus ensuring almost-global convergence to the desired configuration. Finally, we present the comparative simulation results that demonstrate the efficacy of the proposed control scheme and its superiority in terms of complexity while yielding a satisfactory performance without incorporating optimization in the selection of the paths.


319、FusionPortableV2: A unified multi-sensor dataset for generalized SLAM
   across diverse platforms and scalable environments
Abstract： Simultaneous Localization and Mapping (SLAM) has been widely applied in various robotic missions, from rescue operations to autonomous driving. However, the generalization of SLAM algorithms remains a significant challenge, as current datasets often lack scalability in terms of platforms and environments. To address this limitation, we present FusionPortableV2, a multi-sensor SLAM dataset featuring sensor diversity, varied motion patterns, and a wide range of environmental scenarios. Our dataset comprises 27 sequences, spanning over 2.5 hours and collected from four distinct platforms: a handheld suite, a legged robot, an unmanned ground vehicle (UGV), and a vehicle. These sequences cover diverse settings, including buildings, campuses, and urban areas, with a total length of 38.7 km. Additionally, the dataset includes ground truth (GT) trajectories and RGB point cloud maps covering approximately 0.3 km2. To validate the utility of our dataset in advancing SLAM research, we assess several state-of-the-art (SOTA) SLAM algorithms. Furthermore, we demonstrate the dataset's broad application beyond traditional SLAM tasks by investigating its potential for monocular depth estimation. The complete dataset, including sensor data, GT, and calibration details, is accessible at https://fusionportable.github.io/dataset/fusionportable_v2.

320、CyberCortex.AI: An AI-based operating system for autonomous robotics and
   complex automation
Abstract： The underlying framework for controlling autonomous robots and complex automation applications is Operating Systems (OS) capable of scheduling perception-and-control tasks, as well as providing real-time data communication to other robotic peers and remote cloud computers. In this paper, we introduce CyberCortex.AI, a robotics OS designed to enable heterogeneous AI-based robotics and complex automation applications. CyberCortex.AI is a decentralized distributed OS which enables robots to talk to each other, as well as to High Performance Computers (HPC) in the cloud. Sensory and control data from the robots is streamed toward HPC systems with the purpose of training AI algorithms, which are afterwards deployed on the robots. Each functionality of a robot (e.g., sensory data acquisition, path planning, motion control, etc.) is executed within a so-called DataBlock of Filters shared through the internet, where each filter is computed either locally on the robot itself or remotely on a different robotic system. The data is stored and accessed via a so-called Temporal Addressable Memory (TAM), which acts as a gateway between each filter's input and output. CyberCortex.AI has two main components: (i) the CyberCortex.AI.inference system, which is a real-time implementation of the DataBlock running on the robots' embedded hardware, and (ii) the CyberCortex.AI.dojo, which runs on an HPC computer in the cloud, and it is used to design, train and deploy AI algorithms. We present a quantitative and qualitative performance analysis of the proposed approach using two collaborative robotics applications: (i) a forest fires prevention system based on an Unitree A1 legged robot and an Anafi Parrot 4K drone, as well as (ii) an autonomous driving system which uses CyberCortex.AI for collaborative perception and motion control.

321、Semantic map construction approach for human-robot collaborative
   manufacturing
Abstract： Map construction is the initial step of mobile robots for their localization, navigation, and path planning in unknown environments. Considering the human-robot collaboration (HRC) scenarios in modern manufacturing, where the human workers' capabilities are closely integrated with the efficiency and precision of robots in the same workspace, a map integrating geometric and semantic information is considered as the technical foundation for intelligent interactions between human workers and robots, such as motion planning, reasoning, and context-aware decision-making. Although different map construction methods have been proposed for mobile robots' perception in the working environment, it is still a challenging task when applied to such human-robot collaborative manufacturing scenarios to achieve the afore-mentioned intelligent interactions between human workers and robots due to the poor integration of semantic information in the constructed map. On the one hand, due to the lack of ability for differentiating the dynamic objects, the mobile robot might sometimes wrongly use the dynamic objects as the spatial references to calculate the pose transformation between the two successive frames, which negatively affects the accuracy of the robot's localization and pose estimation. On the other hand, the map that integrates both the geometric and semantic information can hardly be constructed in real-time, which cannot provide an effective support for the real-time reasoning and decision making during the humanrobot collaboration process. This study proposes a novel map construction approach containing semantic information generation, geometric information generation, and semantic & geometric information fusion modules, which enables the integration of the semantic and geometric information in the constructed map. First, the semantic information generation module analyzes the captured images of the dynamic working environment, eliminates the features of dynamic objects, and generates the semantic information of the static objects. Meanwhile, the geometric information generation module is adopted to generate the accurate geometric information of the robot's motion plane by using the environment data. Finally, a map integrating semantic and geometric information in real-time can be constructed by the semantic & geometric fusion module. The experimental results demonstrate the effectiveness of the proposed semantic map construction approach.

322、An Open-Source UAV Platform for Swarm Robotics Research: Using
   Cooperative Sensor Fusion for Inter-Robot Tracking
Abstract： In this work, we present an open-source unmanned aerial vehicle (UAV) platform for research in swarm robotics. In swarm robotics, groups of robots collaborate using local interactions and collectively solve tasks beyond an individual robot's capabilities. Individual robots must have onboard processing, communication, and sensing capabilities to autonomously react to their neighbors and immediate environment. Most research involving UAVs in swarm robotics presents only simulation results, while key landmark studies with real UAV swarms have used UAV platforms that were custom-built for the respective study. One important reason for this is that no commercial UAV platform comes pre-equipped with the ability to identify and track the positions and poses of nearby drones using only onboard sensors and computation, and in research platforms, the relevant sensing technologies are currently under development. Our aim is to provide a platform that allows swarm robotics researchers to test their algorithms on real UAVs, without having to develop their own custom-built UAVs or to wait until more advanced sensing technology is ready off-the-shelf. We provide a well-documented, entirely open-source UAV platform- S-drone (Swarm-drone)-to foster and support UAV swarm research in a laboratory environment. The S-drone uses fiducial markers in the environment and cooperative feature-level sensor fusion for inter-robot tracking to track the presence, identity, relative 2D position, and relative 2D orientation of neighboring peers. The S-drone is suitable for a wide range of contexts, supports quad-camera vision-based navigation and a variety of onboard sensing, and is extensible. It is especially suited for swarm robotics research because it can operate using strictly onboard processing and sensing without the need for global positioning systems, motion capture systems, or ground stations for off-board sensing.

323、AONeuS: A Neural Rendering Framework for Acoustic-Optical Sensor Fusion
Abstract： Underwater perception and 3D surface reconstruction are challenging problems with broad applications in construction, security, marine archaeology, and environmental monitoring. Treacherous operating conditions, fragile surroundings, and limited navigation control often dictate that submersibles restrict their range of motion and, thus, the baseline over which they can capture measurements. In the context of 3D scene reconstruction, it is well-known that smaller baselines make reconstruction more challenging. Our work develops a physics-based multimodal acoustic-optical neural surface reconstruction framework (AONeuS) capable of effectively integrating high-resolution RGB measurements with lowresolution depth-resolved imaging sonar measurements. By fusing these complementary modalities, our framework can reconstruct accurate high-resolution 3D surfaces from measurements captured over heavily-restricted baselines. Through extensive simulations and in-lab experiments, we demonstrate that AONeuS dramatically outperforms recent RGB-only and sonar-only inverse-differentiablerendering-based surface reconstruction methods.

324、ROG-Map: An Efficient Robocentric Occupancy Grid Map for Large-scene and
   High-resolution LiDAR-based Motion Planning
Abstract： Recent advances in LiDAR technology have opened up new possibilities for robotic navigation. Given the widespread use of occupancy grid maps (OGMs) in robotic motion planning, this paper aims to address the challenges of integrating LiDAR with OGMs. To this end, we propose ROG-Map, a uniform grid-based OGM that maintains a local map moving along with the robot to enable efficient map operation and reduce memory costs for large-scene autonomous flight. Moreover, we present a novel incremental obstacle inflation method that significantly reduces the computational cost of inflation. The proposed method outperforms state-of-the-art methods on various public datasets. To demonstrate the effectiveness and efficiency of ROG-Map, we integrate it into a complete quadrotor system and perform autonomous flights against both small obstacles and large-scale scenes. During real-world flight tests with a 0.05m resolution local map and 30mx30mx6m local map size, ROG-Map takes only 29.8% of frame time on average to update the map at a frame rate of 50 Hz (i.e., 5.96 ms in 20 ms), including 0.33% (i.e., 0.66 ms) to perform obstacle inflation, which represents only half of the total map updating time when compared to the state-of-the-art baseline. We release ROG-Map as an open-source ROS package(1) to promote the development of LiDAR-based motion planning.

325、An End-to-End Robotic Visual Localization Algorithm Based on Deep
   Learning
Abstract： Efficient localization plays a significant role in mobile autonomous robots' navigation systems. Traditional visual simultaneous localization systems based on point feature matching suffer from two shortcomings. First one is that the method of tracking features is not robust for environments with frequent changes in brightness. Another one is the large of consecutive visual keyframes can consume expensive computational and storage resources in complex environments. To solve these problems, an end-to-end real-time six degrees of freedom object pose estimation algorithm is proposed to solve the robust and efficient challenges through a deep learning model. First, preprocessing operations such as cropping, averaging, and timestamp alignment are performed on datasets to reduce computational cost and time. Second, the processed dataset is fed into our neural network model to extract the most effective features for matching. Finally, the robot's current 3D translation and 4D angle information are predicted and output to achieve an end-to-end localization system. A broad range of experiments are performed on both indoor and outdoor datasets. The experimental results demonstrate that the translation and orientation accuracy in outdoor scenes improved by 32.9% and 31.4%, respectively. The average improvement of localization accuracy in indoor scenes is 38.4%, and the angle improvement is 13.1%. Moreover, the effectiveness of predicting the global motion trajectories of sequential images algorithm has been verified and is superior to other convolutional neural network methods.

326、Monocular vision guided deep reinforcement learning UAV systems with
   representation learning perception
Abstract： In recent years, numerous studies have applied deep reinforcement learning (DRL) algorithms to vision-guided unmanned aerial systems. However, DRL is not good at training deep networks in an end-to-end manner due to data inefficiency and lack of direct supervision signals. This paper provides a visual information dimension reduction scheme with representation learning as the visual perception module, which reduces the dimensions of high-dimensional visual information and retains its features related to UAV navigation. Combining such state representation learning with the DRL model can effectively reduce the complexity of the neural network required by DRL. Based on this scheme, we design three motion control models with a monocular camera as the main sensor and train them to control UAVs for obstacle avoidance tasks in a simulated environment. Experiments show that all these models achieve high obstacle avoidance ability after a certain period of training. In addition, one of them also enables the monocular vision guidance system to avoid obstacles in the blind spot of side vision.

327、Simulation of Autonomous Driving for a Line-Following Robotic Vehicle:
   Determining the Optimal Manoeuvring Mode
Abstract： Mobile robotic systems offer valuable test platforms due to their shared features with autonomous vehicles, including features such as sensor technologies, navigation algorithms, and control systems. However, constraints in laboratory environments or technical resources, along with the need for extensive testing, often necessitate the use of virtual test laboratories. While line-following is a widely preferred application in mobile robotics, research on this topic within virtual laboratories is limited. This study pioneers the use of a car-like robotic vehicle in conducting line-following tests within a virtual laboratory environment. To facilitate these tests, a virtual simulator was developed to meet the requirements of realistic simulations. This simulator includes simulated elements, such as roads and environmental features, along with virtual sensors designed to collect and process dynamic motion data. An exceptional aspect of this study is its ability to collect consistent dynamic travel data by sampling realistic sensor information within a virtual environment. The developed linefollowing algorithm employs a controller to minimise lateral deviation while the robotic vehicle follows a road line during its movement. The study conducted virtual driving tests using two different manoeuvre modes on four distinct road segments, exploring how the manoeuvring style influences the driving quality. It was demonstrated that in the low manoeuvre mode, the ride is more comfortable, but exhibits a greater route deviation due to reduced oscillation, while the high manoeuvre mode exhibits the opposite behaviour.

328、Visibility-Aware Navigation Among Movable Obstacles
Abstract： In this paper, we examine the problem of visibility-aware robot navigation among movable obstacles (VANAMO). A variant of the well-known NAMO robotic planning problem, VANAMO puts additional visibility constraints on robot motion and object movability. This new problem formulation lifts the restrictive assumption that the map is fully visible and the object positions are fully known. We provide a formal definition of the VANAMO problem and propose the Look and Manipulate Backchaining (LAMB) algorithm for solving such problems. LAMB has a simple vision-based interface that makes it more easily transferable to real-world robot applications and scales to the large 3D environments. To evaluate LAMB, we construct a set of tasks that illustrate the complex interplay between visibility and object movability that can arise in mobile base manipulation problems in unknown environments. We show that LAMB outperforms NAMO and visibility-aware motion planning approaches as well as simple combinations of them on complex manipulation problems with partial observability.

329、On Motion Blur and Deblurring in Visual Place Recognition
Abstract： Visual Place Recognition (VPR) in mobile robotics enables robots to localize themselves by recognizing previously visited locations using visual data. While the reliability of VPR methods has been extensively studied under conditions such as changes in illumination, season, weather and viewpoint, the impact of motion blur is relatively unexplored despite its relevance not only in rapid motion scenarios but also in low-light conditions where longer exposure times are necessary. Similarly, the role of image deblurring in enhancing VPR performance under motion blur has received limited attention so far. This letter bridges these gaps by introducing a new benchmark designed to evaluate VPR performance under the influence of motion blur and image deblurring. The benchmark includes three datasets that encompass a wide range of motion blur intensities, providing a comprehensive platform for analysis. Experimental results with several well-established VPR and image deblurring methods provide new insights into the effects of motion blur and the potential improvements achieved through deblurring. Building on these findings, the letter proposes adaptive deblurring strategies for VPR, designed to effectively manage motion blur in dynamic, real-world scenarios.

330、S3E: A Multi-Robot Multimodal Dataset for Collaborative SLAM
AB The burgeoning demand for collaborative robotic systems to execute complex tasks collectively has intensified the research community's focus on advancing simultaneous localization and mapping (SLAM) in a cooperative context. Despite this interest, the scalability and diversity of existing datasets for collaborative trajectories remain limited, especially in scenarios with constrained perspectives where the generalization capabilities of Collaborative SLAM (C-SLAM) are critical for the feasibility of multi-agent missions. Addressing this gap, we introduce S3E, an expansive multimodal dataset. Captured by a fleet of unmanned ground vehicles traversing four distinct collaborative trajectory paradigms, S3E encompasses 13 outdoor and 5 indoor sequences. These sequences feature meticulously synchronized and spatially calibrated data streams, including 360-degree LiDAR point cloud, high-resolution stereo imagery, high-frequency inertial measurement units (IMU), and Ultra-wideband (UWB) relative observations. Our dataset not only surpasses previous efforts in scale, scene diversity, and data intricacy but also provides a thorough analysis and benchmarks for both collaborative and individual SLAM methodologies.

331、HE-Nav: A High-Performance and Efficient Navigation System for
   Aerial-Ground Robots in Cluttered Environments
Abstract： Existing AGR navigation systems have advanced in lightly occluded scenarios (e.g., buildings) by employing 3D semantic scene completion networks for voxel occupancy prediction and constructing Euclidean Signed Distance Field (ESDF) maps for collision-free path planning. However, these systems exhibit suboptimal performance and efficiency in cluttered environments with severe occlusions (e.g., dense forests or tall walls), due to limitations arising from perception networks' low prediction accuracy and path planners' high computational overhead. In this letter, we present HE-Nav, the first high-performance and efficient navigation system tailored for AGRs operating in cluttered environments. The perception module utilizes a lightweight semantic scene completion network (LBSCNet), guided by a bird's eye view (BEV) feature fusion and enhanced by an exquisitely designed SCB-Fusion module and attention mechanism. This enables real-time and efficient obstacle prediction in cluttered areas, generating a complete local map. Building upon this completed map, our novel AG-Planner employs the energy-efficient kinodynamic A* search algorithm to guarantee planning is energy-saving. Subsequent trajectory optimization processes yield safe, smooth, dynamically feasible and ESDF-free aerial-ground hybrid paths. Extensive experiments demonstrate that HE-Nav achieved 7x energy savings in real-world situations while maintaining planning success rates of 98% in simulation scenarios.

332、Enhancing Mobile Robot Navigation: Optimization of Trajectories through
   Machine Learning Techniques for Improved Path Planning Efficiency
Abstract： Efficient navigation is crucial for intelligent mobile robots in complex environments. This paper introduces an innovative approach that seamlessly integrates advanced machine learning techniques to enhance mobile robot communication and path planning efficiency. Our method combines supervised and unsupervised learning, utilizing spline interpolation to generate smooth paths with minimal directional changes. Experimental validation with a differential drive mobile robot demonstrates exceptional trajectory control efficiency. We also explore Motion Planning Networks (MPNets), a neural planner that processes raw point-cloud data from depth sensors. Our tests demonstrate MPNet's ability to create optimal paths using the Probabilistic Roadmap (PRM) method. We highlight the importance of correctly setting parameters for reliable path planning with MPNet and evaluate the algorithm on various path types. Our experiments confirm that the trajectory control algorithm works effectively, consistently providing precise and efficient trajectory control for the robot.

333、ADM-SLAM: Accurate and Fast Dynamic Visual SLAM with Adaptive Feature
   Point Extraction, Deeplabv3pro, and Multi-View Geometry
Abstract： Visual Simultaneous Localization and Mapping (V-SLAM) plays a crucial role in the development of intelligent robotics and autonomous navigation systems. However, it still faces significant challenges in handling highly dynamic environments. The prevalent method currently used for dynamic object recognition in the environment is deep learning. However, models such as Yolov5 and Mask R-CNN require significant computational resources, which limits their potential in real-time applications due to hardware and time constraints. To overcome this limitation, this paper proposes ADM-SLAM, a visual SLAM system designed for dynamic environments that builds upon the ORB-SLAM2. This system integrates efficient adaptive feature point homogenization extraction, lightweight deep learning semantic segmentation based on an improved DeepLabv3, and multi-view geometric segmentation. It optimizes keyframe extraction, segments potential dynamic objects using contextual information with the semantic segmentation network, and detects the motion states of dynamic objects using multi-view geometric methods, thereby eliminating dynamic interference points. The results indicate that ADM-SLAM outperforms ORB-SLAM2 in dynamic environments, especially in high-dynamic scenes, where it achieves up to a 97% reduction in Absolute Trajectory Error (ATE). In various highly dynamic test sequences, ADM-SLAM outperforms DS-SLAM and DynaSLAM in terms of real-time performance and accuracy, proving its excellent adaptability.

334、End to end navigation stack for nuclear power plant inspection with
   mobile robot
Abstract： This paper describes a novel approach for nuclear facility inspection with novel automated 3D mapping system as an open source end to end navigation stack available at https://github.com/JanuszBedkowski/msas_enrich_ 2023. Incidents such as Fukushima, Majak or Chernobyl as well as the decommissioning and dismantling of old nuclear facilities ( e.g. Sellafield, Asse or Murmansk) are showing great importance of the robotic technology. Rapid inspection requires reliable, accurate, precise and repeatable simultaneous localization and mapping. Proposed SLAM approach uses only non repetitive scanning pattern Lidar (Livox Mid360) and integrated inertial measurement unit. The novelty is based on feature less single core SLAM implementation. It fuses Normal Distributions Transform and motion model for simultaneous map building and current pose estimation. Motion model bounds an optimization result, thus it is stable and reliable. It requires less than 10 ms for pose update, trajectory tracking and emergency behavior. This method is a candidate for real time application since a calculation time is bounded and it uses only one core of Intel Celeron CPU G1840 2.8 GHz. It was tested both (i) during EnRicH 2023 https://enrich.european-robotics.eu/ - the European robotics hackathon, (ii) in laboratory conditions. This open source project provides also software of base station, thus it is first end to end solution available in literature.

335、Camera Pose Optimization for 3D Mapping
Abstract： Digital 3D models of environments are of great value in many applications, but the algorithms that build them autonomously are computationally expensive and require a considerable amount of time to perform this task. In this work, we present an active simultaneous localisation and mapping system that optimises the pose of the sensor for the 3D reconstruction of an environment, while a 2D Rapidly-Exploring Random Tree algorithm controls the motion of the mobile platform for the ground exploration strategy. Our objective is to obtain a 3D map comparable to that obtained using a complete 3D approach in a time interval of the same order of magnitude of a 2D exploration algorithm. The optimisation is performed using a ray-tracing technique from a set of candidate poses based on an uncertainty octree built during exploration, whose values are calculated according to where they have been viewed from. The system is tested in diverse simulated environments and compared with two different exploration methods from the literature, one based on 2D and another one that considers the complete 3D space. Experiments show that combining our algorithm with a 2D exploration method, the 3D map obtained is comparable in quality to that obtained with a pure 3D exploration procedure, but demanding less time.

336、Optimization of Energy Efficiency with a Predictive Dynamic Window
   Approach for Mobile Robot Navigation
Abstract： This study introduces an enhanced Predictive Dynamic Window Approach (P-DWA), developed as an offline trajectory planner for simulation-based analysis. The algorithm predicts nine candidate trajectories per iteration, evaluates their temporal and kinematic feasibility, and selects the top three based on energy efficiency. Results show an average reduction of approximately 9% in energy consumption compared to the traditional P-DWA, while maintaining efficient computational performance with average iteration times ranging from 15.6 ms to 18.5 ms. However, this gain in energy efficiency typically requires more iterations to complete a path, reflecting the algorithm's more conservative motion strategy. The trade-off between energy savings and total simulation time underscores the value of this approach for testing sustainable navigation strategies. Overall, the proposed P-DWA provides a valuable tool for offline trajectory generation in autonomous mobile robotics, supporting energy-aware path planning under controlled simulation environments.


337、Towards Efficient Robotic Software Development by Reusing Behavior Tree Structures for Task Planning Paradigms
Nowadays, autonomous robots are expected to accomplish more complex tasks and operate in an open-world environment with uncertainties. Developing software for such robots involves the design of task planning paradigms and the implementation of robotic software architectures, making software development rather tricky and time-consuming. In recent decades, component-based software development approaches have been increasingly adopted in robotics to improve software development efficiency by reusing data and controlling flows between components. However, few works have tackled the more critical issue of reusing complex high-level task planning paradigms and robotic software architectures. To make up for the limitation, this paper first identifies the mainstream task planning paradigms and proposes a set of novel patterns for interaction pipelines between the robotic functions of sensing, planning, and acting. Then this paper presents a novel Behavior Tree (BT) based development framework Structural-BT, which provides a set of reusable BT structures that implement abstract interaction pipelines while maintaining interfaces for task-specific customization. The Structural-BT framework supports the modular design of structure functionalities and allows easy extensibility of the inner planning flows between BT components. With the Structural-BT framework, software engineers can develop robotic software by flexibly composing BT structures to formulate the skeleton software architecture and implement task-specific algorithms when necessary. In the experiment, this paper develops robotic software for diverse task scenarios and selects the baseline approaches of Robot Operating System (ROS) and classical BT development frameworks for comparison. By quantitatively measuring the reuse frequencies and ratios of BT structures, the Structural-BT framework has been shown to be more efficient than the baseline approaches for robotic software development.