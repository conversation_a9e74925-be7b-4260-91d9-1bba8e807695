1. 引言 (Introduction)
1.1 土木基礎設施老化的維護挑戰
1.2 傳統裂縫檢測方法的局限性
1.3 機器人自主檢測的技術優勢
1.4 本文的綜述範圍與結構安排
2. 裂縫視覺感知的核心算法 (Core Algorithms for Visual Crack Perception)
2.1 基於目標檢測的裂縫定位模型
2.2 基於語義分割的裂縫形態提取模型
3. 機器人檢測平台與傳感器載荷 (Robotic Inspection Platforms and Sensor Payloads)
3.1 移動平台 (Mobile Platforms)
3.1.1 空中機器人
3.1.2 爬壁及地面機器人
3.2 傳感器載荷 (Sensor Payloads)
3.2.1 視覺成像傳感器
3.2.2 無損檢測與定位傳感器
4. 機器人自主檢測的系統集成策略 (System Integration Strategies for Robotic Autonomous Inspection)
4.1 自主導航與路徑規劃策略
4.2 多模態傳感器數據融合策略
4.3 檢測結果的數字化呈現策略
5. 挑戰與未來發展 (Challenges and Future Directions)
5.1 當前研究面臨的核心挑戰
5.2 未來值得探索的技術趨勢
6. 總結 (Conclusion)