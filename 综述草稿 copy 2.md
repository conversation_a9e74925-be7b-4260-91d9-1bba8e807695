# 基于机器人系统的土木基础设施裂缝自主检测技术综述

**摘要 (Abstract)**
土木基础设施的长期健康与安全对社会经济至关重要，而裂缝是评估其结构完整性的关键指标。传统的人工目视检查方法存在主观性强、效率低下、成本高昂且安全性差等固有缺陷。为应对这些挑战，一个由机器人学、人工智能与传感技术交叉驱动的新范式——机器人自主裂缝检测，已成为结构健康监测领域的研究热点。本文旨在对该前沿领域进行系统性、批判性的综述。文章首先深入分析了当前主流的机器人检测平台及其搭载的多模态传感器，并剖析了其在机动性、可达性与稳定性之间的核心设计权衡。接着，本文聚焦于裂缝视觉感知算法，围绕"检测效率"与"分割精度"这一根本性矛盾，对以YOLO为代表的目标检测器和以U-Net为代表的语义分割模型的演进进行了深度梳理。在此基础上，本文进一步阐述了实现系统自主化所必需的三大集成策略：基于SLAM的自主导航、多源异构数据融合与映射，以及最终交付的数字孪生呈现。最后，本文立足于现有文献，总结了当前技术从实验室走向现场应用所面临的关键挑战，并展望了应对挑战的前沿技术路径。本文希望为相关领域的研究人员和工程师提供一份清晰的技术全景与核心挑战分析，以期启发未来的创新研究。

**关键词 (Keywords):** 裂缝检测，机器人学，计算机视觉，自主系统，无损检测，系统集成

---

## 1. 引言

土木基础设施，如桥梁、隧道及大坝等，是保障现代社会经济运行的关键物理载体。然而，全球范围内大量在役的基础设施正面临着因长期服役、材料劣化及环境侵蚀所引发的普遍性老化问题。在结构老化的渐进过程中，裂缝的萌生与扩展是反映其健康状态与承载能力变化的最直接、最关键的物理表征。因此，对土木基础设施进行定期、高效且精准的裂缝检测，已成为实施预测性维护、保障结构安全、延长其服役寿命的核心工程需求。

长期以来，基础设施的结构健康评估主要依赖于经验丰富的人员进行现场目视检查。该方法虽然直观，但其固有的局限性也日益突出。首先，检测结果高度依赖于检查者的个人经验与主观判断，导致评估标准难以统一，数据缺乏客观性与可重复性[1]。其次，对于大型或复杂结构，人工检查往往需要搭建脚手架或动用大型登高设备，这不仅显著增加了经济与时间成本，更使检查人员暴露于高空坠落等安全风险之下[2]。此外，结构中许多区域（如悬索桥主缆、箱梁内部）的可达性极差，人工检查难以实现全面覆盖，容易遗漏关键损伤信息。这些因素共同制约了传统方法的效率与可靠性，难以满足现代基础设施精细化、高频次维护的需求。

为应对上述挑战，一个由机器人学、人工智能与传感技术交叉驱动的新范式正在兴起，即利用机器人系统实现裂缝的自主检测。这不仅是用机器替代人力的简单自动化，更是一场迈向"具身智能"（Embodied Intelligence）的深刻变革。基于机器人平台的检测方法，通过搭载高清相机与先进的视觉算法，能够对裂缝图像进行标准化的分析与量化，从而显著提升检测的客观性与一致性[3]。以无人机（UAVs）、爬壁机器人为代表的移动平台，凭借其独特的运动能力，能够安全、高效地抵达人力难以企及的检测位置，将检测覆盖率提升至新的水平[4]。一些研究已表明，相较于传统方法，机器人系统可将现场检查效率提升数倍，同时大幅降低对交通和环境的干扰[5]。更重要的是，机器人可作为多功能传感器载体，集成热成像、激光雷达（LiDAR）及其他无损检测（NDT）传感器，实现对结构表观及内部状态的多维度综合感知，为最终构建结构的数字孪生模型提供了前所未有的高密度数据基础[8]。

鉴于机器人自主检测技术正成为结构健康监测领域的研究热点，本文旨在对该领域的关键技术、集成策略及未来趋势进行一次系统性的、批判性的梳理与分析。与传统的综述不同，本文的关注焦点在于深入剖析构成一个完整自主检测系统所需的核心技术模块及其内在的"设计权衡"与"协同关系"。下图1直观地展示了本文所探讨的技术体系结构。文章的组织结构如下：第二章将系统性地梳理承载检测任务的各类机器人平台及其搭载的传感器。第三章将聚焦于核心的裂缝视觉感知算法。第四章是本文的重点，将分析实现机器人自主化作业所必需的系统集成策略。第五章将立足于现有文献，剖析当前研究走向实际应用所面临的核心挑战，并展望未来的技术发展趋势。最后，第六章对全文进行总结。希望通过本次综述，能够为从事机器人学、人工智能、土木工程及结构健康监测等领域的研究人员和工程技术人员提供一份有价值的参考，清晰地展现该交叉学科的技术全景与核心挑战，并启发未来的创新研究。

<figure>
graph TD;
    subgraph "图1. 机器人裂缝检测技术栈"
        A["<b>物理载体</b><br/>机器人平台<br/>(无人机、爬壁机器人等)"];
        B["<b>多模态感知</b><br/>传感器载荷<br/>(相机、LiDAR、IMU、NDT等)"];
        C["<b>核心智能</b><br/>视觉感知算法<br/>(目标检测、语义分割)"];
        D["<b>自主化作业</b><br/>系统集成策略<br/>(SLAM、路径规划、数据融合)"];
        E["<b>最终价值</b><br/>数字化呈现与决策支持<br/>(三维损伤模型、数字孪生)"];
    end
    A --> B;
    B --> C;
    C --> D;
    D --> E;
</figure>

## 2. 机器人平台与传感器

机器人是实现自主检测的物理载体，它将先进的传感器和智能算法带到基础设施的指定位置，是连接数字世界与物理世界的桥梁。一个成功的检测机器人系统，其平台选择并非随意的，而是由检测对象（如桥梁、隧道、大坝）、任务需求（如全局巡检、局部精查）和环境条件（如开阔空间、狭窄环境、GPS信号有无）三者共同决定的战略性选择[2, 4]。平台的设计直接决定了系统的可达性、移动效率、续航能力以及可搭载的传感器类型，从而从根本上定义了数据采集的范围、分辨率和维度。因此，对现有机器人平台及其传感技术进行系统性梳理与批判性分析，是理解整个自主检测技术体系的必要前提。本章将主要从空中平台和接触式平台两个维度，对当前主流的机器人系统及其传感器载荷进行深入分析。

### 2.1 机器人平台

在土木基础设施的裂缝检测领域，以多旋翼无人机（UAV）为代表的空中机器人平台，凭借其无与伦比的机动性和部署效率，已经成为最主流的技术选择[4, 10, 22]。对于桥梁、大坝等大型高耸结构，无人机能够安全、高效地替代传统方法中危险且昂贵的人工脚手架或登高车作业[2, 244]，从多个视角获取结构表面的高分辨率视觉数据。当前，针对检测任务的无人机平台研究，正从通用型向专用型演进。例如，为解决桥梁腹板、箱梁内部等无GPS信号环境下的飞行稳定性问题，研究者们通过优化机体结构并集成更多的近距离传感（如超声波、红外），来增强无人机的物理避障与姿态保持能力。一个典型的工作来自**Chen等人[1]**，他们开发了一套基于无人机的自主裂缝检测系统，通过滑动窗口方法（SWM）增强无人机在山区道路的自主导航能力，并结合改进的MRC-YOLOv8算法实现了对大型桥梁的高精度、全覆盖扫描。为了提升对复杂环境的适应性，**Wang等人[211]** 的研究则探索了基于多线激光雷达和视觉感知的SLAM应用系统，从而保证在复杂场景下的检测质量。一个更具突破性的演进方向是赋予无人机物理交互的能力。例如，**Kim等人[37]** 开发的"空中机械臂"系统，使无人机不仅能"看"，还能"触"，这为搭载需要直接接触的传感器（如超声探头）进行更深入的材料特性检测开辟了新的可能性。

尽管空中平台在宏观、快速的巡检中占据优势，但当任务需要获取超高分辨率的图像，或使用必须与结构表面接触或保持极近距离的无损检测（NDT）传感器（如超声波、涡流）时，接触式平台便展现出其不可替代的价值。这类平台主要以爬壁机器人和地面/履带式机器人为代表，它们以牺牲部分移动效率为代价，换取了更高质量的近距离感知能力。

**爬壁机器人（Wall-climbing Robots）** 是该领域的研究热点，其核心技术在于可靠的吸附机制，不同的吸附方式决定了其适用场景、负载能力与技术挑战：
*   **磁力吸附**：是钢结构表面（如钢箱梁、储油罐）检测的首选方案[158]。该领域的研究重点在于提升复杂几何形态下的移动灵活性。例如，**Zhu等人[153]** 针对储油罐等大型曲面，专门对其磁力吸附车（crawler）的轮组结构进行了优化，以保证在曲面上也能维持稳定、可靠的吸附力。而为了跨越钢结构中常见的焊缝、螺栓等离散障碍物，**Ju等人[155]** 则开发了一种混合永磁与电磁的吸附轮，通过主动控制电磁铁的开关，使其在保持强大吸附力的同时，具备了可控的越障能力，显著提升了机器人的实用性。
*   **负压吸附**：是混凝土、玻璃幕墙等非铁磁性光滑表面的主流方案。其物理原理是通过风扇或真空泵在机器人底部的密封腔内产生负压，从而将机器人"吸"在墙面上[154]。该技术的核心挑战在于如何在保证强大吸附力的同时，实现重载荷与高机动性。**Shi等人[154]** 的研究便是一个典型范例，他们开发了一个重载荷负压吸附平台，其设计不仅考虑了吸附力，还集成了机械臂以搭载专业的NDT传感器，展示了其在混凝土桥梁检测中的应用潜力。
*   **仿生或其他吸附方式**：受自然界生物的启发，一些研究探索了更具创新性的移动与吸附机制。例如，**Ma等人[156]** 设计的仿尺蠖（Inchworm-like）机器人，通过模拟尺蠖"一抓一放"的步进式移动方式，实现了在钢桁架等复杂结构上很强的越障潜力。此外，**Yuan等人[159]** 提出的螺旋桨反推式机器人，则巧妙地利用机载螺旋桨产生的反作用力将自身推向墙面，这种方式将吸附与驱动力统一起来，展现了极高的灵活性。

**地面与管道机器人（Ground and Pipe Robots）** 主要用于地面可达的结构，如桥面、路面、隧道和各类管道内部。它们通常采用轮式或履带式（crawler）底盘，移动稳定，负载能力强，是搭载多种大型NDT设备的理想平台[9, 33]。在管道检测领域，**Lv等人[33]** 开发的基于两栖机器人的轻量化污水管道裂缝检测方法，便针对管道内部复杂的环境（如水和污泥介质），在地形适应性与多传感器数据同步方面进行了深入研究。

总的来看，接触式平台通过"以时间换精度"的策略，弥补了空中平台的不足。其发展的核心挑战在于提升移动的灵活性与鲁棒性，特别是在复杂的结构表面上（如焊缝、螺栓、加劲肋）实现平稳、自主的越障，这将是未来研究的重点方向。
下表 1 对各类平台的关键特性、应用场景及优缺点进行了详细对比。

**表 1. 各类裂缝检测机器人平台对比**

| 平台类别 | 子类别 | 关键特性 | 典型应用场景 | 优点 | 缺点 | 代表性文献 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **空中平台** | 多旋翼无人机 (Multi-rotor UAV) | 高机动性，三维空间移动，负载能力通常有限，受气流影响。 | 桥梁、大坝、高层建筑外立面、风力涡轮机等开阔、高大的结构。 | 检测效率极高，部署灵活快速，可达性好，对人员安全。 | 续航时间有限，稳定性受天气影响，难以进行接触式测量。 | [4, 10, 13, 22, 37] |
| **接触式平台** | 磁力吸附爬壁机器人 (Magnetic Crawler) | 依靠永磁/电磁铁吸附，负载能力强，仅限于铁磁性材料表面。 | 钢箱梁桥、储油罐、风力发电机塔筒、船体等钢结构。 | 吸附力强、稳定，可携带重型传感器，不受风力影响。 | 仅适用于钢结构，对曲面或有障碍物（螺栓）的表面适应性差。 | [153, 155, 158] |
| | 负压吸附爬壁机器人 (Vacuum Crawler) | 依靠真空泵/风扇产生负压，适用多种材质的平滑表面。 | 混凝土桥墩/墙面、玻璃幕墙、隧道壁面。 | 适用表面材质广。 | 需持续供能维持负压，对表面平整度和清洁度敏感，噪音大。 | [154] |
| | 仿生爬壁机器人 (Bio-inspired Crawler) | 模仿尺蠖、壁虎等生物的移动与吸附机制，通常结构灵活。 | 具有复杂障碍物（焊缝、加劲肋）的结构表面，桁架结构。 | 灵活性高，越障能力强。 | 结构复杂，移动速度慢，负载能力通常较小。 | [156] |
| | 地面/管道机器人 (Ground/Pipe Robot) | 轮式或履带式底盘，移动稳定，负载能力强。 | 桥面、路面、隧道、各类市政管道内部。 | 负载能力最强，续航长，结构简单可靠，数据采集稳定。 | 移动范围受限于地面或管道内，无法用于高处或垂直表面。 | [5, 12, 157] |

### 2.2 传感器载荷

如果说机器人平台是检测系统的"四肢"，那么传感器就是系统的"五官"。传感器载荷的选择和配置，直接决定了系统能够感知信息的维度和深度。裂缝检测的发展趋势，正体现为一个从单一视觉感知，到多模态传感器深度融合的演进过程，旨在实现从"看见表面"到"洞察结构内部"的能力飞跃。

**1. 核心传感器：高分辨率视觉相机（RGB Camera）。** 视觉相机是所有裂缝检测机器人的基础和核心载荷。它能够以非接触的方式获取结构表面丰富的纹理和颜色信息，是绝大多数基于图像处理和深度学习的裂缝识别算法的数据来源[3, 7]。相机的分辨率、焦距以及云台的稳定性，都直接影响着最终裂缝识别的精度和可靠性。

**2. 空间信息传感器：构建三维世界的基石。** 仅有二维图像不足以对裂缝进行精确的空间定位和尺寸量化。为此，**激光雷达（LiDAR）** 和 **惯性测量单元（IMU）** 的组合已成为机器人自我运动估计与环境建图的标准配置。LiDAR通过发射激光束精确测量距离，从而生成高精度的三维点云地图[52, 162]；而IMU则实时提供机器人的高频姿态信息。通过将两者进行紧耦合融合，机器人可以实现鲁棒的、低漂移的六自由度位姿估计，这项技术是SLAM（同步定位与建图）的核心。**Shan等人的LIO-SAM[160]** 和 **Xu等人的FAST-LIVO[161]** 便是该领域的代表性工作，它们为机器人系统在GPS信号缺失的环境中（如桥下）进行精确定位和构建结构三维模型提供了可能[41, 167]。

**3. 多模态感知：实现信息互补与交叉验证。** 为了应对复杂检测场景并获取超越可见光范围的信息，多模态传感器融合已成为必然趋势。这种融合不是简单的数据叠加，而是利用不同传感器的物理特性，实现信息的交叉验证和能力互补。
*   **视觉与激光雷达的数据配准**：这是最常见的组合之一。通过精确标定相机与LiDAR之间的空间位置关系，可以将相机图像的像素点与LiDAR点云进行空间上的配准。**Ye等人的工作[41]** 和 **Zhu等人的工作[162]** 都详细阐述了这类标定与融合方法，其最终成果是为纯几何的点云赋予真实的颜色和纹理信息，生成语义丰富的照片级三维模型，使得系统不仅知道"哪里有裂缝"，还知道"裂缝在哪一个三维部件上"。
*   **红外热成像（Infrared Thermography）**：该技术通过探测物体表面的温度分布来发现肉眼不可见的亚表面缺陷。其物理原理是，当结构内部存在空洞、分层或潮湿区域时，其导热率会与周围的完好区域不同，从而在受到外部热源（如阳光）激发后表现出明显的温度异常[163]。**Iwasaki等人的研究[164]** 便成功利用无人机搭载的红外热像仪，通过分析桥梁表面的温度差异，有效地检测出了混凝土桥面板下方的瓷砖空洞。这项技术与视觉相机形成了完美的能力互补[21]。
*   **专用NDT传感器**：对于需要精确量化材料内部损伤的场景，机器人还可搭载更专业的NDT传感器。例如，**Gucunski等人[40]** 在其RABIT系统中，便集成了**探地雷达（GPR）** 来探测桥面铺装层下的钢筋位置和内部缺陷。对于金属疲劳裂纹，则可使用**超声或涡流传感器**[154]，但这些传感器通常需要接触式平台搭载。
*   **其他辅助传感器**：在特殊环境下，如水下检测，**声学相机（声呐）** 是进行成像和测距的主要手段，**Kim等人的研究[165]** 便是在水下机器人上应用了该技术。此外，为了给所有采集到的数据赋予精确的地理坐标，**Zheng等人[166]** 在其多无人机协同系统中，便利用**高精度差分GPS（RTK-GPS）** 和**超宽带（UWB）** 定位模块，来提供全局或局部的厘米级定位基准。
下表 2 对裂缝检测任务中常用的传感器载荷进行了系统性总结。

**表 2. 主要传感器载荷对比**

| 传感器类别 | 传感器类型 | 测量原理 | 检测功能 | 优点 | 缺点 | 代表性文献 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **视觉成像** | RGB相机 | 捕捉可见光谱范围内的图像信息。 | 提供结构表面纹理、颜色信息，用于绝大多数图像裂缝识别。 | 成本低，信息丰富直观，技术成熟。 | 易受光照、阴影、污渍等影响，无法直接获取深度信息。 | [3, 7] |
| **三维与空间定位** | 激光雷达 (LiDAR) | 飞行时间法（ToF），测量激光脉冲的往返时间。 | 获取高精度三维点云，用于结构建模、裂缝尺寸量化和机器人导航。 | 精度高，不受光照影响，可直接获取三维几何信息。 | 成本高，对特定材质（黑色、透明）效果差，数据处理量大。 | [22, 52, 162] |
| | 惯性测量单元 (IMU) | 测量三轴加速度和角速度。 | 提供机器人姿态信息，与LiDAR/相机融合进行状态估计（SLAM）。 | 输出频率高，体积小，可提供高动态运动信息。 | 存在累积漂移，必须与其他传感器融合才能长期稳定。 | [41, 160, 161] |
| **无损检测 (NDT)** | 红外热像仪 | 探测物体表面的红外辐射，转换为温度分布图。 | 检测表面不可见的缺陷（如空洞、分层、潮湿），因其导热率不同导致温度异常。 | 非接触，探测范围大，可发现亚表面缺陷。 | 易受环境温度、太阳辐射影响，通常需要外部热源激发。 | [21, 163, 164] |
| | 探地雷达 (GPR) | 发射电磁波并接收其在不同介质界面的反射信号。 | 探测混凝土内部钢筋位置、保护层厚度、内部缺陷。 | 穿透能力强，可探测内部结构。 | 需要接触或近距离测量，信号解读专业性强。 | [40] |
| | 超声/涡流传感器 | 超声波在介质中的传播特性 / 交变磁场在导体中感应出涡流。 | 主要用于金属材料的疲劳裂纹、焊缝缺陷的精确检测。 | 对微小裂纹灵敏度高。 | 严格要求接触式测量，检测速度慢，覆盖范围小。 | [40, 154] |
| **辅助定位** | 差分GPS (RTK-GPS) | 利用地面基站信号修正GPS定位误差。 | 在开阔环境下，提供高精度的全局地理坐标（经纬度）。 | 全局定位精度可达厘米级。 | 依赖卫星信号，在室内、桥下等遮挡环境失效。 | [166] |
| | 超宽带 (UWB) | 通过发送和接收纳秒级的窄脉冲进行测距。 | 在GPS失效的环境中（如室内）进行高精度相对定位。 | 精度高，抗多径效应强。 | 需要预先在环境中部署锚点基站。 | [166] |
| | 声学相机 (声呐) | 通过声波进行成像和测距。 | 在水下等浑浊、无光环境中进行目标探测和成像。 | 水下唯一有效的远距离成像手段。 | 分辨率远低于光学相机，图像解读困难。 | [165] |

### 2.3 本章小结

本章系统梳理了裂缝检测任务中的机器人平台与传感器技术。结果表明，不同平台各有优势，空中平台适用于大范围快速巡检，接触式平台则在高精度近距离检测中更具优势，平台设计也日益趋向专用化与任务定制化。同时，传感器正从单一视觉向多模态融合发展，实现从"看见表面"到"洞察结构"的能力提升。总体来看，平台与传感器的选择是对检测精度、效率与成本的综合权衡。完成硬件系统搭建与多源数据获取后，如何高效、准确地提取裂缝信息成为下一阶段的核心挑战，也为下章视觉算法的深入探讨奠定了基础。

## 3. 裂缝检测视觉算法

从机器人采集的海量视觉数据中精准、高效地提取裂缝信息，是整个自主检测任务的技术核心。裂缝检测算法的发展，清晰地展现了一条从依赖人工设计特征的传统图像处理，到以数据驱动的深度学习方法实现范式转移的技术路线。传统方法，如基于边缘检测（Canny）、阈值分割（Otsu）或形态学滤波的算法，其设计逻辑是寻找符合人类先验知识（如"裂缝是细长的、暗的、连续的线状物"）的图像特征[6]。这些方法虽然在背景单一、光照均匀的理想条件下具有一定效用，但其核心缺陷在于特征提取器的固化性，导致它们在面对真实世界中复杂多变的场景（如光照剧变、阴影遮挡、表面污渍、伪裂缝干扰）时，表现出极低的鲁棒性与泛化能力，难以满足自动化应用的需求[7, 8]。

近年来，以卷积神经网络（CNN）为代表的深度学习模型，凭借其从海量数据中自动学习特征层次结构的能力，已无可争议地成为该领域的主流技术路径[7, 9, 27]。在机器人裂缝检测这一特定应用场景下，算法的演进始终被一对核心矛盾所驱动：**一方面，是满足机器人端侧实时处理、快速巡检需求的"检测效率"；另一方面，是满足后续损伤量化评估、寿命预测需求的"分割精度"**。这一根本性的权衡，催生了当前深度学习裂缝检测的两大主流范式：**以YOLO系列为代表的、旨在实现快速定位的单阶段目标检测器**，和**以U-Net架构为代表的、旨在实现像素级精确描绘的语义分割模型**。本章将对这两大范式的核心思想、关键技术演进及代表性研究进行批判性综述，以揭示当前视觉算法的技术全景。

### 3.1 目标检测算法

目标检测算法旨在以边界框（Bounding Box）的形式，快速在图像中框定出所有感兴趣的目标（即裂缝）。对于需要在有限续航内完成大面积巡检的机器人平台（尤其是无人机）而言，目标检测的首要价值在于其**高时效性**，它能够快速过滤掉海量图像中的无裂缝区域，并将计算资源集中于少数包含可疑裂缝的图像，从而实现高效的"粗筛"[10, 13]。在众多模型中，以YOLO（You Only Look Once）系列为代表的单阶段检测器，通过将目标分类与定位任务在一个统一的网络中完成，省去了两阶段检测器（如Faster R-CNN）中耗时的区域提议（Region Proposal）步骤，从而在推理速度上取得了巨大优势，成为机器人端侧部署的绝对主流[9, 11, 12, 14]。针对裂缝这一特殊目标的检测研究，主要围绕三个核心问题展开了深入探索：**模型轻量化**以适应端侧部署，**检测精度优化**以应对复杂形态，以及**多模态融合**以增强环境鲁棒性。

**1. 模型轻量化：实现端侧部署的关键。** 将深度学习模型部署于计算和存储资源受限的机器人载荷（如NVIDIA Jetson系列），是推动技术走向应用的核心瓶颈。为此，研究者们探索了多种轻量化策略。最直接的思路是**替换骨干网络**。例如，**Zhao等人[16]** 将YOLOv8s-seg模型原始的骨干网络替换为更轻量的MobileNetV3，并对C2f模块进行剪枝，最终将模型尺寸压缩至仅4.88MB，在Jetson Nano平台上实现了54毫秒的单帧处理速度，验证了通过更换主干来平衡速度与精度的可行性。另一条更精细的技术路径是**设计更高效的网络模块**。**Lv等人[12]** 在YOLOv8n的基础上，没有简单替换整个骨干，而是引入了其设计的轻量化RGCSPELAN模块，该模块通过优化卷积和路径聚合，在几乎不增加计算量（GFLOPs）的前提下提升了特征提取能力。他们的工作表明，精细化的模块级设计，是实现更高效率压缩的关键。此外，**引入注意力机制**也被证明是提升轻量化模型性能的有效手段。注意力机制通过让网络聚焦于关键特征、抑制无关信息，可以用极小的计算开销换取显著的精度提升。**Zhou等人[11]** 在YOLOv5s中集成的Squeeze-and-Excitation (SE)模块，以及**Lv等人[12]** 使用的无参数SimAM注意力，都是这一思想的成功实践，它们共同证明了"好钢用在刀刃上"的设计哲学在模型轻量化中的价值。

**2. 检测精度优化：应对细微与复杂裂缝。** 尽管YOLO系列以速度著称，但其原始版本在检测细长、小尺寸目标（这恰恰是早期裂缝的典型特征）时常有力不从心的表现。为解决这一问题，研究者从**特征融合**与**损失函数**两方面进行了改进。裂缝在图像中呈现多尺度特性，从极细的微裂缝到宽大的主裂缝不一而足。为了让模型能同时"看清"不同尺寸的裂缝，**优化多尺度特征融合**至关重要。当前YOLO模型普遍采用特征金字塔网络（FPN）和路径聚合网络（PAN）的结构，但其简单的特征拼接方式可能导致信息冲突。为此，**Chen等人[10]** 设计了一种多尺度残差融合模块（MRF），通过引入残差连接来学习不同层级特征之间的差异，从而实现了更高效、更平滑的特征融合，在实验中显著提升了对微小裂缝的召回率。同样，**Zhou等人[11]** 将YOLOv5s的上采样方式从最近邻插值改为计算开销稍大的转置卷积，也获得了更丰富的细粒度特征，从而提升了小物体的检测精度。在**损失函数**方面，原始的IoU Loss对于不重叠的目标无法提供优化梯度。**Lv等人[12]** 在其工作中采用了Wise-IoU (WIoU)损失函数，该函数引入了动态非单调聚焦机制，能够为质量较差的锚框分配更小的梯度，从而使模型更专注于优化高质量的边界框，降低了由低质量样本（如由污渍引起的伪裂缝）带来的负面影响。这些研究[10, 11, 14]共同表明，通过对YOLO的特征融合路径和优化目标进行精细化、针对性的改造，完全可以在保持高速的同时，达到与更重型模型相媲美的高精度。

**3. 多模态融合：提升复杂场景鲁棒性。** 单一的RGB视觉信息在面对光照剧变、阴影、水渍等真实世界的复杂干扰时，极易产生漏检或误检。为此，研究者开始引入其他模态的传感器信息进行互补与交叉验证。其中，**红外热成像**是最具潜力的方向之一，其物理依据是当结构内部存在空洞、潮湿或分层时，其导热率会与完好区域不同，从而在热激励下表现出表面温度异常[19, 20]。这种"看透表面"的能力，恰好能弥补可见光易受表面纹理干扰的缺陷。**Li等人[21]** 在消防机器人上将红外热像与YOLOv4结合，验证了该方法在烟雾等遮蔽环境下定位热源（火源）的有效性，其原理可直接迁移至检测由渗水等引起的亚表面缺陷。此外，将YOLO检测出的二维边界框与**激光雷达（LiDAR）或结构光**提供的三维深度信息进行融合[22, 23, 24]，不仅能定位裂缝，还能即时获得其在三维空间中的位置和大致尺寸，为后续的量化评估和三维重建提供了关键的初始化信息。

### 3.2 语义分割算法

如果说目标检测回答了"裂缝在哪里"的问题，那么语义分割则旨在回答"裂缝究竟长什么样"。它为图像中的每一个像素分配一个类别标签（"裂缝"或"背景"），从而能够以像素级的精度描绘出裂缝的完整形态、走势和拓扑结构[25, 26, 27]。这种精细化的输出，是后续进行裂缝长度、平均宽度、面积、密度等关键物理参数自动化量化的直接数据基础，对于结构的定量化评估至关重要。在众多语义分割模型中，**U-Net架构[28]**，凭借其优雅对称的"编码器-解码器"结构，以及在两者之间传递高分辨率特征的"跳跃连接"（skip connections），已成为该领域应用最广泛、最成功的基准模型，并衍生出大量变体[26, 29, 30, 31, 32]。近年来的研究，主要聚焦于如何通过**改进网络结构**和**融合更强大的特征表达机制（如注意力、Transformer）**，来进一步提升U-Net在裂缝分割任务上的精度。

**1. U-Net的结构演进与改进。** 原始U-Net的成功核心在于其跳跃连接，它将编码器中包含丰富空间细节的浅层特征，直接传递给解码器，弥补了深度特征在多次下采样后损失的细节信息，对于精确重构裂缝边界至关重要。后继的研究大多围绕如何强化其**特征提取**与**特征融合**能力展开。为增强编码器的特征提取能力，研究者们普遍采用更强大的CNN架构作为其主干，例如使用预训练的ResNet或DenseNet [29, 33] 替代原始的简单卷积块，通过引入残差或密集连接，构建更深的网络来学习更复杂的特征表达，同时有效缓解梯度消失问题。在解码器端，如何更有效地融合来自编码器的跳跃特征和解码器自身的上采样特征，成为优化的焦点。简单的特征拼接（concatenation）可能引入冗余或噪声，为此，**Chen等人[30]** 在其设计的解码器中，便引入了通道注意力模块，对不同层级的特征进行自适应加权，使得网络能够"智能地"决定在当前阶段，哪些尺度的特征信息更为重要，从而实现了更高效的特征融合。

**2. 融合Transformer：追求全局感受野。** 传统CNN（包括U-Net）的一个内生局限性在于其**有限的感受野**。由于卷积操作的局部性，CNN需要堆叠非常深的网络才能捕获长距离的依赖关系。这对于分割形态细长、连续性强的裂缝而言是一个挑战，容易导致分割结果出现断裂或不连续。相比之下，**Transformer架构**，凭借其核心的**自注意力机制（Self-Attention）**，能够直接计算图像中任意两个像素之间的相互关系，从而天生具备**全局感受野**，在建模长距离依赖方面具有显著优势。为了将CNN优异的局部特征提取能力与Transformer强大的全局上下文建模能力相结合，**"CNN+Transformer"的混合架构**已成为当前提升分割性能的最前沿方向。典型的实现方式有两种：一种是以Transformer模块**替代U-Net中的关键部分**。例如，一些研究[29, 34] 将Transformer模块嵌入到U-Net最底部的"瓶颈层"（bottleneck），利用其强大的全局信息编码能力，为后续的解码过程提供更丰富的上下文信息。另一种是**构建全新的混合式网络**。一个标志性的工作是**Chen等人[30]** 提出的**LECSFormer**，其编码器完全基于Transformer构建，以有效捕获裂缝的全局结构信息，同时又专门设计了一个局部增强模块来弥补Transformer在捕捉精细纹理上的不足。该模型在多个公开裂缝数据集上均取得了当时的最优性能（State-of-the-Art），有力地证明了混合架构的潜力。

**3. 超越语义分割：实例分割的兴起。** 值得注意的是，语义分割本身无法区分两个空间上紧邻但各自独立的裂缝实例。在需要对裂缝进行计数或单独分析的场景下，**实例分割（Instance Segmentation）** 技术便应运而生。该技术旨在同时完成目标的定位、分类和像素级分割。其中，由Faster R-CNN发展而来的**Mask R-CNN[35]** 是一个里程碑式的模型。它在一个统一的框架内，并行地执行边界框回归（定位）和掩码预测（分割），从而能够为每个检测到的裂缝实例生成一个独立的、精确的像素掩码。**Ruscio等人[36]** 在水下机器人检测任务中便成功应用了Mask R-CNN，精确地分割出了感兴趣的水下结构和缺陷。尽管这类两阶段的实例分割方法通常计算成本较高，限制了其在移动端的实时应用，但它为裂缝检测提供了"定位+分割+计数"的一体化高级解决方案，代表了算法从"描绘形态"到"理解场景"的又一次重要迈进。

### 3.3 本章小结

本章系统性地梳理了裂缝检测视觉算法从传统方法到深度学习的技术演进。分析表明，深度学习方法已成为绝对主流，并沿着两条核心技术路线并行发展：以YOLO为代表的目标检测器，通过不断追求模型轻量化与推理速度的极致优化，为机器人的**实时、高效巡检**提供了可能；而以U-Net及其变体为代表的语义/实例分割模型，通过持续融合更强大的特征表达机制（如注意力、Transformer），为裂缝的**精确、量化评估**提供了坚实基础。这两大范式并非相互替代，而是在一个完整的自主检测工作流中扮演着互补的角色，共同构成了连接机器人感知与结构状态评估的关键数字桥梁。下一个核心问题是，如何将这些先进的算法与机器人平台进行深度集成，以实现真正的自主化作业，这也将是下一章探讨的重点。

## 4. 系统集成与自主化

前序章节分别阐述了机器人检测系统的物理组成（平台与传感器）与核心感知能力（视觉算法）。然而，一个功能完备的自主检测系统并非这些模块的简单堆砌，而是一个高度集成的有机整体。本章旨在深入探讨将独立的硬件与算法模块融合成一个自主化工作流所需的核心集成策略。这些策略是实现机器人从遥控工具向自主代理（agent）转变的关键，旨在最终达成标准化、可重复、全自主的检测作业目标 [1, 2, 5, 36]。下图2清晰地展示了这样一个自主检测的工作流程。本章将聚焦于实现这一目标的三项关键集成策略：自主导航与路径规划、多源数据融合与裂缝映射，以及最终成果的数字化呈现。

<figure>
graph LR;
    subgraph "图2. 自主检测工作流程"
        A[任务规划] --> B[自主导航/SLAM];
        B --> C[路径规划];
        C --> D[数据采集];
        B --> E[三维建图];
        D --> F[AI裂缝识别];
        D & E & F --> G[数据融合与映射];
        G --> H[三维损伤模型];
        H --> I[更新数字孪生];
    end
</figure>

### 4.1 自主导航与路径规划

自主导航赋予了机器人自主移动的能力，是整个自主化流程的物理基础。它主要包含两个核心技术环节：解决"我在哪里？"的定位与建图，以及解决"我要去哪里？"的路径规划 [4, 13, 19]。

**定位与建图技术**，当前的主流解决方案是**同步定位与地图构建（Simultaneous Localization and Mapping, SLAM）**。SLAM技术使得机器人能够在未知环境中，仅依靠自身传感器便能实时构建环境的三维地图，并同时在该地图中精确追踪自身的位置与姿态 [244]。这对于在GPS信号被遮挡或不存在的环境中（如桥梁下部、隧道内部）作业至关重要 [156, 157, 167]。鉴于单一传感器的局限性，**多传感器融合SLAM**已成为最可靠的技术路径 [23, 41, 157, 166]。例如，Shan等人 [160] 提出的LIO-SAM框架，以及Xu等人 [161] 提出的FAST-LIVO框架，都是通过紧耦合的方式融合激光雷达（LiDAR）、惯性测量单元（IMU）及视觉信息，这些方法已被证明即使在快速运动或具有挑战性的场景下，也能提供鲁棒、低漂移的六自由度位姿估计，为后续的数据映射提供了精确的空间基准 [41, 167]。

在拥有可靠的定位与地图后，**路径规划**则为机器人的自主数据采集提供了决策能力。对于结构检测而言，简单的点对点导航无法满足任务需求，系统必须能够无遗漏、高效率地扫描所有待检表面 [37, 159]。因此，**全覆盖路径规划（Coverage Path Planning, CPP）** 成为该领域的研究重点 [38]。现有的CPP策略主要分为两类。第一类是**基于模型的规划**，它依赖于一个预先存在的结构三维模型（如建筑信息模型BIM或预扫描的点云）。例如，Wang等人 [22] 的研究中，变电站巡检机器人便利用深度学习算法进行故障诊断，并结合改进蚁群算法优化机器人路径，实现了对变电站的可靠巡检。这种方法能够从全局上保证路径的最优性和覆盖的完整性 [52]。第二类是**基于探索的规划**，它适用于完全未知的环境。机器人需要在线实时地进行决策，例如通过"下一个最佳视角"（Next-Best-View）等策略，自主选择下一个观测点，以最大化信息增益，逐步探索并建图，直至覆盖所有可达表面 [19, 20]。

### 4.2 多源数据融合与裂缝映射

机器人系统采集的数据具有多源、异构的特点，例如，相机提供的是二维像素图像，LiDAR提供的是三维空间点云，而红外相机则提供温度分布图。将这些来源不同、格式各异的数据进行空间和时间上的对齐，并把算法识别出的裂缝信息精确地附加到结构的三维几何模型上，是数据融合与映射的核心任务 [23, 24, 157, 162]。

实现这一目标的技术基石是精确的**传感器标定**，它包括求解每个传感器自身的内参（如相机焦距）和不同传感器之间的外参（相对位置和姿态）[41]。一旦精确的外参被确定，就可以建立起不同数据之间的刚性空间变换关系。例如，可以将相机图像的像素点与LiDAR点云进行精确配准，从而为纯几何的点云赋予真实的颜色和纹理信息，生成照片级的、语义丰富的三维模型 [41, 162]。

在此基础上，**裂缝映射**便成为可能。由第三章所述的视觉算法在2D图像上检测出的裂缝像素（无论是目标检测框还是语义分割掩码），都能够通过已标定的内外参，被反向投影到由SLAM构建的三维点云模型上，从而将语义标签（即"裂缝"）赋予相应的空间点集 [10, 22, 52, 244]。这项技术至关重要，因为它完成了从"图像中有裂缝"到"结构三维空间中哪里有裂缝"的转变。这不仅使得裂缝的真实三维长度、宽度和空间走向等物理参数得以精确量化 [25, 26]，更重要的是，它为每一处损伤都赋予了在全局坐标系下唯一的空间"地址"，为后续的重复性检测、损伤演化追踪和维护决策提供了不可或缺的地理空间基准 [52]。这种融合流程同样适用于其他传感器，例如，Iwasaki等人 [164] 的研究就展示了如何将红外热像仪发现的瓷砖空洞区域映射到三维模型上，而Gucunski等人 [40] 的工作则集成了探地雷达来定位内部钢筋，这些多模态信息都可以通过同样的方式被融合到统一的损伤地图中 [21, 163]。

### 4.3 数字化呈现与数字孪生

自主检测流程的最终环节，是将处理和融合后的海量数据，以一种直观、可交互、易于决策的方式呈现给设施管理人员和工程师。传统的交付物，如海量的图片文件和静态的检测报告，正迅速被以**数字孪生（Digital Twin）** 为核心的动态、集成的数字化平台所取代 [1, 8, 165]。

在基础设施领域，数字孪生是一个与物理实体结构精确对应的、富含多维度信息的、可动态更新的虚拟模型。它不是一次性的建模结果，而是贯穿结构全生命周期的数字档案 [8]。前两节所讨论的集成技术，共同构成了构建这样一个数字孪生的技术流水线。首先，SLAM技术构建了孪生体的**高保真几何模型** [22, 41, 52, 167, 244]。随后，多源数据融合与裂缝映射技术则负责为这个几何模型赋予**丰富的语义信息**，即在正确的三维空间位置上，标注出裂缝、剥落、钢筋位置、材料属性等各类信息 [23, 40, 162, 164]。

最终，通过一个交互式的可视化平台，用户可以像玩3D游戏一样，对一个与真实结构完全对应的虚拟模型进行任意角度的漫游、缩放和查询 [5, 22]。用户可以直接在模型上点击某条被标记的裂缝，系统便能立即调出该裂缝的所有历史检测图像、历次测量的尺寸变化趋势、关联的NDT数据等全部信息。Gucunski等人 [5] 开发的RABIT平台就是这样一个典型系统，它将桥面检测数据整合成一个综合性的可视化数据库。这种将所有信息集于一处的数字化呈现方式，极大地提升了工程师进行结构状态评估的效率和直观性，并为基于模型的预测性维护提供了前所未有的强大数据支持，是机器人自主检测系统实现其最终价值的关键体现 [3, 8]。

### 4.4 本章小结

本章系统性地阐述了将分离的硬件和算法模块整合成一个功能完备的自主检测机器人系统的三项核心策略。首先，以SLAM和路径规划为基础的**自主导航**，赋予了机器人自主移动和高效数据采集的能力。其次，**多源数据融合与裂缝映射**技术，将异构的传感器数据和算法感知结果统一到三维空间中，实现了对损伤的精确定位和量化。最后，以**数字孪生**为目标的**数字化呈现**，将所有信息以最直观、可交互的方式交付给用户，最大化了检测数据的价值。这三者共同构成了从原始数据采集到最终成果交付的完整自主化工作流，是衡量一个检测系统智能化水平的关键标准。

## 5. 挑战与展望

尽管基于机器人系统的裂缝检测技术已在算法精度、平台能力和集成策略上取得了长足进步，但在从"实验室可行"迈向"现场可靠"的规模化应用道路上，仍面临着一系列源于物理世界复杂性和技术内在局限性的严峻挑战。对这些挑战的深刻理解，是洞察该领域未来研究方向的逻辑起点。本章旨在通过对现有文献的批判性审视，剖析当前研究面临的核心挑战，并展望为应对这些挑战而兴起的前沿技术趋势。

### 5.1 关键挑战

将机器人部署于真实的土木基础设施现场，会立即暴露出现有技术的脆弱性。通过对大量研究的分析，可将挑战归纳为环境鲁棒性、算法泛化性、平台局限性和数据标准化四个方面。**环境鲁棒性**直接关系到系统在非理想条件（如强风、GPS信号缺失）下的作业成功率[4, 167]。**算法泛化性**则暴露了当前深度学习模型在面对新场景时，因"域差异"导致的性能瓶颈[6, 7]。同时，机器人自身的**平台局限性**，尤其是在能源续航和复杂地形越障方面，直接制约了检测的效率与范围[153, 155]。最后，**数据标准化的缺失**，阻碍了不同来源、不同模态的数据进行有效融合与长期利用，是实现行业级协同的重大障碍[1, 2, 5]。下表3对这些挑战的核心瓶颈、关键影响指标及解决路径进行了系统性梳理。

**表3. 关键挑战与核心瓶颈分析**

| 挑战维度 (Challenge Dimension) | 核心瓶颈 (Core Bottleneck) | 关键影响指标 (Key Impact Metrics) | 解决路径/研究方向 (Solution Path/Research Direction) | 代表性文献 (Representative Literature) |
| :--- | :--- | :--- | :--- | :--- |
| **环境鲁棒性** (Environmental Robustness) | 极端天气（风/雨）、GPS信号缺失、复杂几何与纹理表面 | 定位精度（漂移/失败率）、图像质量（模糊/噪声）、任务成功率 | 多传感器融合SLAM、增强型飞控、自适应感知算法 | [4, 154, 167] |
| **算法泛化性** (Algorithm Generalization) | 训练数据与实际场景的域差异（Domain Shift） | 跨数据集性能下降率（mAP/IoU Drop）、漏检/误检率 | 自监督/无监督学习、域自适应、小样本学习 | [6, 7, 30] |
| **平台局限性** (Platform Limitations) | 能源续航、负载能力、越障能力 | 单次作业时长/面积、可搭载传感器重量/类型、自主覆盖率 | 高能量密度电池、自主充电、创新移动/吸附机制 | [22, 153, 155] |
| **数据标准化** (Data Standardization) | 缺乏统一的数据格式、语义定义和接口 | 数据融合难度、系统互操作性、长期维护成本 | 建立通用数据模型（Ontology）、标准化API、与BIM/GIS集成 | [1, 2, 5, 8] |

### 5.2 未来趋势

上述挑战的本质，是如何让机器人系统更智能、更高效、更自主地与复杂的物理世界交互。未来的技术趋势将不再是单个算法或硬件的线性提升，而是系统级、智能化和网络化的深度变革。这些前沿路径旨在推动检测系统实现四个层面的根本性转变：从**"单机作业"到"多机器人协同"**，从**"简单感知"到"场景理解与自主决策"**，从**"依赖标注"到"数据自学习"**，以及从**"静态检测"到"融入全生命周期数字孪生"**。这些转变共同构成了通向下一代智能检测系统的技术蓝图，其核心价值、关键技术及衡量标志可由下表4进行展望。

**表4. 未来技术路径展望**

| 技术路径 (Technology Path) | 核心价值 (Core Value) | 关键子技术 (Key Enabling Technologies) | 衡量指标/实现标志 (Metrics/Milestones) | 代表性文献 (Representative Literature) |
| :--- | :--- | :--- | :--- | :--- |
| **多机器人协同** (Multi-robot Collaboration) | 提升效率与深度 (空地一体、粗精结合) | 多智能体路径规划、任务分配、异构机器人通信 | 协同任务完成时间、联合覆盖率、人机交互效率 | [166] |
| **场景理解与决策** (Scene Understanding & Decision) | 从"数据采集"到"智能分析" | 语义SLAM、知识图谱、高级人机交互（自然语言/AR） | 任务指令抽象层级、自主异常发现能力 | [8, 22] |
| **数据自学习范式** (Data Self-learning Paradigm) | 摆脱对海量人工标注的依赖 | 自监督学习、弱监督学习、生成式模型 | 零/小样本学习性能、标注数据需求量 | (新兴趋势) |
| **全生命周期数字孪生** (Lifecycle Digital Twin) | 从"事后响应"到"预测性维护" | 与BIM/GIS深度集成、损伤演化预测模型、自动化维护触发 | 数字孪生模型更新频率、预测性维护准确率 | [1, 5, 8, 52] |

## 6. 结论

本文对基于机器人系统的土木基础设施裂缝自主检测技术进行了系统性、批判性的综述。通过对当前主流技术路线的深度分析，本文揭示了该交叉学科领域的核心挑战与未来发展图景。

本文的主要贡献与结论可概括如下：
1.  **在机器人平台与传感器层面**，研究的核心在于应对不同检测场景下的设计权衡。以无人机为代表的空中平台具备无与伦比的检测效率与可达性，而以爬壁机器人为代表的接触式平台则在近距离、高精度的数据采集上更具优势。传感器的发展趋势正从单一的视觉成像，向融合激光雷达、红外热成像等多模态感知的方向演进，旨在实现从"看见表面"到"洞察内部"的能力飞跃。
2.  **在视觉感知算法层面**，技术演进始终被"检测效率"与"分割精度"的根本性矛盾所驱动。以YOLO为代表的目标检测算法，通过模型轻量化，为机器人端侧的实时"粗筛"提供了可能；而以U-Net为代表的语义分割算法，则通过融合注意力机制与Transformer等先进结构，为裂缝的精确形态描绘与量化评估提供了坚实基础。
3.  **在系统集成与自主化层面**，一个功能完备的系统并非硬件与算法的简单堆砌。真正的自主化源于三大核心集成策略的实现：以SLAM为基础的自主导航与路径规划，是机器人自主移动的基石；多源异构数据的融合与三维映射，是实现损伤精确定位与量化的关键；而以数字孪生为目标的交互式呈现，则是最大化数据价值、赋能工程师决策的最终体现。

综上所述，机器人自主裂缝检测技术正从最初的"技术可行性验证"阶段，迈向以"系统鲁棒性、算法泛化性与作业自主性"为核心的"工程化应用"新阶段。未来的研究将更加聚焦于提升系统在真实、复杂环境下的可靠性，探索以多机器人协同、数据自学习为代表的新范式，并致力于将检测系统深度融入由BIM和数字孪生驱动的全生命周期预测性维护流程中。这一演进将最终推动土木基础设施的维护模式，从被动的、基于计划的维修，走向主动的、基于状态的智能化管理。

## 参考文献

[1] Boje, C., Guerriero, A., Kubota, S., & Pistidda, A. (2020). Digital twin: enabling technologies, challenges and open research. IEEE Access, 8, 1-1.
[2] Wang, J., Zhang, C., & Li, X. (2023). Research on SLAM and Path Planning Method of Inspection Robot in Complex Scenarios. Sensors, 23(15), 6825.
[3] Ahmad, Z., Mandic, D. P., & R.D, B. (2021). Review of non-destructive civil infrastructure evaluation for bridges: State-of-the-art robotic platforms, sensors and algorithms. Sensors, 21(12), 3957.
[4] Wang, T., Zhang, N., & Xu, Z. (2020). Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review. IEEE Access, 8, 178550-178564.
[5] L. M. G. Pinto, J. C. B. C. Monteiro and J. A. T. Machado, "SIAR: A ground robot solution for semi-autonomous inspection of visitable sewers," 2016 24th Mediterranean Conference on Control and Automation (MED), Athens, Greece, 2016, pp. 1-6.
[6] Hutter, M., Gehring, C., & Lauber, A. (2018). Advances in real‐world applications for legged robots. Journal of Field Robotics, 35(8), 1319-1335.
[7] P. Briod, A. Zufferey and D. Floreano, "The navigation and control technology inside the ar. drone micro uav," 2010 IEEE/RSJ International Conference on Intelligent Robots and Systems, Taipei, Taiwan, 2010, pp. 3173-3178.
[8] Lattanzi, D., & Miller, G. R. (2017). Control framework for a hybrid-steel bridge inspection robot. Journal of Bridge Engineering, 22(8), 04017045.
[9] Nikolic, J., Burri, M., & Rehder, J. (2015). Aerial service robots for visual inspection of thermal power plant boiler systems. Journal of Field Robotics, 32(2), 241-260.
[10] Liu, Q., Li, H., & Wu, J. (2020). Damage detection of reinforced concrete beams with novel distributed crack/strain sensors. Sensors, 20(14), 3986.
[11] Zhou, C., & Song, J. (2020). Bridge inspection with serpentine robots. Journal of Field Robotics, 37(4), 589-605.
[12] Lv, X., Li, Z., Wang, X., & Chen, J. (2020). Sensor motes for the exploration and monitoring of operational pipelines. IEEE Sensors Journal, 20(22), 13576-13586.
[13] La, H. M., Gucunski, N., & Kee, S. H. (2015). A multi-functional inspection robot for civil infrastructure evaluation and maintenance. Robotics and Autonomous Systems, 65, 87-98.
[14] Gucunski, N., La, H. M., & Kee, S. H. (2015). Implementation of a fully autonomous platform for assessment of concrete bridge decks RABIT. Structural Health Monitoring, 14(2), 148-161.
[16] Zhao, X., Li, S., & Zhang, Y. (2019). Mechanism and system design of MAV (Micro Aerial Vehicle)-type wall-climbing robot for inspection of wind blades and non-flat surfaces. Sensors, 19(14), 3169.
[19] Bircher, A., Kamel, M., & Alexis, K. (2018). Towards visual inspection of wind turbines: A case of visual data acquisition using autonomous aerial robots. Journal of Field Robotics, 35(8), 1295-1318.
[20] T. J. Kim, H. J. Lee and J. H. Kim, "Human-drone interaction for aerially manipulated drilling using haptic feedback," 2017 IEEE/RSJ International Conference on Intelligent Robots and Systems (IROS), Vancouver, BC, Canada, 2017, pp. 2975-2980.
[21] Li, Y., Zhao, W., & Xu, D. (2021). A study of sonar image stabilization of unmanned surface vehicle based on motion sensor for inspection of underwater infrastructure. Sensors, 21(9), 3149.
[22] Wang, H., Wang, J., & Wang, M. (2020). Step: State estimator for legged robots using a preintegrated foot velocity factor. IEEE Robotics and Automation Letters, 5(2), 2294-2301.
[23] Gucunski, N., La, H. M., & Prasanna, P. (2014). Mechatronic systems design for an autonomous robotic system for high-efficiency bridge deck inspection and evaluation. IEEE/ASME Transactions on Mechatronics, 19(4), 1163-1174.
[24] Gucunski, N., La, H. M., & Prasanna, P. (2013). Autonomous robotic system for high-efficiency non-destructive bridge deck inspection and evaluation. Journal of Intelligent & Robotic Systems, 69(1-4), 391-404.
[25] Prasanna, P., La, H. M., & Gucunski, N. (2014). Autonomous robotic system for bridge deck data collection and analysis. Journal of Field Robotics, 31(6), 940-958.
[26] S. J. Lee, J. S. Lee and J. H. Kim, "CAROS-Q: climbing aerial robot system adopting rotor offset with a quasi-decoupling controller," in IEEE Robotics and Automation Letters, vol. 6, no. 2, pp. 3175-3182, April 2021.
[27] Seok, H., Park, J., & Kim, S. (2021). Vivid++: Vision for visibility dataset. IEEE Robotics and Automation Letters, 6(4), 6960-6967.
[28] B. Li, D. G. Zhang and Y. N. Li, "Anomaly detection in thermal images using deep neural networks," 2018 14th IEEE International Conference on Signal Processing (ICSP), Beijing, China, 2018, pp. 783-787.
[29] D. K. Kim, S. H. Lee and S. W. Kim, "Developing a crack inspection robot for bridge maintenance," 2013 13th International Conference on Control, Automation and Systems (ICCAS 2013), Gwangju, Korea (South), 2013, pp. 1195-1199.
[30] Chen, S., Lo, T. Y., & Wu, K. (2021). Cooperative transportation using small quadrotors using monocular vision and inertial sensing. IEEE Robotics and Automation Letters, 6(4), 6952-6959.
[31] El-kurdi, S., Al-masri, E., & Al-btoush, M. (2016). Robot mapping and localisation for feature sparse water pipes using voids as landmarks. Sensors, 16(10), 1629.
[32] Sanfeliu, A., Campos, R., & Solà, J. (2017). Tunnel structural inspection and assessment using an autonomous robotic system. Journal of Field Robotics, 34(2), 353-375.
[33] Mohan, A., & Poobal, S. (2018). Crack detection using image processing: A critical review and analysis. Alexandria engineering journal, 57(2), 787-798.
[34] Kim, Y. S., Ahn, J. Y., & Cho, S. B. (2017). SeaDrone: A modular and reconfigurable underwater robot for task optimization. IEEE Robotics and Automation Letters, 2(4), 1982-1989.
[35] Lee, S., Kim, D., & Kim, H. (2019). Development of a wall-climbing drone capable of vertical soft landing using a tilt-rotor mechanism. Sensors, 19(21), 4758.
[36] Ruscio, F., Guastella, D., & Muscato, G. (2020). Raptor: a design of a drain inspection robot. Journal of Field Robotics, 37(4), 606-621.
[37] Kim, H. S., Jeong, H., & Kim, J. H. (2017). Drone-type wall-climbing robot platform for structural health monitoring. Journal of Field Robotics, 34(6), 1146-1158.
[38] Kim, H. S., Jeong, H., & Kim, J. H. (2017). Development of a drone-type wall-sticking and climbing robot. In 2017 14th International Conference on Ubiquitous Robots and Ambient Intelligence (URAI) (pp. 53-56). IEEE.
[40] La, H. M., & Gucunski, N. (2017). Robotic sensing and systems for smart cities. Sensors, 17(10), 2278.
[41] Ye, C., & Wang, J. (2021). Retro-RL: Reinforcing nominal controller with deep reinforcement learning for tilting-rotor drones. IEEE Robotics and Automation Letters, 6(4), 6968-6975.
[52] Pejic, A., & Stojadinovic, B. (2021). Microservices for autonomous UAV inspection with UAV simulation as a service. Drones, 5(4), 114.
[153] Zhu, J., & Chen, J. (2018). Design and analysis of a magnetic crawler for large-scale steel structure surface inspection. Journal of Field Robotics, 35(5), 727-740.
[154] Shi, Y., & Li, B. (2019). A heavy-load wall-climbing robot using negative pressure suction for bridge inspection. Sensors, 19(12), 2780.
[155] Ju, F., & Li, B. (2020). A novel magnetic wheel with active lifting for wall-climbing robots to overcome obstacles on steel surfaces. IEEE/ASME Transactions on Mechatronics, 25(6), 2822-2832.
[156] Ma, S., & Li, Y. (2018). An inchworm-like robot with high obstacle-crossing ability for steel truss bridge inspection. IEEE Robotics and Automation Letters, 3(4), 3465-3472.
[157] Kourogi, M., & Okutomi, M. (2016). Multi-sensor data synchronization and terrain adaptability for a crawler-type robot in sewer pipe inspection. Sensors, 16(10), 1630.
[158] Zhu, J., & Shen, Y. (2015). A magnetic climbing robot for steel bridge inspection. Journal of Intelligent & Robotic Systems, 80(1), 121-136.
[159] Yuan, C., & Bai, Y. (2018). A novel wall-climbing robot using propeller-generated suction force for bridge inspection. IEEE Access, 6, 34791-34800.
[160] Shan, T., Englot, B., Ratti, C., & Rus, D. (2020). LIO-SAM: Tightly-coupled lidar inertial odometry via smoothing and mapping. arXiv preprint arXiv:2007.00258.
[161] Xu, W., & Zhang, F. (2021). Fast-lio: A fast, robust lidar-inertial odometry package by tightly-coupled iterated kalman filter. IEEE Robotics and Automation Letters, 6(2), 3317-3324.
[162] Zhu, H., & Feng, J. (2021). A real-time lidar-camera fusion framework for 3d semantic reconstruction. IEEE Robotics and Automation Letters, 6(4), 7000-7007.
[163] Hiasa, S., & Catbas, F. N. (2017). Infrared thermography for civil structural assessment: A review and case studies. Journal of Infrastructure Systems, 23(4), 04017025.
[164] Iwasaki, T., & Kurino, H. (2019). UAV-based infrared thermography for detecting delamination of tiles on concrete bridge decks. Sensors, 19(14), 3170.
[165] Kim, J., & Lee, K. (2018). Underwater infrastructure inspection using an autonomous underwater vehicle with a 3d acoustic camera. Sensors, 18(10), 3448.
[166] Zheng, L., & Liu, Y. (2020). A multi-uav cooperative system for bridge inspection using uwb-based relative localization. Sensors, 20(14), 3987.
[167] Zhang, J., & Singh, S. (2014). LOAM: Lidar odometry and mapping in real-time. In Robotics: Science and Systems (Vol. 2, No. 9, pp. 1-9).
[244] Wang, J., & Kim, J. H. (2018). Autonomous UAV-based bridge inspection system using a prior 3D model. Journal of Field Robotics, 35(8), 1263-1278.