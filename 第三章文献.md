【文献 1】
作者: <PERSON><PERSON><PERSON>, GL
来源: BUILDINGS
题目: The Phenomenon of Cracking in Cement Concretes and Reinforced Concrete
摘要: Cracks and cavities belong to two basic forms of damage to the concrete structure, which may reduce the load-bearing capacity and tightness of the structure and lead to failures and catastrophes in construction structures. Excessive and uncontrolled cracking of the structural element may cause both corrosion and weakening of the adhesion of the reinforcement present in it. Moreover, cracking in the structure negatively affects its aesthetics and in extreme cases may cause discomfort to people staying in such a building. Therefore, the following article provides an in-depth review of issues related to the formation and development of damage and cracking in the structure of concrete composites. It focuses on the causes of crack initiation and characterizes their basic types. An overview of the most commonly used methods for detecting and analyzing the shape of microcracks and diagnosing the trajectory of their propagation is also presented. The types of cracks occurring in concrete composites can be divided according to eight specific criteria. In reinforced concrete elements, macrocracks depend on the type of prevailing loads, whereas microcracks are correlated with their specific case. The analyses conducted show that microcracks are usually rectilinear in shape in tensioned elements; in shear elements there are wing microcracks with straight wings; and torsional stresses cause changes in wing microcrack morphology in that the tips of the wings are twisted. It should be noted that the subject matter of microcracks and cracks in concrete and structures made of this material is important in many respects as it concerns, in a holistic approach, the durability of buildings, the safety of people staying in the buildings, and costs related to possible repairs to damaged structural elements. Therefore, this problem should be further investigated in the field of evaluation of the cracking and fracture processes, both in concrete composites and reinforced concrete structures.

【文献 2】
作者: Cha, YJ
来源: AUTOMATION IN CONSTRUCTION
题目: Deep learning-based structural health monitoring
摘要: This article provides a comprehensive review of deep learning-based structural health monitoring (DL-based SHM). It encompasses a broad spectrum of DL theories and applications including nondestructive approaches; computer vision-based methods, digital twins, unmanned aerial vehicles (UAVs), and their integration with DL; vibration-based strategies including sensor fault and data recovery methods; and physics-informed DL approaches. Connections between traditional machine learning and DL-based methods as well as relations of local to global approaches including their extensive integrations are established. The state-of-the-art methods, including their advantages and limitations are presented. The review draws on current literature on the topic, also providing a synergistic analysis leading to the understanding of the evolution of DL as a basis for presenting the future research and development needs. Our overall finding is that despite the rapid progression of digital technology along with the progression of DL, the DL-based SHM appears to be in its infant stages with enormous potential for future developments to bring the SHM technology to a common practical use with wide scope applications, performance reliability, cost, and degree of automation. It is anticipated that this review paper will serve as a basic resource for readers seeking comprehensive and holistic understanding of the subject matter.

【文献 3】
作者: Yu, ZF
来源: SCIENCE OF THE TOTAL ENVIRONMENT
题目: Uptake and transport of micro/nanoplastics in terrestrial plants:
摘要: The pervasive dispersion of micro/nanoplastics in various environmental matrices has raised concerns regarding their potential intrusion into terrestrial ecosystems and, notably, plants. In this comprehensive review, we focus on the interaction between these minute plastic particles and plants. We delve into the current methodologies available for detecting micro/nanoplastics in plant tissues, assess the accumulation and distribution of these particles within roots, stems, and leaves, and elucidate the specific uptake and transport mechanisms, including endocytosis, apoplastic transport, crack-entry mode, and stomatal entry. Moreover, uptake and transport of micro/nanoplastics are complex processes influenced by multiple factors, including particle size, surface charge, mechanical properties, and physiological characteristics of plants, as well as external environmental conditions. In conclusion, this review paper provided valuable insights into the current understanding of these mechanisms, highlighting the complexity of the processes and the multitude of factors that can influence them. Further research in this area is warranted to fully comprehend the fate of micro/nanoplastics in plants and their implications for environmental sustainability.

【文献 4】
作者: Luo, K
来源: SENSORS
题目: Computer Vision-Based Bridge Inspection and Monitoring: A Review
摘要: Bridge inspection and monitoring are usually used to evaluate the status and integrity of bridge structures to ensure their safety and reliability. Computer vision (CV)-based methods have the advantages of being low cost, simple to operate, remote, and non-contact, and have been widely used in bridge inspection and monitoring in recent years. Therefore, this paper reviews three significant aspects of CV-based methods, including surface defect detection, vibration measurement, and vehicle parameter identification. Firstly, the general procedure for CV-based surface defect detection is introduced, and its application for the detection of cracks, concrete spalling, steel corrosion, and multi-defects is reviewed, followed by the robot platforms for surface defect detection. Secondly, the basic principle of CV-based vibration measurement is introduced, followed by the application of displacement measurement, modal identification, and damage identification. Finally, the CV-based vehicle parameter identification methods are introduced and their application for the identification of temporal and spatial parameters, weight parameters, and multi-parameters are summarized. This comprehensive literature review aims to provide guidance for selecting appropriate CV-based methods for bridge inspection and monitoring.

【文献 5】
作者: Abou-Khousa, MA
来源: IEEE TRANSACTIONS ON INSTRUMENTATION AND MEASUREMENT
题目: Detection of Surface Cracks in Metals Using Microwave and
摘要: Integrity assessment of metallic structures requires inspection tools capable of detecting and evaluating cracks reliably. To this end, many microwave and millimeter-wave nondestructive testing and evaluation (NDT&E) methods have been developed and applied successfully in the past. Detection of fatigue cracks with widths less than 5 mu m using noncontact microwave-based inspection methods was demonstrated in the 1970s. Since their introduction, these methods have evolved considerably toward enhancing the detection sensitivity and resolution. Undertaking key application challenges has attracted considerable attention in the past three decades and led to the development of the near-field techniques for crack detection. To address a need that cannot be fulfilled by other NDT&E modalities, innovative noncontact microwave and millimeter-wave NDT&E methods were devised recently to detect cracks of arbitrary orientations under thick dielectric structures. While the reported methods share the same underlying physical principles, they vary considerably in terms of the devised probes/sensors and the application procedure. Consequently, their sensitivity and resolution as well as their limitations vary. This article reviews the various crack detection methods developed to-date and compares them in terms of common performance metrics. This comprehensive review is augmented with experimental comparisons and benchmarking aimed to benefit NDT&E practitioners and researchers alike.

【文献 6】
作者: Wang, XQ
来源: MATTER
题目: Artificial-intelligence-led revolution of construction materials: From
摘要: Industry 4.0 promotes the transformation of manufacturing industry to intelligence, which demands advances in materials, devices, and systems of the construction industry. Researchers of construction materials have incorporated artificial intelligence technology to accelerate these advances. From this perspective, we evaluate the latest advances in applying machine learning to the development of concrete, fiber-reinforced composites, and metals in improving their durability, sustainability, safety, and recyclability. We highlight how artificial intelligence addresses the challenges of material research, emphasizing the peculiarities of the construction industry under the Industry 4.0 framework. Based on the advances in artificial intelligence, we envision integration with Industry 4.0, starting with digitization of construction materials, progressing to advanced manufacturing, and eventually aiming to the level of intelligent application and operation of buildings. A revolutionary future can be envisaged in which design, manufacturing, and application of construction materials involve the meticulous integration of artifi-cial intelligence, big data with all theory, experiments, and compu-tations.

【文献 7】
作者: Eslamlou, AD
来源: CONSTRUCTION AND BUILDING MATERIALS
题目: A review on non-destructive evaluation of construction materials and
摘要: The growing demand towards life cycle sustainability has created a tremendous interest in non-destructive evaluation (NDE) to minimize manufacturing defects and waste, and to improve maintenance and extend service life. Applications of Magnetic Sensors (MSs) in NDE of civil Construction Materials to detect damage and deficiencies have become of great interest in recent years. This is due to their low cost, non-contact data collection, and high sensitivity under the influence of external stimuli such as strain, temperature and humidity. There have been several advancements in MSs over the years for strain evaluation, corrosion monitoring, etc. based on the magnetic property changes. However, these MSs are at their nascent stages of development, and thus, there are several challenges that exist. This paper summarizes the recent advancements in MSs and their applications in civil engineering. Principle functions of different types of MSs are discussed, and their comparative characteristics are presented. The research challenges are highlighted and the main applications and advantages of different MSs are critically reviewed.

【文献 8】
作者: Dilek, E
来源: SENSORS
题目: Computer Vision Applications in Intelligent Transportation Systems: A
摘要: As technology continues to develop, computer vision (CV) applications are becoming increasingly widespread in the intelligent transportation systems (ITS) context. These applications are developed to improve the efficiency of transportation systems, increase their level of intelligence, and enhance traffic safety. Advances in CV play an important role in solving problems in the fields of traffic monitoring and control, incident detection and management, road usage pricing, and road condition monitoring, among many others, by providing more effective methods. This survey examines CV applications in the literature, the machine learning and deep learning methods used in ITS applications, the applicability of computer vision applications in ITS contexts, the advantages these technologies offer and the difficulties they present, and future research areas and trends, with the goal of increasing the effectiveness, efficiency, and safety level of ITS. The present review, which brings together research from various sources, aims to show how computer vision techniques can help transportation systems to become smarter by presenting a holistic picture of the literature on different CV applications in the ITS context.

【文献 9】
作者: Jiang, YD
来源: AUTOMATION IN CONSTRUCTION
题目: Tunnel lining detection and retrofitting
摘要: The underground tunnel structure is important and common in transport infrastructures. With the increasing service time, it is crucial to detect the deteriorations in the ageing tunnel linings and make informed retrofitting decisions to ensure their structural safety and extend their service life cycle. This emphasizes the importance of understanding the framework of tunnel lining detection, evaluation, and retrofitting. However, there is no up-todate review available that covers the entire workflow of tunnel lining detection and retrofitting. This paper provides a comprehensive review of non-destructive testing (NDT) methods, health evaluation methods, and retrofitting methods for tunnel linings. The achievements, challenges, and development trends of these methods are illustrated. Specifically, NDT methods for three representative tunnel lining defects, including cracks, leakage, and voids, are introduced and analyzed to show the corresponding advantages and disadvantages. Based on the data obtained by the defect detection methods, the procedures for lining health status evaluation are also summarized to provide a systematic and quantitative evaluation of tunnel linings. Finally, the retrofitting methods and techniques that are suitable for lining structures are reviewed. This paper provides an insight into the development of structural health monitoring (SHM) and the maintenance of tunnel linings, offering a systematic guide for understanding the framework of tunnel lining detection and retrofitting.

【文献 10】
作者: Mishra, M
来源: JOURNAL OF CULTURAL HERITAGE
题目: Artificial intelligence-assisted visual inspection for cultural
摘要: Applying computer science techniques such as artificial intelligence (AI), deep learning (DL), and computer vision (CV) on digital image data can help monitor and preserve cultural heritage (CH) sites. Defects such as weathering, removal of mortar, joint damage, discoloration, erosion, surface cracks, vegetation, seepage, and vandalism and their propagation with time adversely affect the structural health of CH sites. Several studies have reported damage detection in concrete and bridge structures using AI techniques. However, few studies have quantified defects in CH structures using the AI paradigm, and limited case studies exist for their applications. Hence, the application of AI-assisted visual inspections for CH sites needs to be explored. AI-assisted digital inspections assist inspection professionals and increase confidence levels in the damage assessment of CH buildings. This review summarizes the damage assessment techniques using image processing techniques, focusing mainly on DL techniques applied for CH conservation. Several case study applications of CH buildings are presented where AI can assist in traditional visual inspections. (c) 2024 The Author(s). Published by Elsevier Masson SAS on behalf of Consiglio Nazionale delle Ricerche (CNR). This is an open access article under the CC BY license ( http://creativecommons.org/licenses/by/4.0/ )

【文献 11】
作者: Chen, D
来源: APPLIED SCIENCES-BASEL
题目: A Review of Detection Technologies for Underwater Cracks on Concrete Dam
摘要: Cracks seriously endanger the safe and stable operation of dams. It is important to detect surface cracks in a timely and accurate manner to ensure the safety and serviceability of a dam. The above-water crack detection technology of dams has been widely studied, but due to the complex underwater environment, above-water crack detection technology on dam surfaces cannot be directly applied to underwater crack detection. To adapt to the underwater detection environment and improve the efficiency and accuracy of underwater crack detection, many methods have been proposed for underwater crack detection, including sensor detection and image detection. This paper presents a systematic overview of the development and application practices of existing underwater crack detection technologies for concrete dams, focusing on methods that use underwater robots as underwater mobile carriers to acquire images that are combined with digital image processing algorithms to identify, locate, and quantify underwater cracks in dams. This method has been widely used for underwater crack detection on dam surfaces with the advantages of being non-contact, non-destructive, having high efficiency, and wide applicability. Finally, this paper looks further forward to the development trends and research challenges of detection technologies for underwater cracks on concrete dam surfaces, which will help researchers to complete further studies on underwater crack detection.

【文献 12】
作者: Ding, SH
来源: SENSORS
题目: Acoustic-Signal-Based Damage Detection of Wind Turbine Blades-A Review
摘要: Monitoring and maintaining the health of wind turbine blades has long been one of the challenges facing the global wind energy industry. Detecting damage to a wind turbine blade is important for planning blade repair, avoiding aggravated blade damage, and extending the sustainability of blade operation. This paper firstly introduces the existing wind turbine blade detection methods and reviews the research progress and trends of monitoring of wind turbine composite blades based on acoustic signals. Compared with other blade damage detection technologies, acoustic emission (AE) signal detection technology has the advantage of time lead. It presents the potential to detect leaf damage by detecting the presence of cracks and growth failures and can also be used to determine the location of leaf damage sources. The detection technology based on the blade aerodynamic noise signal has the potential of blade damage detection, as well as the advantages of convenient sensor installation and real-time and remote signal acquisition. Therefore, this paper focuses on the review and analysis of wind power blade structural integrity detection and damage source location technology based on acoustic signals, as well as the automatic detection and classification method of wind power blade failure mechanisms combined with machine learning algorithm. In addition to providing a reference for understanding wind power health detection methods based on AE signals and aerodynamic noise signals, this paper also points out the development trend and prospects of blade damage detection technology. It has important reference value for the practical application of non-destructive, remote, and real-time monitoring of wind power blades.

【文献 13】
作者: Latifi, R
来源: MATERIALS
题目: A Brief Overview on Crack Patterns, Repair and Strengthening of
摘要: Given that a significant fraction of buildings and architectural heritage in Europe's historical centers are masonry structures, the selection of proper diagnosis, technological surveys, non-destructive testing, and interpretations of crack and decay patterns is paramount for a risk assessment of possible damage. Identifying the possible crack patterns, discontinuities, and associated brittle failure mechanisms within unreinforced masonry under seismic and gravity actions allows for reliable retrofitting interventions. Traditional and modern materials and strengthening techniques create a wide range of compatible, removable, and sustainable conservation strategies. Steel/timber tie-rods are mainly used to support the horizontal thrust of arches, vaults, and roofs and are particularly suitable for better connecting structural elements, e.g., masonry walls and floors. Composite reinforcing systems using carbon, glass fibers, and thin mortar layers can improve tensile resistance, ultimate strength, and displacement capacity to avoid brittle shear failures. This study overviews masonry structural diagnostics and compares traditional and advanced strengthening techniques of masonry walls, arches, vaults, and columns. Several research results in automatic surface crack detection for unreinforced masonry (URM) walls are presented considering crack detection based on machine learning and deep learning algorithms. In addition, the kinematic and static principles of Limit Analysis within the rigid no-tension model framework are presented. The manuscript sets a practical perspective, providing an inclusive list of papers describing the essential latest research in this field; thus, this paper is useful for researchers and practitioners in masonry structures.

【文献 14】
作者: Jia, J
来源: SENSORS
题目: Deep Learning for Structural Health Monitoring: Data, Algorithms,
摘要: Environmental effects may lead to cracking, stiffness loss, brace damage, and other damages in bridges, frame structures, buildings, etc. Structural Health Monitoring (SHM) technology could prevent catastrophic events by detecting damage early. In recent years, Deep Learning (DL) has developed rapidly and has been applied to SHM to detect, localize, and evaluate diverse damages through efficient feature extraction. This paper analyzes 337 articles through a systematic literature review to investigate the application of DL for SHM in the operation and maintenance phase of facilities from three perspectives: data, DL algorithms, and applications. Firstly, the data types in SHM and the corresponding collection methods are summarized and analyzed. The most common data types are vibration signals and images, accounting for 80% of the literature studied. Secondly, the popular DL algorithm types and application areas are reviewed, of which CNN accounts for 60%. Then, this article carefully analyzes the specific functions of DL application for SHM based on the facility's characteristics. The most scrutinized study focused on cracks, accounting for 30 percent of research papers. Finally, challenges and trends in applying DL for SHM are discussed. Among the trends, the Structural Health Monitoring Digital Twin (SHMDT) model framework is suggested in response to the trend of strong coupling between SHM technology and Digital Twin (DT), which can advance the digitalization, visualization, and intelligent management of SHM.

【文献 15】
作者: Ehrnstén, U
来源: JOURNAL OF NUCLEAR MATERIALS
题目: A review of stress corrosion cracking of austenitic stainless steels in
摘要: Initial cases of stress corrosion cracking (SCC) in pressurized water reactors (PWRs) occurred mostly but not exclusively in stagnant areas like dead-legs, but recently more extensive IGSCC has occurred in normal freeflowing PWR primary water. Operational experience and laboratory data reveal that the main parameters in IGSCC include cold work and weld residual strain, oxygen, and residual and applied stress. Residual strain, which arises from manufacturing, surface grinding, and welding, should be limited by optimizing manufacturing procedures, minimizing alignment and fit-up stresses and using high-quality weld procedures. Preventing oxygen ingress in the make-up water should be pursued. Stresses created by thermal fluctuations (thermal mixing, lowleakage core operation, and start-ups) deserve more attention. Weld residual stress, fit-up stresses and local stresses from load follow must be maintained below the annealed yield stress. IGSCC should be considered in aging management and in-service inspection. Detection techniques capable of identifying IGSCC should be employed.

【文献 16】
作者: Javidrad, H
来源: VIRTUAL AND PHYSICAL PROTOTYPING
题目: Fatigue performance of metal additive manufacturing: a comprehensive
摘要: Fatigue life assessment of metal additive manufacturing (AM) products has remained challenging due to the uncertainty of as-built defects, heterogeneity of the microstructure, and residual stress. In the past few years, many works have been conducted to develop models in order to predict fatigue life of metal AM samples by considering the existence of AM inherent defects. This review paper addresses the main issues regarding fatigue assessment of metal AM parts by considering the effect of defects and post processing strategies. Mechanisms that are contributing to the failure of metal AM samples are categorized and discussed in detail. Several modelling approaches exist in the case of fatigue life prediction. The common fatigue models that are compatible with AM properties are thoroughly explained by discussing the previous works and highlighting their major conclusions. In addition, the use of machine learning is identified as the future of metal AM fatigue life assessment due to their high performance. The main challenge of today's fatigue and fracture community was identified as the fatigue life estimation of complex geometries with the presence of different types of defects, anisotropic microstructure, and complex state of residual stress. This work proposes the available approaches to tackle this challenge.

【文献 17】
作者: Khan, MA
来源: REMOTE SENSING
题目: Image Processing Techniques for Concrete Crack Detection: A
摘要: Cracks in concrete surfaces are one of the most prominent causes of the degradation of concrete structures such as bridges, roads, buildings, etc. Hence, it is very crucial to detect cracks at an early stage to inspect the structural health of the concrete structure. To solve the drawbacks of manual inspection, Image Processing Techniques (IPTs), especially those based on Deep Learning (DL) methods, have been investigated for the past few years. Due to the groundbreaking development of this field, researchers have devoted their endeavors to detecting cracks using DL-based IPTs and as a result, the techniques have given answers to many challenging problems. However, to the best of our knowledge, a state-of-the-art systematic review paper is lacking in this field that would present a scientometric analysis as well as a critical survey of the existing works to document the research trends and summarize the prominent IPTs for detecting cracks in concrete structures. Therefore, this article comes forward to spur researchers with a systematic review of the relevant literature, which will present both scientometric and critical analysis of the papers published in this research area. The scientometric data that are brought out from the articles are analyzed and visualized by using VOSviewer and CiteSpace text mining tools in terms of some parameters. Furthermore, this article elucidates research from all over the world by highlighting and critically analyzing the incarnated essence of some of the most influential papers. Moreover, this research raises some common questions as well as extracts answers from the analyzed papers to highlight various features of the utilized methods.

【文献 18】
作者: Payawal, JMG
来源: APPLIED SCIENCES-BASEL
题目: Image-Based Structural Health Monitoring: A Systematic Review
摘要: The early discovery of factors that compromise a civil infrastructure's structural integrity allows for safety monitoring, timely prevention, and a prompt remedy to the discovered problem. As a result, researchers have been researching various methodologies and types of structural health monitoring (SHM). A systematic search was performed following the updated Preferred Reporting Items for Systematic reviews and Meta-Analyses (PRISMA 2020) in Scopus and ScienceDirect from Elsevier, Google Scholar, MDPI, Springer, Wiley Online and ASCE Library, EOP and IOP Science, IEEE, and other databases with the reliable peer review process. From 1480 identified pieces of literature, one hundred and nine (109) sources met the criteria for inclusion and exclusion and were used to produce our findings. This study presents the identified purpose and application of image-based SHM, which includes: (1) identifying and discovering; (2) measuring and monitoring; (3) automating and increasing efficiency; and (4) promoting development and creating 3D models. Furthermore, the responsibilities and relevance of components and parameters for implementing image-based SHM devices and systems, and their issues, are covered in this paper. Future research can benefit from the stated applications for innovation and the requirements of image-based SHM.

【文献 19】
作者: Rabi, RR
来源: BUILDINGS
题目: Effectiveness of Vibration-Based Techniques for Damage Localization and
摘要: Bridges are essential to infrastructure and transportation networks, but face challenges from heavier traffic, higher speeds, and modifications like busway integration, leading to potential overloading and costly maintenance. Structural Health Monitoring (SHM) plays a crucial role in assessing bridge conditions and predicting failures to maintain structural integrity. Vibration-based condition monitoring employs non-destructive, in situ sensing and analysis of system dynamics across time, frequency, or modal domains. This method detects changes indicative of damage or deterioration, offering a proactive approach to maintenance in civil engineering. Such monitoring systems hold promise for optimizing the management and upkeep of modern infrastructure, potentially reducing operational costs. This paper aims to assist newcomers, practitioners, and researchers in navigating various methodologies for damage identification using sensor data from real structures. It offers a comprehensive review of prevalent anomaly detection approaches, spanning from traditional techniques to cutting-edge methods. Additionally, it addresses challenges inherent in Vibration-Based Damage (VBD) SHM applications, including establishing damage thresholds, corrosion detection, and sensor drift.

【文献 20】
作者: Nuthalapati, S
来源: NUCLEAR ENGINEERING AND TECHNOLOGY
题目: A review of chloride induced stress corrosion cracking characterization
摘要: Austenitic stainless steels (ASS) are extensively employed in various sectors such as nuclear, power, petrochemical, oil and gas because of their excellent structural strength and resistance to corrosion. SS304 and SS316 are the predominant choices for piping, pressure vessels, heat exchangers, nuclear reactor core components and support structures, but they are susceptible to stress corrosion cracking (SCC) in chloride-rich environments. Over the course of several decades, extensive research efforts have been directed towards evaluating SCC using diverse methodologies and models, albeit some uncertainties persist regarding the precise progression of cracks. This review paper focuses on the application of Acoustic Emission Technique (AET) for assessing SCC damage mechanism by monitoring the dynamic acoustic emissions or inelastic stress waves generated during the initiation and propagation of cracks. AET serves as a valuable non-destructive technique (NDT) for in-service evaluation of the structural integrity within operational conditions and early detection of critical flaws. By leveraging the time domain and time-frequency domain techniques, various Acoustic Emission (AE) parameters can be characterized and correlated with the multi-stage crack damage phenomena. Further theories of the SCC mechanisms are elucidated, with a focus on both the dissolution-based and cleavage-based damage models. Through the comprehensive insights provided here, this review stands to contribute to an enhanced understanding of SCC damage in stainless steels and the potential AET application in nuclear industry.

【文献 21】
作者: Sandhu, HK
来源: ENERGIES
题目: A Future with Machine Learning: Review of Condition Assessment of
摘要: The nuclear industry is exploring applications of Artificial Intelligence (AI), including autonomous control and management of reactors and components. A condition assessment framework that utilizes AI and sensor data is an important part of such an autonomous control system. A nuclear power plant has various structures, systems, and components (SSCs) such as piping-equipment that carries coolant to the reactor. Piping systems can degrade over time because of flow-accelerated corrosion and erosion. Any cracks and leakages can cause loss of coolant accident (LOCA). The current industry standards for conducting maintenance of vital SSCs can be time and cost-intensive. AI can play a greater role in the condition assessment and can be extended to recognize concrete degradation (chloride-induced damage and alkali-silica reaction) before cracks develop. This paper reviews developments in condition assessment and AI applications of structural and mechanical systems. The applicability of existing techniques to nuclear systems is somewhat limited because its response requires characterization of high and low-frequency vibration modes, whereas previous studies focus on systems where a single vibration mode can define the degraded state. Data assimilation and storage is another challenging aspect of autonomous control. Advances in AI and data mining world can help to address these challenges.

【文献 22】
作者: Keshmiry, A
来源: CONSTRUCTION AND BUILDING MATERIALS
题目: Assessment, repair, and retrofitting of masonry structures: A
摘要: In the history of our civilization, masonry structures date back thousands of years. Considering the different types, geometries, and arrangements of masonry components, as well as the mortar properties, defining "masonry"precisely is challenging. Construction of masonry structures relies on layering single components and binding them using mortar or crafting them with stones without mortar. Despite many advantages, masonry structures remain one of the most vulnerable construction types. Many of these structures have not been designed to withstand seismic loads. Generally, their structural systems have been designed primarily to withstand gravity loads. Consequently, moderate earthquakes can cause extensive damage and destroy entire cities. Therefore, the assessment, repair, and retrofitting of these structures is vital to society's well-being. First, this study describes masonry structures and their mechanical and structural characteristics. Next, methods for detecting and classifying common types of damage are presented. Subsequently, a comprehensive review of assessment, repair, and retrofitting methods for masonry structures is provided. Machine learning (ML) techniques have proven to be exceptional tools for providing accurate and reliable information. In this paper, descriptions and recent advances in ML techniques for the assessment, repair, and retrofitting of masonry structures are presented. These models can be utilized for several predictive applications, such as determining possible damage scenarios in heritage buildings, assessing seismic vulnerability, detecting superficial surface damage, and selecting mortar compositions for optimal mechanical properties. Furthermore, structural health monitoring (SHM) methods applicable to masonry structures are discussed. The study concludes with case studies, and an extensive discussion of existing methods, challenges, and recommendations for future work.

【文献 23】
作者: Chu, T
来源: HELIYON
题目: A review of vibration analysis and its applications
摘要: Vibration Analysis (VA) is the most commonly used technique in predictive maintenance. It allows the diagnosis of faults, especially those in the early stages. The use of VA is important for maintenance costs and downtime savings, making decisions about repair and total replacement. The method has been applied in many industries and proven to be effective. It is applicable to rotating, non -rotating equipment, continuous processes or even construction structure. In this paper, vibration analysis fundamentals as well as many studies on the method's application are reviewed. The purpose is to give an overview of how vibration analysis is used in many industries including petroleum to show its potential in petroleum industry. VA has been used in many areas from transportation, refinery to drilling and production. However, there are still rooms for improvement and implementation. One potential application is detecting faults in Electric Submersible Pump (ESP) system. ESP is located downhole making it susceptible to faults and defects that could be difficult to detect using conventional methods. These faults and defects could lead to reduced pump performance or even complete failure that require replacement. Thus, it is important to monitor and analyze vibration of ESP components, specifically pump and motor. Different studies on the topic are also reviewed and discussed. Some studies have been conducted showing that analyzing ESP vibration data helps predict early problems and identifying the causes. Vibration data were also used in principal component analysis models to predict and identify problems as presented in some works. However, principal component analysis could discharge the data models to be unable to correctly predict and determine the faults. VA is a practical technique to monitor and diagnose machine's health. It is important to research VA further and apply it more in petroleum industry, especially in production system. Applications of VA could increase machine's lifespan, reduce maintenance cost and would be useful in optimization.

【文献 24】
作者: Hussain, M
来源: MACHINES
题目: Review of Prediction of Stress Corrosion Cracking in Gas Pipelines Using
摘要: Pipeline integrity and safety depend on the detection and prediction of stress corrosion cracking (SCC) and other defects. In oil and gas pipeline systems, a variety of corrosion-monitoring techniques are used. The observed data exhibit characteristics of nonlinearity, multidimensionality, and noise. Hence, data-driven modeling techniques have been widely utilized. To accomplish intelligent corrosion prediction and enhance corrosion control, machine learning (ML)-based approaches have been developed. Some published papers related to SCC have discussed ML techniques and their applications, but none of the works has shown the real ability of ML to detect or predict SCC in energy pipelines, though fewer researchers have tested their models to prove them under controlled environments in laboratories, which is completely different from real work environments in the field. Looking at the current research status, the authors believe that there is a need to explore the best technologies and modeling approaches and to identify clear gaps; a critical review is, therefore, required. The objective of this study is to assess the current status of machine learning's applications in SCC detection, identify current research gaps, and indicate future directions from a scientific research and application point of view. This review will highlight the limitations and challenges of employing machine learning for SCC prediction and also discuss the importance of incorporating domain knowledge and expert inputs to enhance the accuracy and reliability of predictions. Finally, a framework is proposed to demonstrate the process of the application of ML to condition assessments of energy pipelines.

【文献 25】
作者: Cao, HB
来源: JOURNAL OF MATERIALS RESEARCH AND TECHNOLOGY-JMR&T
题目: Advances in subsurface defect detection techniques for fused silica
摘要: Fused silica is widely used in high-power laser systems, astronomy and military fields due to its excellent optical and physical properties. However, Subsurface defects(SSDs), such as microcracks, scratches, plastic deformation, and pits are often formed during mechanical processing, which can seriously reduce the optical performance and durability of fused silica. This paper first discusses the formation mechanism of SSDs in fused silica due to mechanical stress, thermal effects, etc. during mechanical processing such as cutting, grinding, and polishing. Then, the commonly used destructive and non-destructive detection methods are reviewed, and the principles and latest progress of each technique are discussed. The potential of deep learning algorithms for SSD detection is also examined. Finally, future directions for research and development are proposed to provide a valuable reference for researchers in the field.

【文献 26】
作者: Was, GS
来源: PROGRESS IN MATERIALS SCIENCE
题目: How irradiation promotes intergranular stress corrosion crack initiation
摘要: Irradiation assisted stress corrosion cracking (IASCC) is a form of intergranular stress corrosion cracking that occurs in irradiated austenitic alloys. It requires an irradiated microstructure along with high temperature water and stress. The process is ubiquitous in that it occurs in a wide range of austenitic alloys and water chemistries, but only when the alloy is irradiated. Despite evidence of this degradation mode that dates back to the 1960s, the mechanism by which it occurs has remained elusive. Here, using high resolution electron backscattering detection to analyze local stress-strain states, high resolution transmission electron microscopy to identify grain boundary phases at crack tips, and decoupling the roles of stress and grain boundary oxidation, we are able to unfold the complexities of the phenomenon to reveal the mechanism by which IASCC occurs. The significance of the findings impacts the mechanical integrity of core components of both current and advanced nuclear reactor designs worldwide.

【文献 27】
作者: Yap, RC
来源: JOURNAL OF DENTISTRY
题目: A narrative review of cracks in teeth: Aetiology, microstructure and
摘要: Objectives: To summarize the available evidence of crack formation in teeth and to discuss the limitations of the current clinical diagnostic modalities for crack detection in teeth.Background: Cracks are a common clinical finding in teeth and yet clinicians still struggle to identify the full extent and orientation of cracks for their appropriate timely management. The biomechanics of crack development can be due to multiple factors and can differ from an unrestored tooth to a restored or endodontically treated tooth.Data & sources: This narrative review has been designed following the guidelines published by Green et al. 2006 [1] Published literature in the English language that addresses the objectives of this review up to July 2022 was sourced from online databases and reference lists. The relevance of the papers was assessed and discussed by two reviewers. A total of 101 publications were included in this narrative review.Conclusions: The initiation and development of cracks in teeth are likely linked to an interplay between the masticatory forces and fracture resistance of the remaining tooth structure. From the identified literature, the quality and quantity of remaining tooth structure in a restored or endodontically-treated tooth affects the biomechanics of crack development compared to an unrestored tooth. The extent, orientation, and size of the cracks do affect a clinician's ability to detect cracks in teeth. There is still a need to develop reliable diagnostic tools that will accurately identify cracks in teeth beneath restorations to enable effective monitoring of their propagation and provide appropriate interventions.Clinical significance: The development and propagation of cracks in an unrestored tooth differ greatly from a restored and endodontically treated tooth; mainly linked to the quantity and quality of the remaining tooth structure and the forces acting on them. Identifying the extent of cracks in teeth remains challenging for early clinical intervention.

【文献 28】
作者: Abdelkader, EM
来源: BUILDINGS
题目: Synthesized Evaluation of Reinforced Concrete Bridge Defects, Their
摘要: Defects are essential indicators to gauge the structural integrity and safety of reinforced concrete bridges. Non-destructive inspection has been pervasively explored over the last three decades to localize and characterize surface and subsurface anomalies in reinforced concrete bridges. In addition, different fuzzy set theory-based, computer vision and artificial intelligence algorithms were leveraged to analyze the data garnered from non-destructive evaluation techniques. In light of the foregoing, this research paper presents a mixed review method that encompasses both bibliometric and systematic analyses of the state-of-the-art work pertinent to the assessment of reinforced concrete bridge defects using non-destructive techniques (CBD_NDT). In this context, this study reviews the literature of journal articles and book chapters indexed in Scopus and Web of Science databases from 1991 to the end of September 2022. To this end, 505 core peer-reviewed journal articles and book chapters are compiled for evaluation after conducting forward and backward snowballing alongside removing irrelevant papers. This research study then exploits both VOSVIEWER and Bibiometrix R Package for the purpose of network visualization and scientometric mapping of the appended research studies. Thereafter, this paper carries out a multifaceted systematic review analysis of the identified literature covering tackled bridge defects, used non-destructive techniques, data processing methods, public datasets, key findings and future research directions. The present study is expected to assist practitioners and policymakers to conceive and synthesize existing research and development bodies, and future trends in the domain of the assessment of bridge defects using non-destructive techniques. It can also aid in raising awareness of the importance of defect management in bridge maintenance systems.

【文献 29】
作者: Zhang, JH
来源: SENSORS
题目: A Review on Concrete Structural Properties and Damage Evolution
摘要: Concrete structures have emerged as some of the most extensively utilized materials in the construction industry due to their inherent plasticity and high-strength characteristics. However, due to the temperature fluctuations, humidity, and damage caused by human activities, challenges such as crack propagation and structural failures pose threats to the safety of people's lives and property. Meanwhile, conventional non-destructive testing methods are limited to defect detection and lack the capability to provide real-time monitoring and evaluating of concrete structural stability. Consequently, there is a growing emphasis on the development of effective techniques for monitoring the health of concrete structures, facilitating prompt repairs and mitigation of potential instabilities. This paper comprehensively presents traditional and novel methods for concrete structural properties and damage evolution monitoring, including emission techniques, electrical resistivity monitoring, electromagnetic radiation method, piezoelectric transducers, ultrasonic techniques, and the infrared thermography approach. Moreover, the fundamental principles, advantages, limitations, similarities and differences of each monitoring technique are extensively discussed, along with future research directions. Each method has its suitable monitoring scenarios, and in practical applications, several methods are often combined to achieve better monitoring results. The outcomes of this research provide valuable technical insights for future studies and advancements in the field of concrete structural health monitoring.

【文献 30】
作者: Rathee, M
来源: SENSORS
题目: Automated Road Defect and Anomaly Detection for Traffic Safety: A
摘要: Recently, there has been a substantial increase in the development of sensor technology. As enabling factors, computer vision (CV) combined with sensor technology have made progress in applications intended to mitigate high rates of fatalities and the costs of traffic-related injuries. Although past surveys and applications of CV have focused on subareas of road hazards, there is yet to be one comprehensive and evidence-based systematic review that investigates CV applications for Automated Road Defect and Anomaly Detection (ARDAD). To present ARDAD's state-of-the-art, this systematic review is focused on determining the research gaps, challenges, and future implications from selected papers (N = 116) between 2000 and 2023, relying primarily on Scopus and Litmaps services. The survey presents a selection of artefacts, including the most popular open-access datasets (D = 18), research and technology trends that with reported performance can help accelerate the application of rapidly advancing sensor technology in ARDAD and CV. The produced survey artefacts can assist the scientific community in further improving traffic conditions and safety.

【文献 31】
作者: Zhang, R
来源: GEOLOGICAL JOURNAL
题目: Unmanned aerial vehicle navigation in underground structure inspection:
摘要: Many years after construction, a number of existing old tunnels and underground structures are deteriorating with time as evidenced by cracks, large deformations, water leakage and so forth, which usually require regular site inspections to record their structural deterioration by taking high-pixel, high-overlap images along miles of a tunnel network. For complex underground structures (e.g., long tunnels and large caves), unmanned aerial vehicles (UAVs) may be adaptive in acquiring images at multiple heights and angles with low operational costs. So far, UAV underground structural health monitoring has become mature for open-air surveying with rapid developments in robotic software and hardware. However, the UAV image acquisition for underground working conditions still faces a number of key challenges. This paper aims to provide an overview of UAV navigation techniques in confined dark spaces for geotechnical engineers, geologists, drone developers and other interdisciplinary researchers & professionals in the structural health monitoring field. It specifies the challenges for UAV application in underground space, mainly including lack of Global Navigation Satellite System (GNSS) signals, poor lighting conditions, weak features and obstacle avoidance and then followed by strategic solutions. For example, in light of poor GNSS signals, the fusion of multi-sensors (e.g., laser imaging, detection and ranging (LiDAR) and multi-cameras) can enhance localization accuracy in low-luminance underground conditions. To address obstacle avoidance, computer vision (CV)-based navigation algorithms (e.g., deep reinforced learning [DRL]) enable effective navigation in complex 3D spaces, but their adaptability is limited by arithmetic power and pre-training needs. The review of relevant previous studies concludes that further development for UAVs in underground space inspection may focus on operation in large-scale geometric inspection environments, obstacle avoidance, features and semantic recognition.

【文献 32】
作者: Sjölander, A
来源: SENSORS
题目: Towards Automated Inspections of Tunnels: A Review of Optical
摘要: In recent decades, many cities have become densely populated due to increased urbanization, and the transportation infrastructure system has been heavily used. The downtime of important parts of the infrastructure, such as tunnels and bridges, seriously affects the transportation system's efficiency. For this reason, a safe and reliable infrastructure network is necessary for the economic growth and functionality of cities. At the same time, the infrastructure is ageing in many countries, and continuous inspection and maintenance are necessary. Nowadays, detailed inspections of large infrastructure are almost exclusively performed by inspectors on site, which is both time-consuming and subject to human errors. However, the recent technological advancements in computer vision, artificial intelligence (AI), and robotics have opened up the possibilities of automated inspections. Today, semiautomatic systems such as drones and other mobile mapping systems are available to collect data and reconstruct 3D digital models of infrastructure. This significantly decreases the downtime of the infrastructure, but both damage detection and assessments of the structural condition are still manually performed, with a high impact on the efficiency and accuracy of the procedure. Ongoing research has shown that deep-learning methods, especially convolutional neural networks (CNNs) combined with other image processing techniques, can automatically detect cracks on concrete surfaces and measure their metrics (e.g., length and width). However, these techniques are still under investigation. Additionally, to use these data for automatically assessing the structure, a clear link between the metrics of the cracks and the structural condition must be established. This paper presents a review of the damage of tunnel concrete lining that is detectable with optical instruments. Thereafter, state-of-the-art autonomous tunnel inspection methods are presented with a focus on innovative mobile mapping systems for optimizing data collection. Finally, the paper presents an in-depth review of how the risk associated with cracks is assessed today in concrete tunnel lining.

【文献 33】
作者: Yang, JF
来源: BUILDINGS
题目: A Review on Damage Monitoring and Identification Methods for Arch
摘要: The damage monitoring and identification of arch bridges provide an important means to ensure the safe operation of arch bridges. At present, many methods have been developed, and the applicability and effectiveness of these methods depend on the damage type, structural configuration and available data. To guide the practical application of these methods, a systematic review is implemented in this paper. Specifically, the damage monitoring and identification methods of arch bridges are divided into the damage monitoring of local diseases and damage identification of overall performance. Firstly, the research on the damage monitoring of the local diseases of arch bridges is reviewed. According to the disease type, it is divided into four categories, including suspender inspection, void monitoring, stress detection and corrosion detection. For each disease, this paper analyzes the principles, advantages and shortcomings of various methods. Then, the damage identification methods of the overall performance of arch bridges are reviewed, including masonry arch bridges, steel arch bridges, reinforced concrete arch bridges and concrete-filled steel tubular arch bridges. And the commonly used damage indexes of damage identification methods are summarized. This review aims to help researchers and practitioners in implementing existing damage detection methods effectively and developing more reliable and practical methods for arch bridges in the future.

【文献 34】
作者: de Sousa, AASR
来源: JOURNAL OF VIBRATION ENGINEERING & TECHNOLOGIES
题目: Multiclass Supervised Machine Learning Algorithms Applied to Damage and
摘要: PurposeStructural damage can significantly alter a system's local flexibility, leading to undesirable displacements and vibrations. Analysing the dynamic structure feature through statistical analysis enables us to discriminate the current structural condition and predict its short- or long-term lifespan. By directly affecting the system's vibration, cracks and discontinuities can be detected, and their severity quantified using the DI. Two damage indexes (DI) are used to build a dataset from the beam's natural frequency and frequency response function (FRF) under both undamaged and damaged conditions, and numerical and experimental tests provided the data-driven.MethodsIn this paper, we present the methodology based on machine learning (ML) to monitor the structural integrity of a beam-like structure. The performance of six ML algorithms, including k-nearest neighbors (kNN), Support Vector Machine (SVM), Decision Tree (DT), Random Forest (RF), and Naive Bayes (NB) are investigated.ResultsThe paper discusses the challenges of implementing each technique and assesses their performance in accurately classifying the dataset and indicating the beam's integrity.ConclusionThe structural monitoring performed with the ML algorithm achieved excellent metrics when inputting the simulation-generated dataset, up to 100%, and up to 95% having as input dataset provided from experimental tests. Demonstrating that the ML algorithm could correctly classify the health condition of the structure.

【文献 35】
作者: Chen, JJ
来源: AUTOMATION IN CONSTRUCTION
题目: Shifting research from defect detection to defect modeling in computer
摘要: The last decade has witnessed a plethora of studies on the applications of computer vision (CV) in structural health monitoring (SHM). While effort has been primarily focused on defect detection, increasing studies are tapping into a new area called defect modeling. It remains unclear whether the shifting focus constitutes a systematic transition. This article aims to answer the question by conducting a critical review of CV-based SHM. It is found that the turning of limelight to defect modeling coincides with the proliferation of deep learning (DL) in defect detection. The shift to defect modeling does not mean a resolution of defect detection, but poses higher requirements on its performance in realistic settings (e.g., complex background, instance differentiation). A roadmap is proposed to synergize future defect detection/modeling research. The research contributes to understanding the rapidly evolving landscape of CV-based SHM, and laying out an overarching framework to guide future research.

【文献 36】
作者: Azouz, Z
来源: ELECTRONICS
题目: Evolution of Crack Analysis in Structures Using Image Processing
摘要: Structural health monitoring (SHM) involves the control and analysis of mechanical systems to monitor the variation of geometric features of engineering structures. Damage processing is one of the issues that can be addressed by using several techniques derived from image processing. There are two types of SHM: contact-based and non-contact methods. Sensors, cameras, and accelerometers are examples of contact-based SHM, whereas photogrammetry, infrared thermography, and laser imaging are non-contact SHM techniques. In this research, our focus centres on image processing algorithms to identify the crack and analyze its properties to detect occurred damages. Based on the literature review, several preprocessing approaches were employed including image enhancement, image filtering to remove the noise and blur, and dynamic response measurement to predict the crack propagation.

【文献 37】
作者: Yuan, Q
来源: REMOTE SENSING
题目: A Review of Computer Vision-Based Crack Detection Methods in Civil
摘要: Cracks are a common defect in civil infrastructures, and their occurrence is often closely related to structural loading conditions, material properties, design and construction, and other factors. Therefore, detecting and analyzing cracks in civil infrastructures can effectively determine the extent of damage, which is crucial for safe operation. In this paper, Web of Science (WOS) and Google Scholar were used as literature search tools and "crack", "civil infrastructure", and "computer vision" were selected as search terms. With the keyword "computer vision", 325 relevant documents were found in the study period from 2020 to 2024. A total of 325 documents were searched again and matched with the keywords, and 120 documents were selected for analysis and research. Based on the main research methods of the 120 documents, we classify them into three crack detection methods: fusion of traditional methods and deep learning, multimodal data fusion, and semantic image understanding. We examine the application characteristics of each method in crack detection and discuss its advantages, challenges, and future development trends.

【文献 38】
作者: Wang, H
来源: JOURNAL OF PIPELINE SCIENCE AND ENGINEERING
题目: A comprehensive review of polyethylene pipes: Failure mechanisms,
摘要: Polyethylene (PE) pipes are widely used for natural gas distribution due to their good durability and low costs. To ensure the integrity of PE pipelines, it is crucial to develop a comprehensive understanding of pipe failure mechanisms and to recognize the benefits and limitations of different pipeline monitoring strategies. This review provides an overview of different types of pipe failures in the context of their response to operational loads and material degradation. It also covers the details of mechanical tests for predicting the long-term performance of pipes, theoretical models for studying defect growth, examines different defect detection methods, and concludes with an assessment of pipe repair techniques. The findings highlight the importance of investigating the effects of existing defects on the operational performance of the pipeline. This indirectly emphasizes the need to develop time- and cost-efficient strategies to detect defects in the early stages. There is a clear gap in the inclusion of PE aging effects in the lifetime performance models. In addition, given the large number of inspection techniques, a regulated selection of pipeline inspection methods is highly desired, specific to the defect type. Further research in advancing adhesive-based repair of incipient defects is crucial to prevent catastrophic defect growth.

【文献 39】
作者: Huang, J
来源: SENSORS
题目: Systematic Evaluation of Ultrasonic In-Line Inspection Techniques for
摘要: The global reliance on oil and gas pipelines for energy transportation is increasing. As the pioneering review in the field of ultrasonic defect detection for oil and gas pipelines based on bibliometric methods, this study employs visual analysis to identify the most influential countries, academic institutions, and journals in this domain. Through cluster analysis, it determines the primary trends, research hotspots, and future directions in this critical field. Starting from the current global industrial ultrasonic in-line inspection (ILI) detection level, this paper provides a flowchart for selecting detection methods and a table for defect comparison, detailing the comparative performance limits of different detection devices. It offers a comprehensive perspective on the latest ultrasonic pipeline detection technology from laboratory experiments to industrial practice.

【文献 40】
作者: Knox, A
来源: ANIMALS
题目: Current and Future Advances in the Detection and Surveillance of
摘要: strengths of AAS rise initially, and then fall as MSWI-BA content rises, reaching their highest value in AAS with 6% MSWI-BA. The flexural and compressive strength of AAS with 6% MSWI-BA at 400 degrees C are 31.3% and 17.9% higher than that of AAS without MSWI-BA, respectively. When MSWI-BA content is less than 6%, the presence of portlandite and hydrotalcite in MSWI-BA in-creases the alkali concentration and accelerates the hydration reaction, which promotes the for-mation of C-A-S-H gel with high degree of polymerization. Besides, the hydrogen generated by the reaction of elemental aluminum in MSWI-BA and OH- can improve the pores connectivity of matrix, and reduce the water vapor pressure and shrinkage, thus inhibiting matrix cracking. However, due to its low activity and porous characteristics, an excessive amount of MSWI-BA (> 6%) leads to a decrease in the amount of C-A-S-H and a significant increase in porosity, thus re-ducing the strength of AAS. property are seriously threatened when fire occurs in building structures [1]. The damage to concrete mainly attributed to water loss and degradation of hydration products in the hardened cement paste Portland cement (OPC) concrete exposed to high-temperatures is significantly reduced by the decompo-(C-S-H) and portlandite (Ca(OH)2), and the accumulation of internal stresses generated by water va-

【文献 41】
作者: Tonga, DA
来源: COATINGS
题目: Nondestructive Evaluation of Fiber-Reinforced Polymer Using Microwave
摘要: Carbon-fiber-reinforced polymer (CFRP) is widely acknowledged as a leading advanced material structure, offering superior properties compared to traditional materials, and has found diverse applications in several industrial sectors, such as that of automobiles, aircrafts, and power plants. However, the production of CFRP composites is prone to fabrication problems, leading to structural defects arising from cycling and aging processes. Identifying these defects at an early stage is crucial to prevent service issues that could result in catastrophic failures. Hence, routine inspection and maintenance are crucial to prevent system collapse. To achieve this objective, conventional nondestructive testing (NDT) methods are utilized to inspect CFRP components. However, the restricted field penetration within the CFRP makes conventional NDT approaches ineffective. Recently, microwave techniques have been developed to address the challenges associated with CFRP inspection by providing better material penetration and more precise results. This paper offers a review of the primary NDT methods employed to inspect CFRP composites, emphasizing microwave-based NDT techniques and their key features.

【文献 42】
作者: Iqbal, U
来源: WATER AND ENVIRONMENT JOURNAL
题目: The last two decades of computer vision technologies in water resource
摘要: Efficient management of water resources is an important task given the significance of water in daily lives and economic growth. Water resource management is a specific field of study which deals with the efficient management of water resources towards fulfilling the needs of society and preventing from water-related disasters. Many activities within this domain are getting benefitted with the recent technological advancements. Within many others, computer vision-based solutions have emerged as disruptive technologies to address complex real-world problems within the water resource management domain (e.g., flood detection and mapping, satellite-based water bodies monitoring, monitoring and inspection of hydraulic structures, blockage detection and assessment, drainage inspection and sewer monitoring). However, there are still many aspects within the water resource management domain which can be explored using computer vision technologies. Therefore, it is important to investigate the trends in current research related to these technologies to inform the new researchers in this domain. In this context, this paper presents the bibliometric analysis of the literature from the last two decades where computer vision technologies have been used for addressing problems within the water resource management domain. The analysis is presented in two categories: (a) performance analysis demonstrating highlighted trends in the number of publications, number of citations, top contributing countries, top publishing journals, top contributing institutions and top publishers and (b) science mapping to demonstrate the relation between the bibliographic records based on the co-occurrence of keywords, co-authorship analysis, co-citation analysis and bibliographic coupling analysis. Bibliographic records (i.e., 1059) are exported from the Web of Science (WoS) core collection database using a comprehensive query of keywords. VOSviewer opensource tool is used to generate the network and overlay maps for the science mapping of bibliographic records. Results highlighted important trends and valuable insights related to the use of computer vision technologies in water resource management. An increasing trend in the number of publications and focus on deep learning/artificial intelligence (AI)-based approaches has been reported from the analysis. Further, flood mapping, crack/fracture detection, coastal flood detection, blockage detection and drainage inspections are highlighted as active areas of research.

【文献 43】
作者: Lu, BP
来源: ENERGIES
题目: Insulation Degradation Mechanism and Diagnosis Methods of Offshore Wind
摘要: The marine environment in which offshore wind turbines are located is very complex and subjected to a variety of random loads that vary with time and space. As an important component of offshore wind power, the cable also bears the impact of the environment in which most of the turbines are located. Under the long-term action of mechanical stresses such as tension, torsion, and vibration, the cable insulation will crack due to stress fatigue leading to partial discharge, which seriously affects its electrical performance. The study of the mechanism of the change of electrical properties of cable insulation due to mechanical behavior is of great theoretical guidance to improve the reliable operation of cables. This paper first introduces the basic characteristics and operating conditions of torsion-resistant cables and submarine cables. Then the mechanical behavior of the cables is summarized, and the deterioration mechanism and deterioration effect of wind power cable insulation under the influence of multiple factors such as heat, oxygen, and mechanical stress are sorted out. Then, the basic principles of wind power cable operation condition monitoring methods and their characteristics are described. Finally, the relevant methods for the detection of hidden defects inside the insulation are summarized.

【文献 44】
作者: Nasar, RA
来源: NONLINEAR DYNAMICS
题目: On modeling and damage detection methodologies in rotor systems
摘要: Damages in rotor systems have severe impact on their functionality, safety, running durability and their industrial productivity, which usually leads to unavoidable economical and human losses. Rotor systems are employed in extensive industrial applications such as jet engines, gas and steam turbines, heavy-duty pumps and compressors, drilling tools, and in other machineries. One of the major damages in such systems is the propagation of fatigue cracks. The heavy-duty and recurrent cyclic fatigue loading in rotor systems is one of the main factors leading to fatigue crack propagation. For the past few decades, numerous research have been conducted to study crack related damages and various methodologies were proposed or employed for damage detection in rotor systems. Therefore, the purpose of the present review article is to provide a thorough analysis and evaluation regarding the associated research related to the modeling aspects of rotor systems that are associated with various kinds of (rotor related) damages. Based on this review, it is observed that the crack modeling, especially with the breathing crack type in rotor systems, is still based on few primary models. Several researchers, based on different assumptions, have extended and modified such models to be more reliable for analysis. Moreover, the arising demand for early crack detection has led to utilization of various tools such as Fast Fourier transform, Hilbert Huang transform, wavelet transform, whirling analysis, energy methods, and the correlation between backward whirling and rotor faults etc. In addition, the significant impact of nonsynchronous whirl within resonance zones of rotor systems on post-resonance backward whirl, under various rotor related faults, is also highlighted in the present review. Therefore, the review provides an evaluation and comparison between several crack models and detection methodologies in rotor systems. Moreover, this review could help in identifying the gaps in modeling, simulation, and dynamical analysis of cracked rotor systems to establish robust research platform on cracked rotor systems.

【文献 45】
作者: Hussain, T
来源: ENERGIES
题目: A Review on Defect Detection of Electroluminescence-Based Photovoltaic
摘要: The past two decades have seen an increase in the deployment of photovoltaic installations as nations around the world try to play their part in dampening the impacts of global warming. The manufacturing of solar cells can be defined as a rigorous process starting with silicon extraction. The increase in demand has multiple implications for manual quality inspection. With automated inspection as the ultimate goal, researchers are actively experimenting with convolutional neural network architectures. This review presents an overview of the electroluminescence image-extraction process, conventional image-processing techniques deployed for solar cell defect detection, arising challenges, the present landscape shifting towards computer vision architectures, and emerging trends.

【文献 46】
作者: Di Summa, M
来源: IEEE ACCESS
题目: A Review on Deep Learning Techniques for Railway Infrastructure
摘要: In the last decade, thanks to a widespread diffusion of powerful computing machines, artificial intelligence has been attracting the attention of the academic and industrial worlds. This review aims to understand how the scientific community is approaching the use of deep-learning techniques in a particular industrial sector, the railway. This work is an in-depth analysis related to the last years of the way this new technology can try to provide answers even in a field where the primary requirement is to improve the already very high levels of safety. A strategic and constantly evolving field such as the railway sector could not remain extraneous to the use of this new and promising technology. Deep learning algorithms devoted to the classification, segmentation, and detection of the faults that affect the railway area and the overhead contact system are discussed. The railway sector offers many aspects that can be investigated with these techniques. This work aims to expose the possible applications of deep learning in the railway sector established on the type of recovered information and the type of algorithms to be used accordingly.

【文献 47】
作者: Tanveer, M
来源: APPLIED SCIENCES-BASEL
题目: Recent Advancements in Guided Ultrasonic Waves for Structural Health
摘要: Structural health monitoring (SHM) is essential for ensuring the safety and longevity of laminated composite structures. Their favorable strength-to-weight ratio renders them ideal for the automotive, marine, and aerospace industries. Among various non-destructive testing (NDT) methods, ultrasonic techniques have emerged as robust tools for detecting and characterizing internal flaws in composites, including delaminations, matrix cracks, and fiber breakages. This review concentrates on recent developments in ultrasonic NDT techniques for the SHM of laminated composite structures, with a special focus on guided wave methods. We delve into the fundamental principles of ultrasonic testing in composites and review cutting-edge techniques such as phased array ultrasonics, laser ultrasonics, and nonlinear ultrasonic methods. The review also discusses emerging trends in data analysis, particularly the integration of machine learning and artificial intelligence for enhanced defect detection and characterization through guided waves. This review outlines the current and anticipated trends in ultrasonic NDT for SHM in composites, aiming to aid researchers and practitioners in developing more effective monitoring strategies for laminated composite structures.

【文献 48】
作者: Machado, MA
来源: SENSORS
题目: Eddy Currents Probe Design for NDT Applications: A Review
摘要: Eddy current testing (ECT) is a crucial non-destructive testing (NDT) technique extensively used across various industries to detect surface and sub-surface defects in conductive materials. This review explores the latest advancements and methodologies in the design of eddy current probes, emphasizing their application in diverse industrial contexts such as aerospace, automotive, energy, and electronics. It explores the fundamental principles of ECT, examining how eddy currents interact with material defects to provide valuable insights into material integrity. The integration of numerical simulations, particularly through the Finite Element Method (FEM), has emerged as a transformative approach, enabling the precise modeling of electromagnetic interactions and optimizing probe configurations. Innovative probe designs, including multiple coil configurations, have significantly enhanced defect detection capabilities. Despite these advancements, challenges remain, particularly in calibration and sensitivity to environmental conditions. This comprehensive overview highlights the evolving landscape of ECT probe design, aiming to provide researchers and practitioners with a detailed understanding of current trends in this dynamic field.

【文献 49】
作者: Rafati, A
来源: THERMAL SCIENCE AND ENGINEERING PROGRESS
题目: Predictive maintenance of district heating networks: A comprehensive
摘要: District heating is one of the main strategies for providing heat supply for buildings in cold climate countries. However, large parts of the current prefabricated pipes of these networks are reaching the end of their technical service life. Replacing these pipes is a time-consuming procedure and requires huge investments. Predictive Maintenance (PdM) is a promising strategy to deal with these situations and to optimize and prioritize maintenance activities. This paper surveys different PdM approaches considering difficulties in implementing PdMs in district heating networks (DHNs) compared with other energy sectors. It demonstrates that the PdM methodology is unique for each DHN according to the distinctive characteristics, environmental factors, heating resource type, available data, equipment, and other factors. To the best of the authors' knowledge, this paper presents the first comprehensive review focused on various aspects of PdM strategies developed for DHNs, including data analytics, prediction models, and integrated technologies to facilitate the implementation of these strategies in DHNs. A thorough discussion on state-of-the-art technologies and real-world challenges for implementing PdM is presented, and potential research avenues are provided.

【文献 50】
作者: Pahnabi, N
来源: SENSORS
题目: Imaging of Structural Timber Based on In Situ Radar and Ultrasonic Wave
摘要: With the rapidly growing interest in using structural timber, a need exists to inspect and assess these structures using non-destructive testing (NDT). This review article summarizes NDT methods for wood inspection. After an overview of the most important NDT methods currently used, a detailed review of Ground Penetrating Radar (GPR) and Ultrasonic Testing (UST) is presented. These two techniques can be applied in situ and produce useful visual representations for quantitative assessments and damage detection. With its commercial availability and portability, GPR can help rapidly identify critical features such as moisture, voids, and metal connectors in wood structures. UST, which effectively detects deep cracks, delaminations, and variations in ultrasonic wave velocity related to moisture content, complements GPR's capabilities. The non-destructive nature of both techniques preserves the structural integrity of timber, enabling thorough assessments without compromising integrity and durability. Techniques such as the Synthetic Aperture Focusing Technique (SAFT) and Total Focusing Method (TFM) allow for reconstructing images that an inspector can readily interpret for quantitative assessment. The development of new sensors, instruments, and analysis techniques has continued to improve the application of GPR and UST on wood. However, due to the hon-homogeneous anisotropic properties of this complex material, challenges remain to quantify defects and characterize inclusions reliably and accurately. By integrating advanced imaging algorithms that consider the material's complex properties, combining measurements with simulations, and employing machine learning techniques, the implementation and application of GPR and UST imaging and damage detection for wood structures can be further advanced.

【文献 51】
作者: Ran, SC
来源: APPLIED SCIENCES-BASEL
题目: A Concise State-of-the-Art Review of Crack Monitoring Enabled by RFID
摘要: Cracking is an important factor affecting the performance and life of large structures. In order to maximize personal safety and reduce costs, it is highly necessary to carry out research on crack monitoring technology. Sensors based on Radio Frequency Identification (RFID) antennas have the advantages of wireless and low cost, which makes them highly competitive in the field of structure health monitoring (SHM). Thus, this study systematically summarizes the research progress of crack monitoring based on RFID technology in recent years. Firstly, this study introduces the causes of cracks and the traditional monitoring methods. Further, this study summarizes several main RFID-based crack monitoring and detection methods, including crack monitoring based on chipless RFID technology, passive RFID technology, and ultra-high-frequency (UHF) RFID technology, including the implementation methods, as well as the advantages and disadvantages of those technologies. In addition, for RFID-based crack monitoring applications, the two most commonly used materials are concrete materials and metal materials, which are also illustrated in detail. In general, this study can provide technical support and a theoretical basis for crack monitoring and detection to ensure the safety of engineering structures.

【文献 52】
作者: Su, CS
来源: APPLIED SCIENCES-BASEL
题目: A Review of Deep Learning Applications in Tunneling and Underground
摘要: With the advent of the era of big data and information technology, deep learning (DL) has become a hot trend in the research field of artificial intelligence (AI). The use of deep learning methods for parameter inversion, disease identification, detection, surrounding rock classification, disaster prediction, and other tunnel engineering problems has also become a new trend in recent years, both domestically and internationally. This paper briefly introduces the development process of deep learning. By reviewing a number of published papers on the application of deep learning in tunnel engineering over the past 20 years, this paper discusses the intelligent application of deep learning algorithms in tunnel engineering, including collapse risk assessment, water inrush prediction, crack identification, structural stability evaluation, and seepage erosion in mountain tunnels, urban subway tunnels, and subsea tunnels. Finally, it explores the future challenges and development prospects of deep learning in tunnel engineering.

【文献 53】
作者: Yang, C
来源: ENERGIES
题目: A Survey of Photovoltaic Panel Overlay and Fault Detection Methods
摘要: Photovoltaic (PV) panels are prone to experiencing various overlays and faults that can affect their performance and efficiency. The detection of photovoltaic panel overlays and faults is crucial for enhancing the performance and durability of photovoltaic power generation systems. It can minimize energy losses, increase system reliability and lifetime, and lower maintenance costs. Furthermore, it can contribute to the sustainable development of photovoltaic power generation systems, which can reduce our reliance on conventional energy sources and mitigate environmental pollution and greenhouse gas emissions in line with the goals of sustainable energy and environmental protection. In this paper, we provide a comprehensive survey of the existing detection techniques for PV panel overlays and faults from two main aspects. The first aspect is the detection of PV panel overlays, which are mainly caused by dust, snow, or shading. We classify the existing PV panel overlay detection methods into two categories, including image processing and deep learning methods, and analyze their advantages, disadvantages, and influencing factors. We also discuss some other methods for overlay detection that do not process images to detect PV panel overlays. The second aspect is the detection of PV panel faults, which are mainly caused by cracks, hot spots, or partial shading. We categorize existing PV panel fault detection methods into three categories, including electrical parameter detection methods, detection methods based on image processing, and detection methods based on data mining and artificial intelligence, and discusses their advantages and disadvantages.

【文献 54】
作者: Shen, XL
来源: CRYSTALS
题目: Nondestructive Testing of Metal Cracks: Contemporary Methods and
摘要: There are high demands for the early and reliable detection of metal components used in safety-critical structures. Nondestructive testing (NDT) is a pivotal technique used across industries to assess a material's integrity without causing damage and has been used in early crack detection of metals, mainly based on changes in the crystal structure and magnetic properties of metals. This review provides an overview of internal and external detection technology based on nondestructive testing methods such as ultrasonic, electromagnetic, ray, magnetic particle, etc. Especially, the integration of advanced methodologies such as machine learning and artificial intelligence deserves a place in NDT methods. Furthermore, the multifactorial detection method is promoted to enhance the sensitivity and detection range due to advantage integration but still has emerging challenges for safer equipment and applications. The review aims to compare these methods and outline the future challenges of NDT technologies for metal crack detection.

【文献 55】
作者: Eickhoff, L
来源: JOURNAL OF CLINICAL NURSING
题目: Slipping through the cracks-detection of sex trafficking in the adult
摘要: Introduction: Current research estimates that over 24 million individuals experience human trafficking worldwide. There is a growing prevalence of sex trafficking in the United States. An estimated 87% of trafficked persons visit the emergency department during their captivity. Emergency departments across the United States use differing screening methods for sex trafficking. Current screening tools return a high rate of false negatives, and the appropriate use of tools or standardised lists remains unclear.

【文献 56】
作者: Zanella, D
来源: ANALYTICAL AND BIOANALYTICAL CHEMISTRY
题目: The contribution of high-resolution GC separations in plastic recycling
摘要: One convenient strategy to reduce environmental impact and pollution involves the reuse and revalorization of waste produced by modern society. Nowadays, global plastic production has reached 367 million tons per year and because of their durable nature, their recycling is fundamental for the achievement of the circular economy objective. In closing the loop of plastics, advanced recycling, i.e., the breakdown of plastics into their building blocks and their transformation into valuable secondary raw materials, is a promising management option for post-consumer plastic waste. The most valuable product from advanced recycling is a fluid hydrocarbon stream (or pyrolysis oil) which represents the feedstock for further refinement and processing into new plastics. In this context, gas chromatography is currently playing an important role since it is being used to study the pyrolysis oils, as well as any organic contaminants, and it can be considered a high-resolution separation technique, able to provide the molecular composition of such complex samples. This information significantly helps to tailor the pyrolysis process to produce high-quality feedstocks. In addition, the detection of contaminants (i.e., heteroatom-containing compounds) is crucial to avoid catalytic deterioration and to implement and design further purification processes. The current review highlights the importance of molecular characterization of waste stream products, and particularly the pyrolysis oils obtained from waste plastics. An overview of relevant applications published recently will be provided, and the potential of comprehensive two-dimensional gas chromatography, which represents the natural evolution of gas chromatography into a higher-resolution technique, will be underlined.

【文献 57】
作者: Li, YH
来源: APPLIED SCIENCES-BASEL
题目: Corrosion Monitoring Techniques in Subcritical and Supercritical Water
摘要: A series of advanced equipment exposed to sub-/supercritical water environments at high temperatures, high pressures, and extreme water chemistry with high salt and dissolved oxygen content faces serious corrosion problems. Obtaining on-site corrosion data for typical materials in harsh environments is crucial for operating and maintaining related equipment and optimizing various corrosion prediction models. First, this article introduces the advantages and disadvantages, usage scenarios, and future development potential of several in situ monitoring technologies, including ultrasonic thickness measurement, the infrared thermography method, microwave imaging, eddy current detection, and acoustic emission. Considering the importance of electrochemical corrosion data in revealing microscale and nanoscale corrosion mechanisms, in situ testing techniques such as electrical resistance probes, electrochemical corrosion potential, electrochemical impedance spectroscopy, and electrochemical noise that can be applied to sub-/supercritical water systems were systematically discussed. The testing platform and typical data obtained were discussed with thick and heavy colors to establish a mechanical prediction model for corrosion behavior. It is of great significance to promote the development of corrosion monitoring techniques, such as breaking through testing temperature limitations and broadening the industrial application scenarios and maturity.

【文献 58】
作者: Weinert, A
来源: SENSORS
题目: Condition Monitoring of Additively Manufactured Injection Mould Tooling:
摘要: Injection moulding (IM) is an important industrial process, known to be the most used plastic formation technique. Demand for faster cycle times and higher product customisation is driving interest in additive manufacturing (AM) as a new method for mould tool manufacturing. The use of AM offers advantages such as greater design flexibility and conformal cooling of components to reduce cycle times and increase product precision. However, shortcomings of metal additive manufacturing, such as porosity and residual stresses, introduce uncertainties about the reliability and longevity of AM tooling. The injection moulding process relies on high volumes of produced parts and a minimal amount of tool failures. This paper reviews the demands for tool condition monitoring systems for AM-manufactured mould tools; although tool failures in conventionally manufactured tooling are rare, they do occur, usually due to cracking, deflection, and channel blockages. However, due to the limitations of the AM process, metal 3D-printed mould tools are susceptible to failures due to cracking, delamination and deformation. Due to their success in other fields, acoustic emission, accelerometers and ultrasound sensors offer the greatest potential in mould tool condition monitoring. Due to the noisy machine environment, sophisticated signal processing and decision-making algorithms are required to prevent false alarms or the missing of warning signals. This review outlines the state of the art in signal decomposition and both data- and model-based approaches to determination of the current state of the tool, and how these can be employed for IM tool condition monitoring. The development of such a system would help to ensure greater industrial uptake of additive manufacturing of injection mould tooling, by increasing confidence in the technology, further improving the efficiency and productivity of the sector.

【文献 59】
作者: Safyari, Y
来源: SENSORS
题目: A Review of Vision-Based Pothole Detection Methods Using Computer Vision
摘要: Potholes and other road surface damages pose significant risks to vehicles and traffic safety. The current methods of in situ visual inspection for potholes or cracks are inefficient, costly, and hazardous. Therefore, there is a pressing need to develop automated systems for assessing road surface conditions, aiming to efficiently and accurately reconstruct, recognize, and locate potholes. In recent years, various methods utilizing (a) computer vision, (b) three-dimensional (3D) point clouds, or (c) smartphone data have been employed to map road surface quality conditions. Machine learning and deep learning techniques have increasingly enhanced the performance of these methods. This review aims to provide a comprehensive overview of cutting-edge computer vision and machine learning algorithms for pothole detection. It covers topics such as sensing systems for acquiring two-dimensional (2D) and 3D road data, classical algorithms based on 2D image processing, segmentation-based algorithms using 3D point cloud modeling, machine learning, deep learning algorithms, and hybrid approaches. The review highlights that hybrid methods combining traditional image processing and advanced machine learning techniques offer the highest accuracy in pothole detection. Machine learning approaches, particularly deep learning, demonstrate superior adaptability and detection rates, while traditional 2D and 3D methods provide valuable baseline techniques. By reviewing and evaluating existing vision-based methods, this paper clarifies the current landscape of pothole detection technologies and identifies opportunities for future research and development. Additionally, insights provided by this review can inform the design and implementation of more robust and effective systems for automated road surface condition assessment, thereby contributing to enhanced roadway safety and infrastructure management.

【文献 60】
作者: Gao, JF
来源: ENERGIES
题目: A Review of Non-Destructive Testing for Lithium Batteries
摘要: With the rapid development of mobile devices, electronic products, and electric vehicles, lithium batteries have shown great potential for energy storage, attributed to their long endurance and high energy density. In order to ensure the safety of lithium batteries, it is essential to monitor the state of health and state of charge/discharge. There are commonly two methods for measuring lithium batteries: destructive testing and non-destructive testing. Destructive testing is not suitable for in situ or non-destructive analysis as it can cause irreversible deformation or damage to the battery. Herein, this review focuses on three non-destructive testing methods for lithium batteries, including ultrasonic testing, computer tomography, and nuclear magnetic resonance. Ultrasonic testing is widely used in crack and fatigue damage detection. X-ray computer tomography and neutron tomography have gained increasing attention in monitoring the health status of lithium batteries. Nuclear magnetic resonance can be used to conduct in situ and ex situ detection. In this review, non-destructive testing of lithium batteries is summarized, including the current status, achievements, and perspectives of this technology.

【文献 61】
作者: Zhao, S
来源: MEASUREMENT
题目: Bridge cable damage identification based on acoustic emission
摘要: Bridges have become an essential part of our transport networks. Therefore, the issue of cable damage detection in bridge health monitoring, specifically through acoustic emission (AE) technology, is of paramount importance for the nation and its citizens. This paper extensively investigates and compares various bridge cable damage identification techniques applied to AE. It can be categorized into two major classes: conventional parameter identification and machine learning identification. For the former, the signal's own feature parameter identification technique and the signal's mathematical index identification method are reviewed, summarizing their pros and cons. For the latter, both unsupervised and supervised learning are extensively surveyed. This paper introduces insights from AE monitoring in other six research fields, such as rail crack monitoring, and presents a basic process. Finally, the directions and recommendations for future research are proposed, providing useful suggestions for research in bridge cable damage identification and related domains.

【文献 62】
作者: Shilar, FA
来源: MATERIALS
题目: Exploring the Potential of Promising Sensor Technologies for Concrete
摘要: Structural health monitoring (SHM) is crucial for maintaining concrete infrastructure. The data collected by these sensors are processed and analyzed using various analysis tools under different loadings and exposure to external conditions. Sensor-based investigation on concrete has been carried out for technologies used for designing structural health monitoring sensors. A Sensor-Infused Structural Analysis such as interfacial bond-slip model, corroded steel bar, fiber-optic sensors, carbon black and polypropylene fiber, concrete cracks, concrete carbonation, strain transfer model, and vibrational-based monitor. The compressive strength (CS) and split tensile strength (STS) values of the analyzed material fall within a range from 26 to 36 MPa and from 2 to 3 MPa, respectively. The material being studied has a range of flexural strength (FS) and density values that fall between 4.5 and 7 MPa and between 2250 and 2550 kg/m3. The average squared difference between the predicted and actual compressive strength values was found to be 4.405. With cement ratios of 0.3, 0.4, and 0.5, the shear strength value ranged from 4.4 to 5.6 MPa. The maximum shear strength was observed for a water-cement ratio of 0.4, with 5.5 MPa, followed by a water-cement ratio of 0.3, with 5 MPa. Optimizing the water-cement ratio achieves robust concrete (at 0.50), while a lower ratio may hinder strength (at 0.30). PZT sensors and stress-wave measurements aid in the precise structural monitoring, enhanced by steel fibers and carbon black, for improved sensitivity and mechanical properties. These findings incorporate a wide range of applications, including crack detection; strain and deformation analysis; and monitoring of temperature, moisture, and corrosion. This review pioneers sensor technology for concrete monitoring (Goal 9), urban safety (Goal 11), climate resilience (Goal 13), coastal preservation (Goal 14), and habitat protection (Goal 15) of the United Nations' Sustainable Development Goals.

【文献 63】
作者: Hamdan, H
来源: HELIYON
题目: Prognosis methods of stress corrosion cracking under harsh environmental
摘要: Stress corrosion cracking (SCC) under harsh environmental conditions still poses a significant challenge, despite extensive research efforts. The intricate interplay among mechanical, chemical, and electrochemical factors hinders the accurate prognosis of material degradation and remaining service life. Furthermore, the demand for real-time monitoring and early detection of SCC defects adds further complexity to the prognostication process. Therefore, there is an urgent need for comprehensive review papers that consolidate current knowledge and advancements in prognosis methods. Such reviews would facilitate a better understanding and resolution of the challenges associated with SCC under harsh environmental conditions. This work aims to provide a comprehensive overview of various prognosis methods utilized for the assessment and prediction of SCC in such environments. The paper will delve into the following sections: exacerbating harsh environmental conditions, non-destructive testing (NDT) techniques, electrochemical techniques, numerical modeling, and machine learning. This review is inclined to serve as a valuable resource for researchers and practitioners working in the field, facilitating the development of effective strategies to mitigate SCC and ensure the integrity and reliability of materials operating in challenging environments. Despite considerable research, stress corrosion cracking in harsh environments remains a critical issue, complicated by the interplay of mechanical, chemical, and electrochemical factors. This review aims to consolidate current prognosis methods, including non-destructive testing, electrochemical techniques, numerical modeling, and machine learning. Key findings indicate that while traditional methods offer limited reliability, emerging computational approaches show promise for real-time, accurate predictions. The paper also briefly discusses notable SCC failure cases to underscore the urgency for improved prognosis techniques. This work aspires to fill knowledge gaps and serve as a resource for developing effective SCC mitigation strategies, thereby ensuring material integrity in challenging operational conditions.

【文献 64】
作者: Zhang, YB
来源: SENSORS
题目: A Review of NDT Methods for Wheel Burn Detection on Rails
摘要: Wheel burn can affect the wheel-rail contact state and ride quality. With long-term operation, it can cause rail head spalling or transverse cracking, which will lead to rail breakage. By analyzing the relevant literature on wheel burn, this paper reviews the characteristics, mechanism of formation, crack extension, and NDT methods of wheel burn. The results are as follows: Thermal-induced, plastic-deformation-induced, and thermomechanical-induced mechanisms have been proposed by researchers; among them, the thermomechanical-induced wheel burn mechanism is more probable and convincing. Initially, the wheel burns appear as an elliptical or strip-shaped white etching layer with or without deformation on the running surface of the rails. In the latter stages of development, this may cause cracks, spalling, etc. Magnetic Flux Leakage Testing, Magnetic Barkhausen Noise Testing, Eddy Current Testing, Acoustic Emission Testing, and Infrared Thermography Testing can identify the white etching layer, and surface and near-surface cracks. Automatic Visual Testing can detect the white etching layer, surface cracks, spalling, and indentation, but cannot detect the depth of rail defects. Axle Box Acceleration Measurement can be used to detect severe wheel burn with deformation.

【文献 65】
作者: Zhang, YS
来源: AUTOMATION IN CONSTRUCTION
题目: Artificial intelligence-enhanced non-destructive defect detection for
摘要: As civil engineering projects become more complex, ensuring the integrity of infrastructure is essential. Traditional inspection methods may damage structures, highlighting the need for non-destructive testing. However, conventional non-destructive methods involve challenges in assessing complex civil infrastructure due to manual operation and subjective interpretation. The integration of artificial intelligence has revolutionized nondestructive testing for civil infrastructure: it rapidly processes data, detects minor defects autonomously, and provides early warnings. This paper explores the significant advancements in artificial intelligence-enhanced non-destructive testing, particularly in radar detection, radiography, and sound-based technologies. Their synergy not only elevates the accuracy and efficiency of structural assessments but also extends the applicability of non-destructive testing techniques in order to address a broad spectrum of complex structural challenges more effectively. These advancements promise breakthroughs in automated inspections, real-time structural monitoring, and predictive maintenance, marking a significant leap forward in the field of civil infrastructure defect detection.

【文献 66】
作者: Li, QZ
来源: CASE STUDIES IN CONSTRUCTION MATERIALS
题目: Classification and application of deep learning in construction
摘要: In the ever-evolving landscape of construction engineering and management (CEM), the dynamic and unique characteristics of construction project environments constantly present multifaceted challenges. These challenges are characterized by the extensive volume of project-specific information and intricate engineering data. Deep learning (DL), with its advanced analytical capabilities, has been emerging as a robust solution to these complexities. While the application of DL in CEM is on an upward trajectory, a systematic review of its implementation is conspicuously lacking. This paper, therefore, embarks on a scientometric and qualitative analysis of 296 DLbased studies related to CEM from 2014 to 2024 in the renowned data science repositories Scopus, Science Direct and Web of Science to explore the characteristics of journals, keywords and clusters. It is found that six research topics have fully utilized the advantages of DL in CEM in the last decade, including construction equipment management, structural health monitoring, construction site safety management, construction schedule management, worker health management and workforce assessment and intelligent design. Then, the studies under each research topic are summarized separately and a searchable taxonomy is proposed that secondarily categorizes each study according to the specific CEM task and DL method used to facilitate understanding and access. Finally, the primary obstacles encountered in DL itself and in its practical application in CEM are discussed. It further articulates five critical future research directions that are evolving in tandem with advances in CEM, multimodal construction site management, realtime structural health monitoring and prediction, project progress visualization and management, intelligent design with data sharing and the incorporating large language models (LLM) for text data analysis. The three goals of this study are providing CEM researchers and practitioners with an in-depth and nuanced understanding of DL, elucidating the diverse nature of CEM activities and the resulting benefits of applying DL, and identifying future opportunities for applying DL in CEM to inform subsequent ongoing academic inquiry and pragmatic applications.

【文献 67】
作者: Rizelioglu, M
来源: ALEXANDRIA ENGINEERING JOURNAL
题目: An extensive bibliometric analysis of pavement deterioration detection
摘要: This study presents a current and extensive bibliometric analysis of pavement deterioration detection, monitoring, and assessment using various sensors alongside machine learning and deep learning algorithms. The impact of electronic sensors, machine learning, and deep learning on road pavement evaluation and monitoring within the transportation sector is highlighted. Conducting a bibliometric analysis covering research until March 1, 2024, 639 publications from 71 countries were examined. Productive countries, journals, institutions, and authors were analyzed and ranked. A standard research score and cumulative output score were calculated to normalize differences in the data. The findings reveal a significant recent increase in studies in this area. The most productive countries, journals, institutions, and authors are China, Transportation Research Record, Southeast University China, and Golroo Amir, respectively. This study serves as a valuable resource for both academic and industry researchers, offering insights into road pavement monitoring and guiding future research. In addition, accelerometer and GPS were the most used sensors, ANN and CNN were the most preferred algorithms, and cracks and potholes were the most studied topics. This study has the potential to be a good map for both academic and industrial researchers for monitoring the state of road pavements and a good guide.

【文献 68】
作者: Oreavbiere, A
来源: MACHINES
题目: Mathematical Complexities in Modelling Damage in Spur Gears
摘要: Analytical modelling is an effective approach to obtaining a gear dynamic response or vibration pattern for health monitoring and useful life prediction. Many researchers have modelled this response with various fault conditions commonly observed in gears. The outcome of such models provides a good idea about the changes in the dynamic response available between different gear health states. Hence, a catalogue of the responses is currently available, which ought to aid predictions of the health of actual gears by their vibration patterns. However, these analytical models are limited in providing solutions to useful life prediction. This may be because a majority of these models used single fault conditions for modelling and are not valid to predict the remaining life of gears undergoing more than one fault condition. Existing reviews related to gear faults and dynamic modelling can provide an overview of fault modes, methods for modelling and health prediction. However, these reviews are unable to provide the critical similarities and differences in the single-fault dynamic models to ascertain the possibility of developing models under combined fault modes. In this paper, existing analytical models of spur gears are reviewed with their associated challenges to predict the gear health state. Recommendations for establishing more realistic models are made especially in the context of modelling combined faults and their possible impact on gear dynamic response and health prediction.

【文献 69】
作者: Afsari, T
来源: ACS APPLIED NANO MATERIALS
题目: Nanorobotic System with Fine Control over Multiple Modes of Motion
摘要: Nanorobot development is at the frontier of advancing multiple fields, including biomedicine, oil recovery, environmental remediation, and crack detection and repair. The ability of nanorobots to impact these fields is dependent upon their ability to undergo varied modes of motion and the ability to finely control each mode based on the needed function. In this work we present a nanorobotic system that can produce multiple unique modes of motion with an unprecedented level of independent control over each component mode. These modes are generated by leveraging a dual ultrasonic-magnetic field actuation approach. Utilizing this approach, we also demonstrate the independent control of the spatial positioning and the mode of motion of the nanorobots. The nanorobotic system described is biocompatible which opens the door for its use in applications in the medical field. We present the use of this system for the removal of obstructions within model capillary vessels.

【文献 70】
作者: Qi, JJ
来源: JOURNAL OF ELECTRICAL ENGINEERING-ELEKTROTECHNICKY CASOPIS
题目: Non-destructive testing of human teeth using microwaves: a
摘要: Tooth diseases including dental caries, periodontitis and cracks have been public health problems globally. How to detect them at the early stage and perform thorough diagnosis are critical for the treatment. The diseases can be viewed as defects from the perspective of non-destructive testing. Such a defect can affect the material properties (e.g., optical, chemical, mechanical, acoustic, density and dielectric properties). A non-destructive testing method is commonly developed to sense the change of one particular property. Microwave testing is one that is focused on the dielectric properties. In recent years, this technique has received increased attention in dentistry. Here, the dielectric properties of human teeth are presented first, and the measurement methods are addressed. Then, the research progress on the detection of teeth over the last decade is reviewed, identifying achievements and challenges. Finally, the research trends are outlined, including electromagnetic simulation, radio frequency identification and heating-based techniques.

【文献 71】
作者: Ji, AK
来源: JOURNAL OF CIVIL ENGINEERING AND MANAGEMENT
题目: SCIENTOMETRIC ANALYSIS OF PAVEMENT MAINTENANCE: A TWENTY-YEAR REVIEW
摘要: Pavement maintenance is widely thought to be critical for promoting sustainability, playing a pivotal role in sustainable and resilient transportation infrastructure for growth in economic development and improvements in social inclusion. It has attracted increasing attention from both academia and industry over the past 20 years. Although several literature reviews have been conducted, there is still a lack of systematic quantitative and visual investigation of the structure and evolution of knowledge in this field. To address this lack, reported here is a comprehensive and objective scientometric analysis to visualize the status quo of research areas regarding pavement maintenance. Focusing on 614 journal articles collected from the Web of Science for 2001-2020, key researchers within the field are identified, as are the key research institutions, key countries, and their interconnections, as well as keywords, evolution trends, key publications, and citation patterns, along with the extent to which these interact with each other in research networks. Based on the in-depth analysis, a knowledge roadmap is provided to inscribe how pavement maintenance-related research evolves over time, greatly contributing to the understanding of the underlying structure of pavement maintenance, and to highlight the identified current research challenges and future research trends, thus potentially benefiting the academic community and practice field on multiple themes of pavement maintenance. The results of this research are instructive, providing a broad overview and holistic thinking for researchers and practitioners with respect to pavement maintenance research, as well as facilitating further research and applications for both academia and industry in improving pavement maintenance for sustainability.

【文献 72】
作者: Spencer, BF Jr
来源: KSCE JOURNAL OF CIVIL ENGINEERING
题目: Advances in artificial intelligence for structural health monitoring: A
摘要: The deterioration of civil infrastructure presents a critical economic and societal challenge, necessitating the development of advanced and efficient monitoring strategies. Artificial intelligence (AI) has recently emerged as a powerful tool for structural health monitoring (SHM) that considerably improves accuracy, robustness, and operational efficiency. Early AI applications focused predominantly on vibration-based monitoring, enabling automated and data-driven damage detection processes. As AI techniques have advanced, their scope has expanded to large-scale data analyses, thereby significantly enhancing predictive maintenance strategies. Trends toward the integration of AI with vision-based methods have recently increased, further advancing damage detection and facilitating the digital transformation of civil infrastructure monitoring. AI has also been instrumental in achieving precise structural displacement tracking and load assessment. This review critically examines the progression of AI in SHM by tracing its evolution from vibration-based methods to the incorporation of vision-based techniques, including damage detection, digital transformation, and measurement. Furthermore, this paper discusses the key challenges associated with deploying AI solutions in real-world environments while highlighting future research directions and potential innovations within this rapidly evolving field.

【文献 73】
作者: Collina, G
来源: INTERNATIONAL JOURNAL OF HYDROGEN ENERGY
题目: Multi-stage monitoring of hydrogen systems for improved maintenance
摘要: Hydrogen is considered a promising solution for global decarbonisation as an alternative to fossil fuels. However, it can interact with and brittle most metallic materials and is highly flammable. These properties call for a systematic investigation of physical and chemical hazards and for the definition of a comprehensive risk management and monitoring framework, including proper maintenance planning. This study aims at establishing a hydrogen monitoring scheme and it provides a descriptive, bibliometric, and interpretative review of the current state-of-the-art of suitable techniques to ensure the safe handling of hydrogen systems. The descriptive analysis outlines the technologies available to supervise the hydrogen-material interactions and detect hydrogen leaks and flames. The bibliometric analysis shows quantitative data to identify the most relevant research groups. The interpretative study discusses the findings and examines the possibility of combining the identified techniques with maintenance programs to prevent catastrophic events.

【文献 74】
作者: Alshammari, YLA
来源: APPLIED SCIENCES-BASEL
题目: Fundamental Challenges and Complexities of Damage Identification from
摘要: For many years, structural health monitoring (SHM) has held significant importance across diverse engineering sectors. The main aim of SHM is to assess the health status and understand distinct features of structures by analyzing real-time data from physical measurements. The dynamic response (DR) is a significant tool in SHM studies. This response is used primarily to detect variations or damage by examining the vibration signals of DR. Numerous scholarly articles and reviews have discussed the phenomenon and importance of using DR to predict damages in uniform thickness (UT) plate structures. However, previous reviews have predominantly focused on the UT plates, neglecting the equally important varying thickness (VT) plate structures. Given the significance of VT plates, especially for academic researchers, it is essential to compile a comprehensive review that covers the vibration of both the UT and VT cracked plate structures and their identification methods, with a special emphasis on VT plates. VT plates are particularly significant due to their application in critical components of various applications where optimizing the weight, aerodynamics, and dimensions is crucial to meet specific design specifications. Furthermore, this review critically evaluates the damage identification methods, focusing on their accuracy and applicability in real-world applications. This review revealed that current research studies are inadequate in describing crack path identification; they have primarily focused on predicting the quantification of cracks in terms of size or possible location. Identifying the crack path is crucial to avoid catastrophic failures, especially in scenarios where the crack may propagate in critical dimensions of the plate. Therefore, it can be concluded that an accurate analytical and empirical study of crack path and damage identification in these plates would be a novel and significant contribution to the academic field.

【文献 75】
作者: Jeon, D
来源: BUILDINGS
题目: Electrical Resistance Tomography (ERT) for Concrete Structure
摘要: Electrical resistance tomography (ERT) is gaining recognition as an effective, affordable, and nondestructive tool for monitoring and imaging concrete structures. This paper discusses ERT's applications, including crack detection, moisture ingress monitoring, steel reinforcement assessment, and chloride level profiling within concrete. Recent advancements, such as time-lapse ERT and artificial intelligence (AI) integration, have enhanced image resolution and provided detailed data for infrastructure monitoring. However, challenges remain regarding the need for better spatial resolution, concrete-compatible electrodes, and integration with other nondestructive testing techniques. Addressing these issues will expand the applicability and reliability of the current ERT, making it an invaluable tool for infrastructure maintenance and monitoring.

【文献 76】
作者: Giannuzzi, V
来源: APPLIED SCIENCES-BASEL
题目: Historic Built Environment Assessment and Management by Deep Learning
摘要: Recent advancements in digital technologies and automated analysis techniques applied to Historic Built Environment (HBE) demonstrate significant advantages in efficiently collecting and interpreting data for building conservation activities. Integrating digital image processing through Artificial Intelligence approaches further streamlines data analysis for diagnostic assessments. In this context, this paper presents a scoping review based on Scopus and Web of Science databases, following the PRISMA protocol, focusing on applying Deep Learning (DL) architectures for image-based classification of decay phenomena in the HBE, aiming to explore potential implementations in decision support system. From the literature screening process, 29 selected articles were analyzed according to methods for identifying buildings' surface deterioration, cracks, and post-disaster damage at a district scale, with a particular focus on the innovative DL architectures developed, the accuracy of results obtained, and the classification methods adopted to understand limitations and strengths. The results highlight current research trends and the potential of DL approaches for diagnostic purposes in the built heritage conservation field, evaluating methods and tools for data acquisition and real-time monitoring, and emphasizing the advantages of implementing the adopted techniques in interoperable environments for information sharing among stakeholders. Future challenges involve implementing DL models in mobile apps, using sensors and IoT systems for on-site defect detection and long-term monitoring, integrating multimodal data from non-destructive inspection techniques, and establishing direct connections between data, intervention strategies, timing, and costs, thereby improving heritage diagnosis and management practices.

【文献 77】
作者: Fan, LX
来源: APPLIED SCIENCES-BASEL
题目: How to Make a State of the Art Report-Case Study-Image-Based Road Crack
摘要: With the rapid growth in urban construction in Malaysia, road breakage has challenged traditional manual inspection methods. In order to quickly and accurately detect the extent of road breakage, it is crucial to apply automated road crack detection techniques. Researchers have long studied image-based road crack detection techniques, especially the deep learning methods that have emerged in recent years, leading to breakthrough developments in the field. However, many issues remain in road crack detection methods using deep learning techniques. The field lacks state-of-the-art systematic reviews that can scientifically and effectively analyze existing works, document research trends, summarize outstanding research results, and identify remaining shortcomings. To conduct a systematic review of the relevant literature, a bibliometric analysis and a critical analysis of the papers published in the field were performed. VOSviewer and CiteSpace text mining tools were used to analyze and visualize the bibliometric analysis of some parameters derived from the articles. The history and current status of research in the field by authors from all over the world are elucidated and future trends are analyzed.

【文献 78】
作者: Gohari, H
来源: SENSORS
题目: Cyber-Physical Systems for High-Performance Machining of Difficult to
摘要: The fifth Industrial revolution (I5.0) prioritizes resilience and sustainability, integrating cognitive cyber-physical systems and advanced technologies to enhance machining processes. Numerous research studies have been conducted to optimize machining operations by identifying and reducing sources of uncertainty and estimating the optimal cutting parameters. Virtual modeling and Tool Condition Monitoring (TCM) methodologies have been developed to assess the cutting states during machining processes. With a precise estimation of cutting states, the safety margin necessary to deal with uncertainties can be reduced, resulting in improved process productivity. This paper reviews the recent advances in high-performance machining systems, with a focus on cyber-physical models developed for the cutting operation of difficult-to-cut materials using cemented carbide tools. An overview of the literature and background on the advances in offline and online process optimization approaches are presented. Process optimization objectives such as tool life utilization, dynamic stability, enhanced productivity, improved machined part quality, reduced energy consumption, and carbon emissions are independently investigated for these offline and online optimization methods. Addressing the critical objectives and constraints prevalent in industrial applications, this paper explores the challenges and opportunities inherent to developing a robust cyber-physical optimization system.

【文献 79】
作者: Swit, G
来源: MATERIALS
题目: Non-Destructive Testing Methods for In Situ Crack Measurements and
摘要: This article presents a concise review of modern non-destructive testing (NDT) methods that allow the detection, tracking, and measurement of cracks in reinforced concrete structures. Over the past decades, the range of solutions available on the market has increased. This provides excellent opportunities when choosing and designing systems for diagnosing and continuously monitoring structures. Cracking affects the mechanical properties, durability, and serviceability of a structure or its elements. Therefore, there is a need to develop methods that would allow the determination of the moment of a destructive process's formation, i.e., a crack's appearance. At the same time, it is crucial to be able to track the development of cracks for the entire structure, not just selected locations. This work also presents the concept of combining selected NDT methods and creating a system for the continuous monitoring of structural integrity and predicting changes in the durability of existing and future buildings.

【文献 80】
作者: Fang, CC
来源: FRONTIERS IN EARTH SCIENCE
题目: Review on detection method, main source and geological application of
摘要: Diamondoids are alkanes with cage-like structure. Their diamond-like structure makes them have high stability and anti-biodegradability, and can be preserved and enriched in complex and long geological processes. Therefore, the continuous development of quantitative detection methods for diamondoids in crude oil has deepened the research of these compounds and made them more widely used in crude oil cracking evaluation, maturity evaluation, biodegradation evaluation and other aspects.

【文献 81】
作者: Alsuhaibani, E
来源: POLYMERS
题目: Nondestructive Testing of Externally Bonded FRP Concrete Structures: A
摘要: The growing application of Fiber-Reinforced Polymer (FRP) composites in rehabilitating deteriorating concrete infrastructure underscores the need for reliable, cost-effective, and automated nondestructive testing (NDT) methods. This review provides a comprehensive analysis of existing and emerging NDT techniques used to assess externally bonded FRP (EB-FRP) systems, emphasizing their accuracy, limitations, and practicality. Various NDT methods, including Ground-Penetrating Radar (GPR), Phased Array Ultrasonic Testing (PAUT), Infrared Thermography (IRT), Acoustic Emission (AE), and Impact-Echo (IE), are critically evaluated in terms of their effectiveness in detecting debonding, voids, delaminations, and other defects. Recent technological advancements, particularly the integration of artificial intelligence (AI) and machine learning (ML) in NDT applications, have significantly improved defect characterization, automated inspections, and real-time data analysis. This review highlights AI-driven NDT approaches such as automated crack detection, hybrid NDT frameworks, and drone-assisted thermographic inspections, which enhance accuracy and efficiency in large-scale infrastructure assessments. Additionally, economic considerations and cost-performance trade-offs are analyzed, addressing the feasibility of different NDT methods in real-world FRP-strengthened structures. Finally, the review identifies key research gaps, including the need for standardization in FRP-NDT applications, AI-enhanced defect quantification, and hybrid inspection techniques. By consolidating state-of-the-art research and emerging innovations, this paper serves as a valuable resource for engineers, researchers, and practitioners involved in the assessment, monitoring, and maintenance of FRP-strengthened concrete structures.

【文献 82】
作者: EL-Molla, DA
来源: ARABIAN JOURNAL FOR SCIENCE AND ENGINEERING
题目: Seepage Control, Detection, and Treatment in Embankment Dams: A
摘要: This study reviews and evaluates the seepage control, detection, and treatment methods of embankment dams. The progress of knowledge in this field during the last two decades is presented. The optimum designs of various seepage control measures (drains, cores, and seepage control barriers) are discussed based on the advances in research. Moreover, the technologies and best practices used to detect and treat unwanted seepage are discussed. Reviewing the previous literature showed many advancements in the designs and materials of seepage control elements. On the other side, all seepage control elements are vulnerable to defects. Hence, the combined usage of drains with cores and seepage control barriers inside the dam or its foundation is the optimum practice. This provides a multiple defense strategy against seepage and ensures having a backup plan in case of core cracking, seepage barrier defects, or drain clogging. Seepage detection methods have also greatly progressed, from geotechnical methods to dye and temperature tracing and geoelectric methods. Yet, all methods have their advantages and limitations, which makes combining different methods more favorable to accurately monitor seepage and capture all defects. Finally, continuous monitoring, early detection, accurate diagnosis, and prompt efficient treatment are essential for the safety of embankment dams, as noticed from the presented case studies. This study presents useful insights that help the designers of embankment dams adopt the best seepage control, detection, and treatment practices. Some research gaps that should be addressed in future studies are also highlighted.

【文献 83】
作者: Prakash, V
来源: APPLIED SCIENCES-BASEL
题目: Structural Health Monitoring of Concrete Bridges Through Artificial
摘要: Concrete has been one of the most essential building materials for decades, valued for its durability, cost efficiency, and wide availability of required components. Over time, the number of concrete bridges has been drastically increasing, highlighting the need for timely structural health monitoring (SHM) to ensure their safety and long-term durability. Therefore, a narrative review was conducted to examine the use of Artificial Intelligence (AI)-integrated techniques in the SHM of concrete bridges for more effective monitoring. Moreover, this review also examined significant damage observed in various types of concrete bridges, with particular emphasis on concrete cracking, detection methods, and identification accuracy. Evidence points to the fact that the conventional SHM of concrete bridges relies on manual inspections that are time-consuming, error-prone, and require frequent checks, while AI-driven SHM methods have emerged as promising alternatives, especially through Machine Learning- and Deep Learning-based solutions. In addition, it was noticeable that integrating multimodal AI approaches improved the accuracy and reliability of concrete bridge assessments. Furthermore, this review is essential as it also addresses critical gaps in SHM approaches and suggests developing more accurate detection techniques, providing enhanced spatial resolution for monitoring concrete bridges.

【文献 84】
作者: Ghattas, A
来源: APPLIED SCIENCES-BASEL
题目: Detecting Defects in Materials Using Nondestructive Microwave Testing
摘要: Microwave nondestructive testing (MNDT) has shown great potential in detecting defects in various materials. This is due to it being safe and noninvasive. Safety is essential for the operators as well as the specimens being tested. Being noninvasive is important in maintaining the health of critical structures and components across various industries. In this paper, a review of MNDT methods is given with a comparison against other NDT techniques. First, the latter techniques are described, namely testing using a dye penetrant, ultrasound, eddy currents, magnetic particles, or radiography. Next, an overview of various microwave NDT methods is provided through a review of the applications, advantages, and limitations of each technique. Further, a detailed review of emerging MNDT techniques like microwave microscopy, active microwave thermography, and chipless radio frequency identification is presented. Next, a brief description of current and emerging algorithms employed in MNDT is discussed, with emphasis on those using artificial intelligence. By providing a comprehensive review, this article aims to shed light on the current state of MNDT, thus serving as a reference for subsequent innovations in this rapidly evolving domain.

【文献 85】
作者: Zhang, XL
来源: ELECTRONICS
题目: A Scoping Review: Applications of Deep Learning in Non-Destructive
摘要: Background: In the context of rapid urbanization, the need for building safety and durability assessment is becoming increasingly prominent. Objective: The aim of this paper is to review the strengths and weaknesses of the main non-destructive testing (NDT) techniques in construction engineering, with a focus on the application of deep learning in image-based NDT. Design: We surveyed more than 80 papers published within the last decade to assess the role of deep learning techniques combined with NDT in automated inspection in construction. Results: Deep learning significantly enhances defect detection accuracy and efficiency in construction NDT, particularly in image-based techniques such as infrared thermography, ground-penetrating radar, and ultrasonic inspection. Multi-technology fusion and data integration effectively address the limitations of single methods. However, challenges remain, including data complexity, resolution limitations, and insufficient sample sizes in NDT images, which hinder deep learning model training and optimization. Conclusions: This paper not only summarizes the existing research results, but also discusses the future optimization direction of the target detection network for NDT defect data, aiming to promote intelligent development in the field of non-destructive testing of buildings, and to provide more efficient and accurate solutions for building maintenance.

【文献 86】
作者: Zhu, JY
来源: BIORESOURCES
题目: Progress in the Study of Dry Shrinkage Deformation and Drying Stress of
摘要: Bamboo is a sustainable material that supports carbon sequestration and helps address the imbalance of timber supply vs. demand. Drying is a crucial step in bamboo processing, in the course of which shrinkage and stress accumulation can lead to defects such as cracking and deformation. Understanding stress and strain development during drying is critical for improving bamboo processing. This review paper explores bamboo's gradient structure and moisture migration characteristics, focusing on the mechanisms behind shrinkage strain formation and the sources of stress. It reviews literature on bamboo drying and cellular structural changes, evaluating the evolution of stress and strain testing methods, from traditional sectioning techniques to advanced methods such as digital imaging and acoustic emission. The paper also summarizes progress in stress-strain research at both macroscopic and cellular scales. Current challenges include species-specific shrinkage variations, limitations in measurement techniques, and insufficient research on shrinkage above the fiber saturation point. To address these issues, the study recommends developing universal theoretical models, employing advanced detection technologies, comparing shrinkage patterns between bamboo culms and nodes, exploring drying stress composition, and adopting multi-scale research approaches. These strategies aim to enhance the quality of bamboo processing and promote higher-value applications within the industry.

【文献 87】
作者: Aromoye, IA
来源: CMES-COMPUTER MODELING IN ENGINEERING & SCIENCES
题目: Significant Advancements in UAV Technology for Reliable Oil and Gas
摘要: Unmanned aerial vehicles (UAVs) technology is rapidly advancing, offering innovative solutions for various industries, including the critical task of oil and gas pipeline surveillance. However, the limited flight time of conventional UAVs presents a significant challenge to comprehensive and continuous monitoring, which is crucial for maintaining the integrity of pipeline infrastructure. This review paper evaluates methods for extending UAV flight endurance, focusing on their potential application in pipeline inspection. Through an extensive literature review, this study identifies the latest advancements in UAV technology, evaluates their effectiveness, and highlights the existing gaps in achieving prolonged flight operations. Advanced techniques, including artificial intelligence (AI), machine learning (ML), and deep learning (DL), are reviewed for their roles in pipeline monitoring. Notably, DL algorithms like You Only Look Once (YOLO) are explored for autonomous flight in UAV-based inspections, real-time defect detection, such as cracks, corrosion, and leaks, enhancing reliability and accuracy. A vital aspect of this research is the proposed deployment of a hybrid drone design combining lighter-than-air (LTA) and heavier-than-air (HTA) principles, achieving a balance of endurance and maneuverability. LTA vehicles utilize buoyancy to reduce energy consumption, thereby extending flight durations. The paper details the methodology for designing LTA vehicles, presenting an analysis of design parameters that align with the requirements for effective pipeline surveillance. The ongoing work is currently at Technology Readiness Level (TRL) 4, where key components have been validated in laboratory conditions, with fabrication and flight testing planned for the next phase. Initial design analysis indicates that LTA configurations could offer significant advantages in flight endurance compared to traditional UAV designs. These findings lay the groundwork for future fabrication and testing phases, which will be critical in validating and assessing the proposed approach's realworld applicability. By outlining the technical complexities and proposing specialized techniques tailored for pipeline monitoring, this paper provides a foundational framework for advancing UAV capabilities in the oil and gas sector. Researchers and industry practitioners can use this roadmap to further develop UAV-enabled surveillance solutions, aiming to improve the reliability, efficiency, and safety of pipeline monitoring.

【文献 88】
作者: Oswald-Tranta, B
来源: QUANTITATIVE INFRARED THERMOGRAPHY JOURNAL
题目: Inductive thermography - review of a non-destructive inspection
摘要: Inductive thermography is an excellent inspection technique for detecting defects in metallic materials. The technique has been greatly improved over the last few decades, from laboratory experiments to industrial applications. Many researches have studied the theory and the physical processes behind inductive thermography and also how the experimental setup and the evaluation of the results can be improved. The purpose of this paper is to give an overview of the theory and also of the most important technical points which should be taken into consideration for the usage of this technique. However, if inductive thermography can be used to locate various types of defects, this paper focuses on the detection of surface cracks in metals. In addition, several examples are given of how this inspection technique is used nowadays in industrial applications.

【文献 89】
作者: Lee, DH
来源: INTERNATIONAL JOURNAL OF MICRO AIR VEHICLES
题目: Design and control of wall-attaching and moving quadcopter using flaps
摘要: This study proposed a quadcopter equipped with flaps designed to attach to and move along walls, enabling safe photography of square-shaped bridge piers. It was verified through experiments whether flaps can generate sufficient and continuous force in the direction of the wall. Additionally, a mathematical model of the quadcopter with flaps was developed using lift and drag measurement data. Through simulations, it was confirmed that the drone could be controlled using the lift generated by the flaps. A prototype quadcopter with applied flaps was then created. An algorithm for processing essential sensor signals, such as attitude and speed, was presented for effective quadcopter control. Moreover, an algorithm utilizing two distance sensors was proposed to measure the angle and distance between the quadcopter and the wall. A controller was also designed to effectively manage the speed and yaw angle of the quadcopter. The developed quadcopter was validated through real experiments, demonstrating its ability to attach to walls and move along the bridge structure.

【文献 90】
作者: Mei, T
来源: PROCESSES
题目: Mapping the Knowledge Domain of Pressure Vessels and Piping Fields for
摘要: With the rapid advancement of modern industries, pressure vessels and piping have become increasingly integral to sectors such as energy, petrochemicals, and process industries. To grasp the research and application status in the field of pressure vessel and piping safety, 670 publications in the Web of Science core database from 2008 to 2024 were taken as data samples in this paper. The knowledge mapping tools were used to carry out co-occurrence analysis, keyword burst detection, and co-citation analysis. The results show that the research in this field presents a multidisciplinary and cross-disciplinary state, involving multiple disciplines such as Nuclear Science and Technology, Engineering Mechanics, and Energy and Fuels. The "International Journal of Hydrogen Energy", "International Journal of Pressure Vessels and Piping", and "Nuclear Engineering and Design" are the primary publication outlets in this domain. The study identifies three major research hotspots: (1) the safety performance of pressure vessels and piping, (2) structural integrity, failure mechanisms, and stress analysis, and (3) numerical simulation and thermal-hydraulic analysis under various operating conditions. The current challenges can be summarized into three aspects: (1) addressing the safety risks brought by new technologies and materials, (2) promoting innovation and the application of detection and monitoring technologies, and (3) strengthening the building capacity for accident prevention and emergency management. Specific to China, the current challenges include the safety and management of aging equipment, the effective detection of circumferential weld cracks, the refinement of risk assessment models, and the advancement of smart technology applications. These findings offer valuable insights for advancing safety practices and guiding future research in this multidisciplinary field.

【文献 91】
作者: Qiao, Q
来源: IEEE ACCESS
题目: A Review of Metal Surface Defect Detection Technologies in Industrial
摘要: Surface defects, including cracks, scratches, and deformations, significantly affect the product performance and service life in industrial manufacturing. Accurate detection of metal surface defects ensures product quality and reliability. Traditional nondestructive testing (NDT) methods, such as ultrasonic and eddy current inspections, are effective but face challenges in terms of efficiency and scalability for industrial applications. This review examines the recent advancements in automated defect detection, focusing on image acquisition, image processing, and detection techniques. The study also evaluates state-of-the-art machine learning and deep learning methods used in defect detection, including the Symmetric Convolutional Network (SCN), Swin Transformer You Only Look Once (ST-YOLO), and Statistical Texture Feature Enhancement Network (STFE-Net), which achieve high accuracy in defect classification and detection. In addition, advanced three-dimensional (3D) detection methods such as photometric stereo, light-field imaging, and structured light have been explored for their potential to address challenges such as real-time detection, few-shot detection, and small-target detection. The study concludes by identifying current limitations and proposing future directions for enhancing industrial defect detection systems.

【文献 92】
作者: Silitonga, DJ
来源: ACTA ACUSTICA
题目: A comprehensive study of non-destructive localization of structural
摘要: Metal plate structures, crucial components in various industrial sectors, demand meticulous inspection methods for the maintenance of their structural integrity. This review article not only serves as a contemporary introduction to this research field but also underlines the vital role of this field in ensuring the safety and reliability of these structures. The study delves into Lamb wave generation and detection techniques, highlighting the challenges and advancements in transducer technologies. Two detailed case studies are presented to contextualize and illustrate the practical applications of these techniques. The first case study demonstrates the detection of weld joints and stiffeners in steel plates, particularly relevant to the shipbuilding industry. Through a combination of numerical simulations and experimental validations designed for this narrative, this study highlights the capability of the A0 Lamb wave mode in identifying these features. The second case study, equally supported by new experiments, focuses on detecting thickness reductions in aluminum plates using high-order Lamb modes in a multimodal excitation setup. This scenario simulates conditions such as corrosion or wear that induce material thinning. By creating blind holes of varying depths on one side of the plate and conducting inspections from the opposite side, the study demonstrates the method's precision in identifying hidden defects. The case studies involving aluminum and steel specimens exemplify the efficacy of Lamb waves in the nondestructive evaluation of metal plates. They provide critical insights into the method's ability to deliver precise and efficient detection of structural anomalies despite inherent challenges in signal interpretation and analysis.

【文献 93】
作者: Casas, JR
来源: REMOTE SENSING
题目: Remote Sensing in Bridge Digitalization: A Review
摘要: A review of the application of remote sensing technologies in the SHM and management of existing bridges is presented, showing their capabilities and advantages, as well as the main drawbacks when specifically applied to bridge assets. The main sensing technologies used as corresponding platforms are discussed. This is complemented by the presentation of five case studies emphasizing the wide field of application in several bridge typologies and the justification for the selection of the optimal techniques depending on the objectives of the monitoring and assessment of a particular bridge. The review shows the potentiality of remote sensing technologies in the decision-making process regarding optimal interventions in bridge management. The data gathered by them are the mandatory precursors for determining the relevant performance indicators needed for the quality control of these important infrastructure assets.

【文献 94】
作者: Peng, ZJ
来源: SENSORS
题目: A Comprehensive Survey on Visual Perception Methods for Intelligent
摘要: There are many high dam hubs in the world, and the regular inspection of high dams is a critical task for ensuring their safe operation. Traditional manual inspection methods pose challenges related to the complexity of the on-site environment, the heavy inspection workload, and the difficulty in manually observing inspection points, which often result in low efficiency and errors related to the influence of subjective factors. Therefore, the introduction of intelligent inspection technology in this context is urgently necessary. With the development of UAVs, computer vision, artificial intelligence, and other technologies, the intelligent inspection of high dams based on visual perception has become possible, and related research has received extensive attention. This article summarizes the contents of high dam safety inspections and reviews recent studies on visual perception techniques in the context of intelligent inspections. First, this article categorizes image enhancement methods into those based on histogram equalization, Retinex, and deep learning. Representative methods and their characteristics are elaborated for each category, and the associated development trends are analyzed. Second, this article systematically enumerates the principal achievements of defect and obstacle perception methods, focusing on those based on traditional image processing and machine learning approaches, and outlines the main techniques and characteristics. Additionally, this article analyzes the principal methods for damage quantification based on visual perception. Finally, the major issues related to applying visual perception techniques for the intelligent safety inspection of high dams are summarized and future research directions are proposed.

【文献 95】
作者: Khan, ZU
来源: IEEE ACCESS
题目: A Review of Degradation and Reliability Analysis of a Solar PV Module
摘要: Affordability, Long-term warranty, scalability, as well as continuous decline in the LCOE (levelized cost of electricity) of PV (Photovoltaic) in many nations, are largely responsible for the current enormous up thrust in global installation of solar PV modules, at residential roof-top as well as utility-scale systems. Also, as the world's energy portfolio evolves toward cleaner energy sources, PV deployment is anticipated to maintain this increasing trend. Despite this, these PV modules are subjected to a broad range of climatic conditions in outdoor application, regardless of the PV module material/type and technology used. These modules are frequently subjected to high chemical, photochemical, and thermomechanical stress because of the reason that these PV modules are exposed to extreme environmental conditions. In addition to manufacturing flaws, these circumstances have a greater impact on the aging rate, flaws, and degradation of PV modules. As a result, different investigations on PV dependability and degradation mechanisms have been conducted recently. These studies not only shed light on how the performance of PV modules deteriorates with time, but most importantly, they provide useful input for future advancements in PV technology and performance forecasting for more accurate financial modeling. Due to this, it is crucial to quickly and accurately identify and classify the degradation modes and mechanisms caused by manufacturing flaws and environmental factors in order to reduce the likelihood of failure and the risks that go along with it. Visual examination, EPM (Electrical-Parameter-Measurements), imaging techniques, and latest data-driven techniques have all been suggested and used in the literature to assess or describe the deterioration signatures and mechanisms/pathways of PV modules. This report offers a critical analysis of recent research that examined the performance reliability and deterioration of solar PV systems. The objective is to identify important topics, advance the state of the art, and offer well-considered suggestions for upcoming research, especially in the field of data-driven analytics. Data-driven analytical methods, such as DL (Deep Learning) ML (Machine Learning) models, have astounding computational abilities to process large amounts of data, with diverse features, and with minimal computation time. This contrasts with visual inspection and statistical approaches, which are more time-intensive and require significant human experience. They can therefore be used to evaluate the performance of a module in manufacturing, field deployments, and lab settings. DL and ML can observe erratic patterns and draw useful conclusions in the classification, diagnosis, and prediction of PV performance degradation signatures thanks to the enormous size of PV module installations, particularly in systems for utility scales, and the large datasets produced in terms of features from imaging and EPM data. In terms of methodology, datasets, characterization techniques, accelerated testing procedures, feature extraction mechanisms, classification procedures, analysis and comparison and critical analysis of solar PV deterioration models. Lastly, we briefly describe any potential research gaps and briefly list some suggestions for additional research.

【文献 96】
作者: Manoni, L
来源: IEEE ACCESS
题目: Recent Advancements in Deep Learning Techniques for Road Condition
摘要: Road Condition Monitoring is a critical task for the management and maintenance of the pavement network infrastructure by the authorities. In recent years, the application of Artificial Intelligence (AI) techniques in this domain has experienced a significant growth, driven by the continuous advancements in AI algorithms. This paper presents a comprehensive review of the latest developments in Road Condition Monitoring approaches using AI methods, with a particular focus on Deep Learning techniques, covering works published from 2020 onwards. It highlights novel approaches that have not been thoroughly explored in previous literature reviews. The literature review categorizes studies based on the type of signal data, distinguishing between acoustic, vibrational, and vision-based approaches. For each data type, the paper examines and discuss the most recent advancements and improvements achieved through AI techniques. Additionally, it provides an overview of future directions and identifying key challenges that remain open in the field. In conclusion, relatively few studies have focused on the analysis of acoustic data, although some studies have reported promising results. Methods based on vibrational data typically integrate feature extraction in frequency and wavelet domain with Convolutional Neural Networks or Long Short-Term Memory Networks. Meanwhile, vision-based methods have experienced significant improvements, driven by the constant evolution of Deep Learning architectures. A total of 173 research articles are summarized across 10 tables.

【文献 97】
作者: Zivkovic, VB
来源: THERMAL SCIENCE
题目: THE INSTIGATING FACTORS BEHIND THE OCCURRENCE OF VIBRATION IN STEAM
摘要: The present study focuses on elucidating the fundamental reasons underlying the emergence of vibrations in steam turbines. During operation, vibrations are observed not only in the components of the machinery that undergo cyclical motion but also in those components connected to the equipment. Therefore, vibration monitoring holds great importance in identifying malfunctions in the functional operations of turbomachinery, enabling timely detection, and prevention of potential accidents. Using the steam turbine unit as an example, it is noteworthy that the rotor primarily undergoes oscillatory motion, where it is essential to recognize that vibrations also manifest in bearings, housings, turbine foundations, pipe-lines, and surrounding components. The thorough examination of vibration should encompass not only turbine rotors but also the entire turbine assembly, including the generator and all associated equipment. It is essential to conduct a comprehensive evaluation of the overall system to ensure optimal functionality. Academic research papers typically do not often assess the specific number of working hours and conditions which are leading to rotor damage, also in that sense, not determining if damage is a result of wear and tear during prolonged undesired operation. Instead, the emphasis is commonly placed on analyzing elevated levels of vibrations and investigating the associated occurrence of cracks. This paper aims to provide a comprehensive summary of the main causes of vibrations through a unified perspective on the various conclusions available, regarding the diverse causes behind these common and complex vibration occurrences.

98、Defect transformer: An efficient hybrid transformer architecture for surface defect detection
Abtract:Surface defect detection is an extremely crucial step to ensure the quality of industrial products. Nowadays, convolutional neural networks (CNNs) based on encoder–decoder architecture have achieved tremendous success in various defect detection tasks. However, the intrinsic locality of convolution prevents them from modeling long-range interactions explicitly, making it difficult to distinguish pseudo-defects in cluttered backgrounds. Recent transformers are especially skilled at learning global image dependencies, but with limited local structural information for the refined defect location. To overcome the above limitations, we incorporate CNN and transformer into an efficient hybrid transformer architecture for defect detection, termed Defect Transformer (DefT), to capture local and non-local relationships collaboratively. Specifically, in the encoder module, a convolutional stem block is firstly adopted to retain more spatial details. Then, the patch aggregation blocks are used to generate multi-scale representation with four hierarchies, each of them is followed by a series of DefT blocks, which respectively include a locally position-aware block for local position encoding, a lightweight multi-pooling self-attention to model multi-scale global contextual relationships with good computational efficiency, and a convolutional feed-forward network for feature transformation and further local information learning. Finally, a simple but effective decoder module is constructed to gradually recover spatial details from the skip connections in the encoder. Extensive experiments on three datasets demonstrate the superiority and efficiency of our method compared with other deeper and complex CNN- and transformer-based networks.

99、Multi-Objects Recognition and Self-Explosion Defect Detection Method for Insulators Based on Lightweight GhostNet-YOLOV4 Model Deployed Onboard UAV
Abtract:In order to meet the application requirements on unmanned aerial vehicle (UAV) for daily inspection of insulators in overhead grid transmission lines, a lightweight GhostNet-YOLOV4 model is proposed to identify the insulators objects and detect their self-explosion defect meanwhile. In the proposed model, the lightweight GhostNet with full convolutional attention module (C-SE) model is embedded into the YOLOV4 as backbone feature extraction network, and the ordinary convolution and Mish activation function of enhanced feature extraction network in GhostNet-YOLOV4 are replaced by the depth separable convolution and ReLU6 activation function respectively. Meanwhile, the combination methods of data augmentation, label smoothing, K-Means algorithm, adaptive cosine annealing learning rate, and Focal Loss function are adopted to optimize the model. Finally, the class activation mapping (CAM) is used to visually analyze for the identification process of the proposed model. The example analysis results show that the object recognition accuracy mAP of the proposed model is 99.50% for insulator objects and self-explosion defect, and the image processing speed FPS is 2.53 frames/s. The proposed model meets the requirements of lightweight, detect accuracy and speed for UAV application.

100、MobileCrack: Object classification in asphalt pavements using an adaptive lightweight deep learning
Abtract:One of the key steps in pavement maintenance is the fast and accurate identification of the distresses, defects, and pavement markings and ability to conduct the maintenance before the irreversible damages. Recently, convolution neural network (CNN) has emerged as a powerful tool to automatically identify the pavement cracks, where many of the CNN models take long computation time. To solve the problem, an adaptive lightweight model, named MobileCrack, is proposed in this study. MobileCrack realizes the fast computation using the following settings: (1) reduce input image size, where besides the original input images, images with a side length of  of that of the original square images are also input into the model by using the resize command; (2) group convolution is used; and (3) global average pooling is used because it normally has less parameters compared with the fully connected layer. MobileCrack will then compute the combinations of different resized input images and different neural network structures, to find the optimal reduced image size and neural network structure with satisfactory accuracy using reasonable computation time. To verify the applicability of MobileCrack, 10,000 input images with size  are trained for the classification task of crack, sealed crack, pavement marking, and pavement matrix. Based on the computation results of combinations of images with different sizes (, , , and ) and different stacking numbers of core modules  (3, 4, 5, and 6), the optimal model is determined as image size  and , where the test accuracy is 0.865 within reasonable computation time ( to test one image). This optimal model will be automatically used for further tests with image size  for a fast computation, which realizes the lightweight adaptive goal. Results also show that the test accuracy of MobileCrack is higher than that of the AlexNet and visual geometry group (VGG), and the parameters of MobileCrack are approximately  of that of the classic lightweight CNN model MobileNet, which saves the storage space. It is concluded that that the proposed adaptive lightweight CNN model, MobileCrack, can be used for the fast object classification on asphalt pavement crack images.

101、Road damage detection using super-resolution and semi-supervised learning with generative adversarial network
Abtract:Road maintenance technology is required to maintain favorable driving conditions and prevent accidents. In particular, a sensor technology is required for detecting road damage. In this study, we developed a new sensor technology that can detect road damage using a deep learning-based image processing algorithm. The proposed technology includes a super-resolution and semi-supervised learning method based on a generative adversarial network. The former improves the quality of the road image to make the damaged area clearly visible. The latter enhances the detection performance using 5327 road images and 1327 label images. These two methods were applied to four lightweight segmentation neural networks. For 400 road images, the average recognition performance was 81.540% and 79.228% in terms of the mean intersection over union and F1-score, respectively. Consequently, the proposed training method improves the road damage detection algorithm and can be used for efficient road management in the future.

102、YOLO-LRDD: A lightweight method for road damage detection based on improved YOLOv5s
Abtract:In computer vision, timely and accurate execution of object identification tasks is critical. However, present road damage detection approaches based on deep learning suffer from complex models and computationally time-consuming issues. To address these issues, we present a lightweight model for road damage identification by enhancing the YOLOv5s approach. The resulting algorithm, YOLO-LRDD, provides a good balance of detection precision and speed. First, we propose the novel backbone network Shuffle-ECANet by adding an ECA attention module into the lightweight model ShuffleNetV2. Second, to ensure reliable detection, we employ BiFPN rather than the original feature pyramid network since it improves the network's capacity to describe features. Moreover, in the model training phase, localization loss is modified to Focal-EIOU in order to get higher-quality anchor box. Lastly, we augment the well-known RDD2020 dataset with many samples of Chinese road scenes and compare YOLO-LRDD against several state-of-the-art object detection techniques. The smaller model of our YOLO-LRDD offers superior performance in terms of accuracy and efficiency, as determined by our experiments. Compared to YOLOv5s in particular, YOLO-LRDD improves single image recognition speed by 22.3% and reduces model size by 28.8% while maintaining comparable accuracy. In addition, it is easier to implant in mobile devices because its model is smaller and lighter than those of the other approaches.

103、GDCP-YOLO: Enhancing Steel Surface Defect Detection Using Lightweight Machine Learning Approach
Abtract:Surface imperfections in steel materials potentially degrade quality and performance, thereby escalating the risk of accidents in engineering applications. Manual inspection, while traditional, is laborious and lacks consistency. However, recent advancements in machine learning and computer vision have paved the way for automated steel defect detection, yielding superior accuracy and efficiency. This paper introduces an innovative deep learning model, GDCP-YOLO, devised for multi-category steel defect detection. We enhance the reference YOLOv8n architecture by incorporating adaptive receptive fields via the DCNV2 module and channel attention in C2f. These integrations aim to concentrate on valuable features and minimize parameters. We incorporate the efficient Faster Block and employ Ghost convolutions to generate more feature maps with reduced computation. These modifications streamline feature extraction, curtail redundant information processing, and boost detection accuracy and speed. Comparative trials on the NEU-DET dataset underscore the state-of-the-art performance of GDCP-YOLO. Ablation studies and generalization experiments reveal consistent performance across a variety of defect types. The optimized lightweight architecture facilitates real-time automated inspection without sacrificing accuracy, offering invaluable insights to further deep learning techniques for surface defect identification across manufacturing sectors.

104、Autonomous Identification of Bridge Concrete Cracks Using Unmanned Aircraft Images and Improved Lightweight Deep Convolutional Networks
Abtract:The identification of the development of structural defects is an important part of bridge structure damage diagnosis, and cracks are considered the most typical and highly dangerous structural disease. However, existing deep learning‐based methods are mostly aimed at the scene of concrete cracks, while they rarely focus on designing network architectures to improve the vision‐based model performance from the perspective of unmanned aircraft system (UAS) inspection, which leads to a lack of specificity. Because of this, this study proposes a novel lightweight deep convolutional neural network‐based crack pixel‐level segmentation network for UAS‐based inspection scenes. Firstly, the classical encoder‐decoder architecture UNET is utilized as the base model for bridge structural crack identification, and the hourglass‐shaped depthwise separable convolution is introduced to replace the traditional convolutional operation in the UNET model to reduce model parameters. Then, a kind of lightweight and efficient channel attention module is used to improve model feature fuzzy ability and segmentation accuracy. We conducted a series of experiments on bridge structural crack detection tasks by utilizing a long‐span bridge as the research item. The experimental results show that the constructed method achieves an effective balance between reasoning accuracy and efficiency with the value of 97.62% precision, 97.23% recall, 97.42% accuracy, and 93.25% IOU on the bridge concrete crack datasets, which are significantly higher than those of other state‐of‐the‐art baseline methods. It can be inferred that the application of hourglass‐shaped depth‐separable volumes can actively reduce basic model parameters. Moreover, the lightweight and efficient attention modules can achieve local cross‐channel interaction without dimensionality reduction and improve the network segmentation performance.

105、Lightweight Detection Methods for Insulator Self-Explosion Defects
Abtract:The accurate and efficient detection of defective insulators is an essential prerequisite for ensuring the safety of the power grid in the new generation of intelligent electrical system inspections. Currently, traditional object detection algorithms for detecting defective insulators in images face issues such as excessive parameter size, low accuracy, and slow detection speed. To address the aforementioned issues, this article proposes an insulator defect detection model based on the lightweight Faster R-CNN (Faster Region-based Convolutional Network) model (Faster R-CNN-tiny). First, the Faster R-CNN model’s backbone network is turned into a lightweight version of it by substituting EfficientNet for ResNet (Residual Network), greatly decreasing the model parameters while increasing its detection accuracy. The second step is to employ a feature pyramid to build feature maps with various resolutions for feature fusion, which enables the detection of objects at various scales. In addition, replacing ordinary convolutions in the network model with more efficient depth-wise separable convolutions increases detection speed while slightly reducing network detection accuracy. Transfer learning is introduced, and a training method involving freezing and unfreezing the model is employed to enhance the network’s ability to detect small target defects. The proposed model is validated using the insulator self-exploding defect dataset. The experimental results show that Faster R-CNN-tiny significantly outperforms the Faster R-CNN (ResNet) model in terms of mean average precision (mAP), frames per second (FPS), and number of parameters.

106、A Lightweight Insulator Defect Detection Model Based on Drone Images
Abtract:With the continuous development and construction of new power systems, using drones to inspect the condition of transmission line insulators has become an inevitable trend. To facilitate the deployment of drone hardware equipment, this paper proposes IDD-YOLO (Insulator Defect Detection-YOLO), a lightweight insulator defect detection model. Initially, the backbone network of IDD-YOLO employs GhostNet for feature extraction. However, due to the limited feature extraction capability of GhostNet, we designed a lightweight attention mechanism called LCSA (Lightweight Channel-Spatial Attention), which is combined with GhostNet to capture features more comprehensively. Secondly, the neck network of IDD-YOLO utilizes PANet for feature transformation and introduces GSConv and C3Ghost convolution modules to reduce redundant parameters and lighten the network. The head network employs the YOLO detection head, incorporating the EIOU loss function and Mish activation function to optimize the speed and accuracy of insulator defect detection. Finally, the model is optimized using TensorRT and deployed on the NVIDIA Jetson TX2 NX mobile platform to test the actual inference speed of the model. The experimental results demonstrate that the model exhibits outstanding performance on both the proprietary ID-2024 insulator defect dataset and the public SFID insulator dataset. After optimization with TensorRT, the actual inference speed of the IDD-YOLO model reached 20.83 frames per second (FPS), meeting the demands for accurate and real-time inspection of insulator defects by drones.

107、LW-YOLO: Lightweight Deep Learning Model for Fast and Precise Defect Detection in Printed Circuit Boards
Abtract:Printed circuit board (PCB) manufacturing processes are becoming increasingly complex, where even minor defects can impair product performance and yield rates. Precisely identifying PCB defects is critical but remains challenging. Traditional PCB defect detection methods, such as visual inspection and automated technologies, have limitations. While defects can be readily identified based on symmetry, the operational aspect proves to be quite challenging. Deep learning has shown promise in defect detection; however, current deep learning models for PCB defect detection still face issues like large model size, slow detection speed, and suboptimal accuracy. This paper proposes a lightweight YOLOv8 (You Only Look Once version 8)-based model called LW-YOLO (Lightweight You Only Look Once) to address these limitations. Specifically, LW-YOLO incorporates a bidirectional feature pyramid network for multiscale feature fusion, a Partial Convolution module to reduce redundant calculations, and a Minimum Point Distance Intersection over Union loss function to simplify optimization and improve accuracy. Based on the experimental data, LW-YOLO achieved an mAP0.5 of 96.4%, which is 2.2 percentage points higher than YOLOv8; the precision reached 97.1%, surpassing YOLOv8 by 1.7 percentage points; and at the same time, LW-YOLO achieved an FPS of 141.5. The proposed strategies effectively enhance efficiency and accuracy for deep-learning-based PCB defect detection.

108、Nondestructive Testing (NDT) for Damage Detection in Concrete Elements with Externally Bonded Fiber-Reinforced Polymer
Abtract:Fiber-reinforced polymer (FRP) composites offer a corrosion-resistant, lightweight, and durable alternative to traditional steel material in concrete structures. However, the lack of established inspection methods for assessing reinforced concrete elements with externally bonded FRP (EB-FRP) composites hinders industry-wide confidence in their adoption. This study addresses this gap by investigating non-destructive testing (NDT) techniques for detecting damage and defects in EB-FRP concrete elements. As such, this study first identified and categorized potential damage in EB-FRP concrete elements considering where and why they occur. The most promising NDT methods for detecting this damage were then analyzed. And lastly, experiments were carried out to assess the feasibility of the selected NDT methods for detecting these defects. The result of this study introduces infrared thermography (IR) as a proper method for identifying defects underneath the FRP system (wet lay-up). The IR was capable of highlighting defects as small as 625 mm2 (1 in.2) whether between layers (debonding) or between the substrate and FRP (delamination). It also indicates the inability of GPR to detect damage below the FRP laminates, while indicating the capability of PAU to detect concrete delamination and qualitatively identify bond damage in the FRP system. The outcome of this research can be used to provide guidance for choosing effective on-site NDT techniques, saving considerable time and cost for inspection. Importantly, this study also paves the way for further innovation in damage detection techniques addressing the current limitations.

109、YOLO-RRL: A Lightweight Algorithm for PCB Surface Defect Detection
Abtract:Printed circuit boards present several challenges to the detection of defects, including targets of insufficient size and distribution, a high level of background noise, and a variety of complex types. These factors contribute to the difficulties encountered by PCB defect detection networks in accurately identifying defects. This paper proposes a less-parametric model, YOLO-RRL, based on the improved YOLOv8 architecture. The YOLO-RRL model incorporates four key improvement modules: The following modules have been incorporated into the proposed model: Robust Feature Downsampling (RFD), Reparameterised Generalised FPN (RepGFPN), Dynamic Upsampler (DySample), and Lightweight Asymmetric Detection Head (LADH-Head). The results of multiple performance metrics evaluation demonstrate that YOLO-RRL enhances the mean accuracy (mAP) by 2.2 percentage points to 95.2%, increases the frame rate (FPS) by 12%, and significantly reduces the number of parameters and the computational complexity, thereby achieving a balance between performance and efficiency. Two datasets, NEU-DET and APSPC, were employed to evaluate the performance of YOLO-RRL. The results indicate that YOLO-RRL exhibits good adaptability. In comparison to existing mainstream inspection models, YOLO-RRL is also more advanced. The YOLO-RRL model is capable of significantly improving production quality and reducing production costs in practical applications while also extending the scope of the inspection system to a wide range of industrial applications.

110、EMB-YOLO: Dataset, method and benchmark for electric meter box defect detection
Abtract:The electric meter box is a terminal device with a large number in the power grid. It may cause electrical hazards and property loss if damaged. Inspection of electricity meter boxes still relies on manual inspection with low efficiency and low automation. But image-based automated inspection is also limited by equipment battery and insufficient computing power, which makes the inspection system in urgent need of efficient model. However, lightweight model may reduce model robustness and be susceptible to interference from complex backgrounds due to insufficient feature extraction. Meanwhile, there are no publicly available datasets for electric meter boxes at present. To address the above issues, we firstly constructed a dataset, named EMB-11. After that, we improved the YOLOv7-tiny to design a novel model for electric meter box defect detection, named EMB-YOLO. In EMB-YOLO, we proposed the Big Kernel ShuffleBlock which can increase the effective receptive field and reduce the model parameters. Additionally, we proposed ELAN-CBAM to enhance the robustness of the model and reduce the interference of background noise. Finally, we constructed RepBSB based on the idea of structural reparameterization to reduce the size of the trained model. Compared to YOLOv7-tiny, the size of EMB-YOLO is only 4.82 Mb, which is reduced by 20.3 %. The detection speed is 343 frames/s, which is increased by 14.3 %. Most importantly, mAP can reach 82.8 %, which is increased by 3.5 %, reaching the SOTA level.

111、A Lightweight and Efficient Multi-Type Defect Detection Method for Transmission Lines Based on DCP-YOLOv8
Abtract:Currently, the intelligent defect detection of massive grid transmission line inspection pictures using AI image recognition technology is an efficient and popular method. Usually, there are two technical routes for the construction of defect detection algorithm models: one is to use a lightweight network, which improves the efficiency, but it can generally only target a few types of defects and may reduce the detection accuracy; the other is to use a complex network model, which improves the accuracy, and can identify multiple types of defects at the same time, but it has a large computational volume and low efficiency. To maintain the model’s high detection accuracy as well as its lightweight structure, this paper proposes a lightweight and efficient multi type defect detection method for transmission lines based on DCP-YOLOv8. The method employs deformable convolution (C2f_DCNv3) to enhance the defect feature extraction capability, and designs a re-parameterized cross phase feature fusion structure (RCSP) to optimize and fuse high-level semantic features with low level spatial features, thus improving the capability of the model to recognize defects at different scales while significantly reducing the model parameters; additionally, it combines the dynamic detection head and deformable convolutional v3’s detection head (DCNv3-Dyhead) to enhance the feature expression capability and the utilization of contextual information to further improve the detection accuracy. Experimental results show that on a dataset containing 20 real transmission line defects, the method increases the average accuracy (mAP@0.5) to 72.2%, an increase of 4.3%, compared with the lightest baseline YOLOv8n model; the number of model parameters is only 2.8 M, a reduction of 9.15%, and the number of processed frames per second (FPS) reaches 103, which meets the real time detection demand. In the scenario of multi type defect detection, it effectively balances detection accuracy and performance with quantitative generalizability.

112、LoHi-WELD: A Novel Industrial Dataset for Weld Defect Detection and Classification, a Deep Learning Study, and Future Perspectives
Abtract:The automated inspection of weld beads is of great importance for many industrial processes. Failures may cause a loss of mechanical resistance of the weld bead and compromise the manufactured part. Several methods have been proposed in the literature to address this problem, and recently, methods based on deep learning have gained prominence in terms of performance and applicability. However, such methods require vast and reliable datasets for different real defects, which have yet to be available in recent literature. Hence, this paper presents LoHi-WELD, an original and public database to address the problem of weld defect detection and classification of four common types of defects — pores, deposits, discontinuities, and stains — with 3,022 real weld bead images manually annotated for visual inspection, composed by low and high-resolution images, acquired from a Metal Active Gas robotic welding industrial process. We also explore variations of a baseline deep architecture for the proposed dataset based on a YOLOv7 network and discuss several case analyses. We show that a lightweight architecture, ideal for industrial edge devices, can achieve up to 0.69 of mean average precision (mAP) considering a fine-grained defect classification and 0.77 mAP for a coarse classification. Open challenges are also presented, promoting future research and enabling robust solutions for industrial scenarios. The proposed dataset, architecture, and trained models are publicly available on https://github.com/SylvioBlock/LoHi-Weld.

113、Lightweight Insulator and Defect Detection Method Based on Improved YOLOv8
Abtract:Insulator and defect detection is a critical technology for the automated inspection of transmission and distribution lines within smart grids. However, the development of a lightweight, real-time detection platform suitable for deployment on drones faces significant challenges. These include the high complexity of existing algorithms, limited availability of UAV images, and persistent issues with false positives and missed detections. To address this issue, this paper proposed a lightweight drone-based insulator defect detection method (LDIDD) that integrates data augmentation and attention mechanisms based on YOLOv8. Firstly, to address the limitations of the existing insulator dataset, data augmentation techniques are developed to enhance the diversity and quantity of samples in the dataset. Secondly, to address the issue of the network model’s complexity hindering its application on UAV equipment, depthwise separable convolution is incorporated for lightweight enhancement within the YOLOv8 algorithm framework. Thirdly, a convolutional block attention mechanism is integrated into the feature extraction module to enhance the detection of small insulator targets in aerial images. The experimental results show that the improved network reduces the computational volume by 46.6% and the mAP stably maintains at 98.3% compared to YOLOv8, which enables the implementation of a lightweight insulator defect network suitable for the UAV equipment side without affecting the detection performance.

114、Insulator Defect Detection Based on the CDDCR-YOLOv8 Algorithm
Abtract: Insulator defect detection is a critical aspect of grid inspection in reality, yet it faces intricate environmental challenges, such as slow detection speed and low accuracy. To address this issue, we propose a YOLOv8-based insulator defect detection algorithm named CDDCR-YOLOv8. This algorithm divides the input insulator images into multiple grid cells, with each grid cell responsible for predicting the presence and positional information of one or more targets. First, we introduce the Coordinate Attention (CA) mechanism module into the backbone network and replace the original C2f module with the enhanced C2f_DCN module. Second, improvements are made to the original upsampling and downsampling layers in the neck network, along with the introduction of the lightweight module RepGhost. Finally, we employ Wise-IoU (WIoU) to replace the original CIoU as the loss function for network regression. Experimental results demonstrate that the improved algorithm achieves an average precision mean (mAP @ 0.5) of 97.5% and 90.6% on the CPLID and IPLID data sets, respectively, with a frame per second (FPS) of 84, achieving comprehensive synchronous improvement. Compared to traditional algorithms, our algorithm exhibits significant performance enhancement.
ER

115、Local and Global Context-Enhanced Lightweight CenterNet for PCB Surface
   Defect Detection
Abtract:Printed circuit board (PCB) surface defect detection is an essential part of the PCB manufacturing process. Currently, advanced CCD or CMOS sensors can capture high-resolution PCB images. However, the existing computer vision approaches for PCB surface defect detection require high computing effort, leading to insufficient efficiency. To this end, this article proposes a local and global context-enhanced lightweight CenterNet (LGCL-CenterNet) to detect PCB surface defects in real time. Specifically, we propose a two-branch lightweight vision transformer module with local and global attention, named LGT, as a complement to extract high-dimension features and leverage context-aware local enhancement after the backbone network. In the local branch, we utilize coordinate attention to aggregate more powerful features of PCB defects with different shapes. In the global branch, Bi-Level Routing Attention with pooling is used to capture long-distance pixel interactions with limited computational cost. Furthermore, a Path Aggregation Network (PANet) feature fusion structure is incorporated to mitigate the loss of shallow features caused by the increase in model depth. Then, we design a lightweight prediction head by using depthwise separable convolutions, which further compresses the computational complexity and parameters while maintaining the detection capability of the model. In the experiment, the LGCL-CenterNet increased the mAP@0.5 by 2% and 1.4%, respectively, in comparison to CenterNet-ResNet18 and YOLOv8s. Meanwhile, our approach requires fewer model parameters (0.542M) than existing techniques. The results show that the proposed method improves both detection accuracy and inference speed and indicate that the LGCL-CenterNet has better real-time performance and robustness.

116、LESM-YOLO: An Improved Aircraft Ducts Defect Detection Model
Abtract: Aircraft ducts play an indispensable role in various systems of an aircraft. The regular inspection and maintenance of aircraft ducts are of great significance for preventing potential failures and ensuring the normal operation of the aircraft. Traditional manual inspection methods are costly and inefficient, especially under low-light conditions. To address these issues, we propose a new defect detection model called LESM-YOLO. In this study, we integrate a lighting enhancement module to improve the accuracy and recognition of the model under low-light conditions. Additionally, to reduce the model's parameter count, we employ space-to-depth convolution, making the model more lightweight and suitable for deployment on edge detection devices. Furthermore, we introduce Mixed Local Channel Attention (MLCA), which balances complexity and accuracy by combining local channel and spatial attention mechanisms, enhancing the overall performance of the model and improving the accuracy and robustness of defect detection. Finally, we compare the proposed model with other existing models to validate the effectiveness of LESM-YOLO. The test results show that our proposed model achieves an mAP of 96.3%, a 5.4% improvement over the original model, while maintaining a detection speed of 138.7, meeting real-time monitoring requirements. The model proposed in this paper provides valuable technical support for the detection of dark defects in aircraft ducts.

117、An Improved Lightweight Deep Learning Model and Implementation for Track
   Fastener Defect Detection with Unmanned Aerial Vehicles
Abtract:Track fastener defect detection is an essential component in ensuring railway safety operations. Traditional manual inspection methods no longer meet the requirements of modern railways. The use of deep learning image processing techniques for classifying and recognizing abnormal fasteners is faster, more accurate, and more intelligent. With the widespread use of unmanned aerial vehicles (UAVs), conducting railway inspections using lightweight, low-power devices carried by UAVs has become a future trend. In this paper, we address the characteristics of track fastener detection tasks by improving the YOLOv4-tiny object detection model. We improved the model to output single-scale features and used the K-means++ algorithm to cluster the dataset, obtaining anchor boxes that were better suited to the dataset. Finally, we developed the FPGA platform and deployed the transformed model on this platform. The experimental results demonstrated that the improved model achieved an mAP of 95.1% and a speed of 295.9 FPS on the FPGA, surpassing the performance of existing object detection models. Moreover, the lightweight and low-powered FPGA platform meets the requirements for UAV deployment.

118、A Novel YOLOv10-Based Algorithm for Accurate Steel Surface Defect
   Detection
Abtract: To address challenges like manual processes, complicated detection methods, high false alarm rates, and frequent errors in identifying defects on steel surfaces, this research presents an innovative detection system, YOLOv10n-SFDC. The study focuses on the complex dependencies between parameters used for defect detection, particularly the interplay between feature extraction, fusion, and bounding box regression, which often leads to inefficiencies in traditional methods. YOLOv10n-SFDC incorporates advanced elements such as the DualConv module, SlimFusionCSP module, and Shape-IoU loss function, improving feature extraction, fusion, and bounding box regression to enhance accuracy. Testing on the NEU-DET dataset shows that YOLOv10n-SFDC achieves a mean average precision (mAP) of 85.5% at an Intersection over Union (IoU) threshold of 0.5, a 6.3 percentage point improvement over the baseline YOLOv10. The system uses only 2.67 million parameters, demonstrating efficiency. It excels in identifying complex defects like 'rolled in scale' and 'inclusion'. Compared to SSD and Fast R-CNN, YOLOv10n-SFDC outperforms these models in accuracy while maintaining a lightweight architecture. This system excels in automated inspection for industrial environments, offering rapid, precise defect detection. YOLOv10n-SFDC emerges as a reliable solution for the continuous monitoring and quality assurance of steel surfaces, improving the reliability and efficiency of steel manufacturing processes.

