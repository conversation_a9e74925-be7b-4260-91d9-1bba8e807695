### 2.1. 用于裂缝检测的机器人平台

机器人平台的选择是设计检测系统的第一个关键决策，因为它直接决定了系统在目标环境中导航和有效定位传感器的能力。多年来，研究人员开发和改造了各种各样的平台，每种平台都有其独特的优势和固有的局限性。这些平台可以根据其主要操作领域大致分为：地面和爬壁平台、空中平台和水上平台。然而，机械设计、移动性限制和环境适应性的异质性，要求我们对每个平台的操作领域和权衡进行情境化理解。以下小节将详细探讨每个类别。

#### 2.1.1. 地面移动与结构附着机器人

地面机器人系统，包括移动平台和结构附着平台，构成了高精度、近距离结构检测的基石。它们的主要优势在于能够承载大量有效载荷，并为各种无损检测传感器提供稳定的基础，从而实现其他平台通常无法实现的详细数据采集。本节深入探讨了各种类型的地面和爬壁机器人，重点介绍了它们的操作能力和特定的应用领域。

##### 2.1.1.1. 地面检测平台（轮式、履带式和足式）

轮式和履带式无人地面车辆（UGV）是检查大型水平表面（如桥面、路面和隧道地面）最成熟的平台。其坚固的设计提供了高有效载荷能力和长续航时间，使其成为携带全面传感器套件的理想选择。一个著名的例子是机器人辅助桥梁检测工具（RABIT），这是一个全向UGV，集成了探地雷达（GPR）、冲击回波传感器和摄像头，用于自主、多模态的桥面评估[18, 23, 31, 19]。最近的系统利用SLAM的进步来融合LiDAR和摄像头数据，使UGV能够生成高保真度的桥梁结构3D损伤地图，并进行精确的缺陷定位[15]。虽然在平整表面上非常有效，但这些平台的主要局限性在于无法跨越重大障碍物或穿越非连续和垂直地形。为了弥补这一移动性差距，腿式机器人，特别是四足机器人，已成为一个有前途的替代方案。像ANYmal这样的平台已经展示了在工业检测任务中穿越不平坦地形和爬楼梯的能力[6]，先进的状态估计技术使其即使在具有挑战性的表面上也能实现稳健的移动[32]。然而，增加的机械和控制复杂性仍然是其广泛应用的一个重要障碍。

##### 2.1.1.2. 爬壁与磁吸平台

为了解决标准UGV无法进入的垂直维度，一类专门的结构附着机器人应运而生。这些爬壁机器人被设计用于穿越垂直或倒置的表面，使其在检查钢桥构件、高层建筑外墙和工业储罐等结构时不可或缺。吸附机制根据表面材料量身定制。对于铁磁结构，通常采用配备永磁体和高摩擦轮或履带的磁性爬行器，展示了在复杂钢结构几何形状上导航并克服接头和焊缝等障碍物的能力[16, 17]。对于非铁磁表面，通常通过负压（真空吸附）或气动推力实现吸附。基于推进器的爬壁机器人通常是混合式空中爬壁系统，使用螺旋桨产生持续的下压力，将机器人压在墙上[33, 34, 35, 36]。这些平台为在垂直表面上进行详细的、近距离的视觉或基于无损检测的检查提供了独特的能力。然而，它们的实际应用面临着相当大的挑战，包括移动速度慢、能耗高以及在肮脏、潮湿或有纹理的表面上吸附不可靠，这需要复杂的控制和机械设计[37, 30]。

##### 2.1.1.3. UGV与机械臂协同系统

为了增强地面平台的多功能性并扩展其感官范围，许多现代系统将UGV与机器人操纵臂集成在一起。这种协同配置允许移动基座保持在稳定、可及的表面上，而机械臂将传感器有效载荷定位到复杂或难以到达的位置。这对于需要精确传感器放置或物理接触的任务尤其重要，例如定位超声波换能器以测量裂缝深度[26]或瞄准相机检查桥梁大梁的下侧。例如，ROBO-SPECT项目使用一台起重机和一个安装在移动车辆上的高精度机械臂，用于详细的隧道缺陷评估[26]。这些系统中的主要挑战在于协调高自由度机械臂与移动基座的运动，特别是要减轻机械臂运动对基座的扰动，以确保末端执行器的稳定性和精度。该领域的研究探索了先进的控制技术，例如使用广义雅可比矩阵来最小化基座运动和反作用力矩，从而提高操纵任务的准确性[38]。

#### 2.1.2. 空中机器人平台

虽然地面系统在近距离、高保真度检测方面表现出色，但它们通常由空中平台补充，以实现快速、大面积的覆盖。无人机（UAVs），特别是多旋翼无人机，在现代检测工作流程中扮演着至关重要的辅助角色。它们为进入高处和难以到达的位置提供了无与伦比的途径，例如桥梁上层结构、塔架、风力涡轮机叶片和冷却塔[12, 8, 21, 39]。它们的主要优势是能够快速有效地进行初步的、大规模的视觉勘测，识别出可能需要地面或爬壁机器人进行更详细后续检查的潜在关注区域。然而，UAV用于详细裂缝检测的效用常常受到风阵引起的稳定性问题[40]、有限的飞行续航时间以及在保持与检测表面恒定、最佳的对峙距离方面的固有困难的限制，这会影响图像质量和测量精度[41]。因此，许多研究致力于开发稳健的飞行控制器和自主路径规划算法，以提高UAV在近距离检测任务中的可靠性[29, 42]。

#### 2.1.3. 水面与密闭空间平台

除了陆地和空中领域，对于浸没在水中或位于密闭空间的基础设施，需要专门的机器人。对于水下资产，如桥墩、坝面和海上结构，使用无人水面艇（USV）和无人水下航行器（UUV）。这些平台使用声纳和专用相机在对人类潜水员危险且无法进入的环境中进行检查[21, 31, 34]。对于城市公用事业网络，管道内检测机器人对于检测下水道和水管中的裂缝和堵塞至关重要[2, 43]。这些机器人必须被设计成能够承受极端的环境条件，同时克服与有限通信、在特征稀疏的环境中导航以及没有GPS相关的基本挑战。

#### 2.1.4. 通用平台设计与智能导航能力

无论其具体操作领域如何，现代检测机器人的有效性越来越不仅取决于其机械设计，还取决于其自主水平。实现自主操作取决于一个稳健的智能导航系统，这是所有平台类型的共同要求。一个关键的使能技术是SLAM，它允许机器人在建立未知环境地图的同时，跟踪自己在其中的位置。这在GPS信号被拒绝的区域，如桥下或隧道内，至关重要[14, 2]。现代SLAM系统通常融合来自多个传感器（通常是LiDAR、摄像头和惯性测量单元（IMU））的数据，以实现稳健和准确的状态估计[27, 28, 44, 45]。

基于SLAM生成的地图或预先存在的建筑信息模型（BIM），自主机器人必须规划其路径。**覆盖路径规划（CPP）**算法对于生成最佳轨迹至关重要，这些轨迹确保感兴趣的整个表面得到有效和无碰撞的检查[29, 30]。这些算法旨在最小化检查时间和能耗，同时保证传感器为高质量数据采集保持合适的姿态[42, 39]。这些导航能力的成功集成将一个遥控机器转变为一个智能代理。然而，这种自主性在很大程度上依赖于感知系统提供的感官数据的质量和丰富性，这构成了通往后续章节讨论主题的关键桥梁。

### 2.2. 多模态传感器系统

虽然机器人平台提供了对结构的物理访问，但是机载的多传感器套件才能实现感知和数据采集。有效的检测取决于一套经过精心挑选和集成的传感器，这些传感器不仅能捕捉可见的表面缺陷，还能捕捉亚表面缺陷或材料异常以及结构的几何特性。主流趋势明显是从单传感器系统向多模态方法转变，即融合来自不同传感器的数据，以创建对结构健康状况更全面、更可靠的理解。这种协同方法使得一种传感器的优势可以弥补另一种传感器的局限性。本节回顾了关键的传感器技术，并讨论了其集成的关键实践。

#### 2.2.1. 视觉与光学传感器

视觉和光学传感器是非接触式机器人检测的基石，为裂缝检测（`检测任务`）和3D环境重建（`建图任务`）提供丰富的数据。

*   **高分辨率RGB相机：** 这是裂缝检测的主要传感器。相机在机器人上的物理安装至关重要；不当的倾斜角度会引入透视畸变，而来自平台的机械振动会降低图像清晰度。先进的系统专注于优化整个数据采集流程。例如，Prasanna等人[5]开发了STRUM分类器，该分类器处理来自机器人桥梁扫描系统的图像，实现了95%的裂缝检测准确率。然而，在动态条件下，由于`光照变化、运动伪影和天气引起的噪声`等环境因素，图像质量仍然是一个挑战。为了解决运动模糊问题，Kim等人[46]提出了一种方法，通过分析混凝土的斑驳纹理来估计模糊参数，使用倒谱分析来锐化移动相机拍摄的图像。为了应对光照问题，Lu等人[47]提出了VIVID++数据集，其中包含对齐的RGB、热成像和事件相机数据，促进了更稳健的视觉算法的开发。

*   **3D几何传感器（LiDAR、RGB-D、结构光）：** 这些传感器对于捕捉`建图`和`避障`任务的几何背景至关重要。LiDAR在构建精确的数字孪生中发挥着重要作用。Jung等人[13]在无人机上使用倾斜的3D LiDAR，通过HG-SLAM进行自主桥梁建图。Ge等人[15]展示了一款地面机器人，使用增强的KISS-ICP算法处理LiDAR数据，以实现高效的3D损伤建图。为了获得更高分辨率，使用了结构光。Lee等人[48]使用结构光相机和PointNet++模型来识别复合材料中的平面外缺陷，交并比（IoU）超过72%。

*   **热（红外）相机：** 超越可见光谱，热像仪可以揭示亚表面缺陷。Tang等人[49]展示了一个显著的应用，他们开发了一个使用脉冲热成像技术的机器人检测平台。他们的工作引入了一种新颖的算法，用于精细对齐和融合多张热成像图像，从而能够准确检测具有复杂几何形状部件的亚表面缺陷。

#### 2.2.2. 非视觉传感器集成

全面的评估需要探测表面以下（`检测任务`）并确保稳健的导航（`定位任务`），这些任务通过集成非视觉传感器来完成。

*   **导航与定位传感器：** 惯性测量单元（IMU）是状态估计的基础。其重要性在紧耦合SLAM框架（如LIO-SAM [27]和FAST-LIO [28]）中表现得最为明显，这些框架融合IMU和LiDAR数据以实现稳健的轨迹估计，尤其是在快速运动或特征稀疏的环境中。这些方法对于在GNSS信号不可用时保持准确定位至关重要[13, 44]。

*   **无损评估（NDE）传感器：** 为了量化损伤，机器人携带NDE传感器，其选择在很大程度上取决于材料。对于**混凝土结构**，像RABIT [18]这样的平台携带**探地雷达（GPR）**来绘制钢筋图和检测亚表面分层。对于**钢结构**，像S.T.Nguyen等人[17]的机器人集成了**涡流传感器**来发现疲劳裂纹。对于**复合材料结构**，Mineo等人[50]使用带有**声学传感器**和CNN模型的机器人，以96.8%的准确率对界面空隙进行分类。许多这些接触式方法部署在UGV-机械臂协同系统上，由机械臂高精度地定位传感器[26]。


