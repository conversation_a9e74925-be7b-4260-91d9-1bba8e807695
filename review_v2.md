A Review of Robotic Crack Detection: A Systems-Level Perspective from Platforms to Deployed Algorithms

Abstract:The safety and longevity of civil infrastructure are critically dependent on effective structural health monitoring (SHM), where cracks serve as primary indicators of degradation. While robotic systems have emerged as a transformative alternative to hazardous manual inspections, a significant gap persists between isolated algorithmic advancements and their robust integration into field-deployable robotic platforms. This paper presents a comprehensive, systems-level review of robotic crack detection, charting the full technological pipeline from hardware to deployed intelligence. We first categorize and analyze the diverse robotic platforms (e.g., ground, aerial, climbing) and multi-modal sensor suites that form the physical foundation of inspection systems. The core of this review then delves into the evolution of perception algorithms, tracing the trajectory from traditional image processing to state-of-the-art deep learning models, with a specific focus on architectural innovations and lightweight designs essential for on-device inference. Critically, we bridge theory with practice by examining the strategies for algorithm deployment, the architectural paradigms for perception-planning-execution integration, and the lessons from real-world case studies. The primary contribution of this work is its holistic, integration-oriented synthesis, which contrasts with prior reviews by emphasizing the synergistic co-design of algorithms and hardware. Finally, by dissecting persistent challenges and identifying future research directions (such as foundation models, data-centric AI, and multi-agent systems), this review provides a strategic roadmap for developing the next generation of robust, efficient, and trustworthy robotic inspection systems.

Keywords:Robotic Inspection, Crack Detection, Structural Health Monitoring (SHM), Unmanned Aerial Vehicle (UAV), Deep Learning, Systems Integration, Sensor Fusion.

1. Introduction

1.1. Research Background and Significance

Civil infrastructure, including bridges, tunnels, dams, and pipelines, constitutes the bedrock of modern society [1, 2]. The structural integrity of these assets is, however, continuously compromised by aging, environmental stressors, and operational loads, often manifesting as surface cracks. These fissures, if unmonitored, can propagate and lead to severe structural failures, incurring catastrophic economic and human costs. Consequently, structural health monitoring (SHM), particularly the timely and accurate detection of cracks, is an indispensable component of any effective infrastructure management and public safety strategy [3].

For decades, the standard practice for inspection has relied on manual visual assessment, a method fraught with limitations [1]. It is subjective, with outcomes dependent on inspector experience [4, 5]; it is labor-intensive and costly, especially for large-scale structures; and, most critically, it exposes personnel to significant safety hazards when inspecting hard-to-reach locations like bridge cables or wind turbine blades [6,7,8]. These profound shortcomings have catalyzed a paradigm shift towards automated inspection systems, leveraging advancements in robotics and artificial intelligence [9].

The last decade has seen a surge in research demonstrating the potential of such systems. Sophisticated deep learning models can now detect cracks with high accuracy [10, 11], while a diverse fleet of robotic platforms, including unmanned aerial vehicles (UAVs) [12, 13], ground vehicles (UGVs) [14, 15], and specialized climbing robots [16, 17], provide unprecedented access to complex structural environments. However, despite significant progress in these individual components, a critical challenge has emerged: the gap between developing high-performance perception algorithms in a laboratory setting and deploying them as part of a reliable, autonomous robotic system in the field. The successful transition from academic prototypes to robust, field-deployable solutions hinges on a holistic, systems-level approach that is currently underexplored in the literature.

1.2. Core Terminology and Problem Definition

The central focus of this review is **Robotic Crack Detection**, a multidisciplinary field at the intersection of robotics, computer vision, and civil engineering. The concept (formalized in early systems that integrated mobile robots with Non-Destructive Evaluation (NDE) sensors [18, 19]) involves utilizing an autonomous or semi-autonomous mobile robot to navigate a target structure, acquire sensory data (primarily visual images), process this data to identify and characterize cracks, and, in advanced systems, map their locations within a global coordinate frame of the structure.

Key terminologies and constituent sub-problems are defined as follows:

*   **Robotic Platform:** The mobile robotic system used for data acquisition [1, 20]. This encompasses a wide range of systems, from **Unmanned Aerial Vehicles (UAVs)**, which provide rapid, non-contact access to elevated and remote areas [21], to **Unmanned Ground Vehicles (UGVs)** and **Climbing Robots**, which are suitable for surfaces like bridge decks [22] or steel structures [17]. The platform's effectiveness is defined by its mobility, stability, and payload capacity.
*   **Sensor System:** The suite of sensors integrated onto the robotic platform [23, 24]. While **high-resolution RGB cameras** are the primary sensor for visual crack detection, systems often incorporate other modalities. **Light Detection and Ranging (LiDAR)** and **RGB-D cameras** provide 3D information for localization and mapping [13, 15]; **thermal cameras** can detect subsurface defects or moisture [25]; and **ultrasonic sensors** can quantify crack depth [26]. The integration and fusion of data from these sensors are critical for comprehensive assessment.
*   **Localization and Mapping:** For a robotic system to operate autonomously and for defect locations to be meaningful, the robot must be able to determine its own position and orientation relative to the structure and create a map of its environment. This is often addressed through **Simultaneous Localization and Mapping (SLAM)** techniques [27, 28], which are especially crucial in GPS-denied environments such as under bridges or inside tunnels [2, 13].
*   **Crack Detection Algorithm:** This refers to the computational methods used to process the collected sensor data and identify cracks [4, 5]. These algorithms have evolved from **traditional image processing** methods (e.g., edge detection, thresholding) to **machine learning** and, more recently, **deep learning-based approaches** [15]. Modern algorithms aim not only for detection but also for semantic segmentation (pixel-level localization) and quantification (measuring length, width, and orientation).
*   **Path Planning:** This sub-problem involves determining an optimal trajectory for the robot to ensure complete and efficient coverage of the structure's surfaces of interest [29, 30]. The path must be navigable, collision-free, and designed to maintain an appropriate distance and viewing angle for high-quality data acquisition.

Solving the robotic crack detection problem therefore requires a holistic, systems-level approach. It is not sufficient to simply develop an accurate crack detection algorithm; this algorithm must be deployable on a mobile platform that can autonomously and reliably execute an inspection mission in a complex, real-world environment.

### 1.3. Contribution and Structure of This Paper

While numerous reviews have addressed specific facets of automated inspection, they often examine either the perception algorithms [4] or the NDE sensing technologies [1] in isolation. This component-centric view overlooks the critical, interdependent challenges that arise at the intersection of hardware, software, and real-world operational constraints. This paper aims to bridge that gap by providing a comprehensive, systems-level synthesis of the entire robotic crack detection pipeline, with a clear focus on the pathway to practical deployment. The main contributions of this review are fourfold:

1.  **A Systems-Level Synthesis:** We offer a holistic analysis that connects the capabilities and limitations of robotic platforms and sensors (the "body") with the evolution and performance of perception algorithms (the "brain"), providing a unified framework that is absent in prior literature.
2.  **A Deployment-Oriented Perspective:** We explicitly address the practical challenges of deploying complex algorithms on resource-constrained robotic hardware and integrating perception with autonomous navigation and control, highlighting strategies that are essential for real-world application. This distinguishes our review from works that remain purely algorithm-centric.
3.  **A Structured Methodological Review:** We systematically trace the development of detection algorithms from traditional image processing to state-of-the-art deep learning architectures, including lightweight models and Transformers, providing a clear map of the technological trajectory.
4.  **A Strategic Forward-Looking Analysis:** We distill the most persistent challenges facing the field and map them to high-impact future research directions, offering a strategic roadmap intended to guide researchers and practitioners in developing the next generation of robust and intelligent inspection systems.

This review is organized as follows: Section 2 details the various robotic platforms and sensor systems. Section 3 presents a thorough analysis of the development of perception algorithms. Section 4 discusses the practical aspects of algorithm deployment and system integration. Section 5 outlines the current challenges and future research trends. Finally, Section 6 concludes the paper with a summary of key findings and a final perspective on the field's trajectory.

---

## 2. Robotic Platforms and Sensor Systems

The successful implementation of an automated crack detection strategy is fundamentally reliant on the capabilities of the underlying hardware. A robotic inspection system is a complex integration of a mobile platform, which provides the necessary mobility and access, and a suite of sensors, which perform the data acquisition. The selection and design of these components are not arbitrary; they are dictated by the specific characteristics of the infrastructure, the environmental conditions, and the desired inspection outcomes. This section provides a detailed examination of the diverse robotic platforms and multi-modal sensor systems that form the physical basis of modern structural inspection. We first categorize and analyze the various mobile platforms before delving into the sensor technologies they employ.

### 2.1. Robotic Platforms for Crack Detection

The choice of a robotic platform is the first critical decision in designing an inspection system, as it directly determines the system's ability to navigate the target environment and position sensors effectively. Over the years, researchers have developed and adapted a wide array of platforms, each with distinct advantages and inherent limitations. These platforms can be broadly categorized based on their primary domain of operation: ground and climbing platforms, aerial platforms, and aquatic platforms. However, the heterogeneity in mechanical design, mobility constraints, and environmental adaptability necessitates a contextual understanding of each platform's operational domain and trade-offs. The following subsections explore each category in detail.

#### 2.1.1. Ground Mobile and Structure-Adhering Robots

Ground-based robotic systems, encompassing both mobile and structure-adhering platforms, form the cornerstone of high-precision, close-range structural inspection. Their principal advantage lies in the ability to carry substantial payloads and provide a stable base for a wide array of NDE sensors, enabling detailed data acquisition that is often infeasible for other platforms. This section delves into the various types of ground and climbing robots, highlighting their operational capabilities and specific application niches.

##### *******. Ground Inspection Platforms (Wheeled, Tracked, and Legged)

Wheeled and tracked Unmanned Ground Vehicles (UGVs) are the most established platforms for inspecting large, horizontal surfaces such as bridge decks, pavements, and tunnel floors. Their robust design affords high payload capacity and long endurance, making them ideal for carrying comprehensive sensor suites. A notable example is the Robotics Assisted Bridge Inspection Tool (RABIT), a holonomic UGV integrating Ground Penetrating Radar (GPR), impact-echo sensors, and cameras for autonomous, multi-modal bridge deck assessment [18, 23, 31, 19]. More recent systems leverage advances in SLAM to fuse LiDAR and camera data, enabling UGVs to generate high-fidelity 3D damage maps of bridge structures with precise defect localization [15]. While highly effective on prepared surfaces, the primary limitation of these platforms is their inability to negotiate significant obstacles or traverse non-contiguous and vertical terrains. To address this mobility gap, legged robots, particularly quadrupeds, have emerged as a promising alternative. Platforms like ANYmal have demonstrated the capacity to traverse uneven terrain and climb stairs during industrial inspection tasks [6], with advanced state estimation techniques enabling robust locomotion even on challenging surfaces [32]. However, the increased mechanical and control complexity remains a significant barrier to their widespread adoption.

##### *******. Wall-Climbing and Magnetic Platforms

To address the vertical dimension inaccessible to standard UGVs, a specialized class of structure-adhering robots has been developed. These climbing robots are engineered to traverse vertical or inverted surfaces, making them indispensable for inspecting structures like steel bridge components, high-rise facades, and industrial storage tanks. Adhesion is realized through diverse mechanisms tailored to the surface material. For ferromagnetic structures, magnetic crawlers equipped with permanent magnets and high-friction wheels or tracks are commonly employed, demonstrating the ability to navigate complex steel geometries and overcome obstacles like joints and welds [16, 17]. For non-ferrous surfaces, adhesion is often achieved using negative pressure (vacuum suction) or aerodynamic thrust. Thruster-based climbers, which are often hybrid aerial-climbing systems, use propellers to generate a persistent downforce, pressing the robot against the wall [33, 34, 35, 36]. These platforms offer unique capabilities for performing detailed, close-range visual or NDE-based inspections on vertical surfaces. Nevertheless, their practical application faces considerable challenges, including slow locomotion speeds, high energy consumption, and unreliable adhesion on dirty, wet, or textured surfaces, which necessitates sophisticated control and mechanical design [37, 30].

##### *******. UGV and Robotic Arm Collaborative Systems

To enhance the versatility of ground platforms and extend their sensory reach, many modern systems integrate a UGV with a robotic manipulator. This collaborative configuration allows the mobile base to remain on a stable, accessible surface while the arm positions a sensor payload into complex or hard-to-reach locations. This is particularly crucial for tasks requiring precise sensor placement or physical contact, such as positioning an ultrasonic transducer to measure crack depth [26] or aiming a camera to inspect the underside of a bridge girder. The ROBO-SPECT project, for example, utilizes a crane and a high-precision robotic arm on a mobile vehicle for detailed tunnel defect assessment [26]. The primary challenge in these systems lies in coordinating the motion of the high-degree-of-freedom manipulator with the mobile base, particularly in mitigating the manipulator's motion-induced disturbances on the base to ensure end-effector stability and precision. Research in this area explores advanced control techniques, such as using the generalized Jacobian, to minimize base motion and reaction torques, thereby improving the accuracy of manipulation tasks [38].

#### 2.1.2. Aerial Robotic Platforms

While ground-based systems excel at close-range, high-fidelity inspection, they are often complemented by aerial platforms for rapid, large-area coverage. Unmanned Aerial Vehicles (UAVs), especially multi-rotor drones, serve a crucial auxiliary role in modern inspection workflows. They provide unparalleled access to elevated and difficult-to-reach locations such as bridge superstructures, pylons, wind turbine blades, and cooling towers [12, 8, 21, 39]. Their main advantage is the ability to conduct a preliminary, large-scale visual survey quickly and efficiently, identifying potential areas of concern that may warrant a more detailed follow-up inspection by a ground or climbing robot. However, the utility of UAVs for detailed crack detection is often constrained by stability issues due to wind gusts [40], limited flight endurance, and the inherent difficulty of maintaining a constant, optimal standoff distance from the inspection surface, which can affect image quality and measurement accuracy [41]. Consequently, much research is dedicated to developing robust flight controllers and autonomous path-planning algorithms to improve the reliability of UAVs in close-proximity inspection tasks [29, 42].

#### 2.1.3. Surface Water and Confined Space Platforms

Beyond terrestrial and aerial domains, specialized robots are required for infrastructure submerged in water or located in confined spaces. For underwater assets like bridge piers, dam faces, and offshore structures, Unmanned Surface Vehicles (USVs) and Unmanned Underwater Vehicles (UUVs) are employed. These platforms use sonar and specialized cameras to perform inspections in environments that are dangerous and inaccessible to human divers [21, 31, 34]. For urban utility networks, in-pipe inspection robots are critical for detecting cracks and blockages in sewer and water pipelines [2, 43]. These robots must be designed to withstand extreme environmental conditions while overcoming fundamental challenges related to limited communication, navigation in feature-sparse environments, and the absence of GPS.

#### 2.1.4. Common Platform Design and Intelligent Navigation Capabilities

Regardless of their specific operational domain, the effectiveness of modern inspection robots is increasingly defined not just by their mechanical design but by their level of autonomy. Achieving autonomous operation hinges on a robust intelligent navigation system, which is a common requirement across all platform types. A key enabling technology is SLAM, which allows a robot to build a map of an unknown environment while simultaneously tracking its own position within it. This is paramount in GPS-denied areas such as under bridges or inside tunnels [14, 2]. Modern SLAM systems often fuse data from multiple sensors (typically LiDAR, cameras, and Inertial Measurement Units (IMU)) to achieve robust and accurate state estimation [27, 28, 44, 45].

Based on the map generated by SLAM or a pre-existing Building Information Model (BIM), an autonomous robot must then plan its path. **Coverage Path Planning (CPP)** algorithms are essential for generating optimal trajectories that ensure the entire surface of interest is inspected efficiently and without collision [29, 30]. These algorithms aim to minimize inspection time and energy consumption while guaranteeing that the sensor maintains a suitable pose for high-quality data acquisition [42, 39]. The successful integration of these navigation capabilities transforms a teleoperated machine into an intelligent agent. This autonomy, however, is critically dependent on the quality and richness of the sensory data provided by the perception system, which forms a crucial bridge to the topics discussed in the subsequent sections.

### 2.2. Multi-modal Sensor Systems

While the robotic platform provides physical access to the structure, it is the onboard multi-sensor suite that enables perception and data acquisition. An effective inspection is contingent upon a carefully selected and integrated set of sensors capable of capturing not only visible surface defects but also subsurface anomalies and the structure's geometric properties. The prevailing trend is a clear shift from single-sensor systems to synergistic multi-sensor approaches, where data from disparate sensors are fused to create a more comprehensive and reliable understanding of the structure's health. This allows the strengths of one sensor type to compensate for the limitations of another [h1_7, h1_42]. This section reviews the key sensor technologies and discusses the critical practices for their integration and application.

#### 2.2.1. Visual and Optical Sensors

Visual and optical sensors are the cornerstone of non-contact robotic inspection, providing rich data for crack detection (`Detection Task`) and 3D environment reconstruction (`Mapping Task`).

*   **High-Resolution RGB Cameras:** These are the primary sensors for crack detection. However, the quality of captured imagery is highly susceptible to environmental and operational challenges. Key issues include `illumination variability`, `motion blur` due to robot movement, and `perspective distortion` from suboptimal viewing angles [h1_40]. To combat these, researchers have developed targeted solutions. For instance, Kim et al. [46] proposed a method to estimate and correct motion blur by analyzing the texture of concrete surfaces. To handle lighting inconsistencies, advanced image enhancement algorithms based on principles like Retinex are often employed as a preprocessing step to normalize images before analysis [65]. Furthermore, intelligent data acquisition strategies that optimize the robot's path to maintain a consistent standoff distance and viewing angle are crucial for ensuring high-quality data collection across large structures [41].

*   **Event Cameras:** Emerging as a powerful alternative to traditional frame-based cameras, event cameras operate asynchronously, measuring per-pixel brightness changes rather than capturing full frames at a fixed rate. This bio-inspired sensing paradigm gives them extremely high dynamic range and low latency, making them highly robust to motion blur and challenging lighting conditions (e.g., rapid transitions from shadow to bright light). While still a nascent technology in SHM, their potential is significant, particularly for high-speed robotic inspections. Lu et al. [47] demonstrated their utility by including them in a multi-sensor dataset (VIVID++), facilitating the development of more robust vision algorithms.

*   **3D Geometric Sensors (LiDAR, Structured Light):** These sensors are vital for capturing geometric context for `Mapping` and `Collision Avoidance`. LiDAR, often a 3D laser scanner, is instrumental in building precise digital twins and enabling SLAM in GPS-denied environments [h1_2, h1_9, h1_13]. Jung et al. [13] used a tilted 3D LiDAR on a UAV for autonomous bridge mapping, while Ge et al. [15] showcased a ground robot using LiDAR for high-fidelity 3D damage mapping. For higher-resolution surface profiling, structured light sensors are used. Lee et al. [48] employed a structural-light camera and a PointNet++ model to identify out-of-plane defects in composites with high precision.

*   **Thermal (Infrared) Cameras:** Moving beyond the visible spectrum, thermal cameras reveal subsurface defects by detecting anomalies in surface temperature distribution. This is particularly effective when combined with an active heat source (active thermography). For instance, Rodríguez et al. developed a ground robot using a hot air blower and an IR camera to detect tile hollowness in buildings [h1_5, h1_6]. Similarly, Tang et al. [49] developed a robotic platform using pulsed thermography for inspecting complex-shaped components, highlighting the need for fine alignment of thermographic images for accurate flaw detection.

#### 2.2.2. Subsurface and Proprioceptive Sensors

A comprehensive assessment requires probing beneath the surface (`Detection Task`) and ensuring robust navigation (`Localization Task`), which is accomplished by integrating non-visual sensors.

*   **Proprioceptive and Navigation Sensors:** The Inertial Measurement Unit (IMU) is a fundamental proprioceptive sensor, measuring the robot's own motion (linear acceleration and angular velocity). Its data is crucial for state estimation, especially when fused with exteroceptive sensors like cameras and LiDAR in tightly-coupled SLAM frameworks. Visual-Inertial Odometry (VIO) and Lidar-Inertial Odometry (LIO) systems like LIO-SAM [27] and FAST-LIO [28] rely on this fusion to achieve robust and accurate trajectory estimation, which is critical for maintaining localization when GNSS signals are unavailable or unreliable [13, 44, h1_13].

*   **Non-Destructive Evaluation (NDE) Sensors:** To quantify damage, robots carry specialized NDE sensors. The choice depends heavily on the material and target defect type [h1_31].
    *   **Ground Penetrating Radar (GPR):** GPR is highly effective for inspecting concrete structures. It emits electromagnetic waves to map subsurface features, such as rebar, and detect anomalies like delamination and voids. Platforms like RABIT [18] have long used GPR arrays for comprehensive bridge deck assessment.
    *   **Acoustic and Ultrasonic Sensors:** These sensors use sound waves to evaluate material integrity. **Acoustic Emission (AE)** sensors are passive devices that "listen" for high-frequency stress waves released by active crack growth, providing early warnings of damage. In contrast, **Ultrasonic Testing (UT)** is an active method where a transducer emits ultrasonic pulses and analyzes the echoes to detect and size internal flaws like cracks and voids with high precision [26, h1_68].
    *   **Eddy Current Testing (ECT):** For conductive materials like steel, ECT is a primary NDE method. A probe generates an alternating magnetic field that induces eddy currents in the material. Surface or near-surface cracks disrupt these currents, and the resulting change in coil impedance is detected by the sensor. This makes it ideal for finding fatigue cracks in steel bridges or welds [17].
    *   **Fiber Optic Sensors (FOS):** A different class of sensor, FOS can be embedded into or mounted onto structures to provide distributed, long-term monitoring of strain and temperature. By measuring shifts in the light signals traveling through the fiber, they can detect the initiation and propagation of cracks over large areas with high sensitivity, offering a powerful tool for continuous structural health monitoring.

#### 2.2.3. Data Acquisition and Sensor Integration Practice

The presence of multiple sensors is not sufficient; their effective integration through meticulous layout, calibration, and synchronization is what enables reliable data fusion [h1_24, h1_32]. A typical data fusion pipeline is illustrated in Figure 2.1.

*   **Data Fusion and System Integration:** The goal is to fuse data into a single, information-rich model. A prime example is the system by Ge et al. [15], which achieves accurate 3D damage visualization by **projecting 2D crack features into 3D point cloud space**. The RABIT platform (Figure 2.2) further illustrates this, where data from GPR, impact-echo, and cameras are fused to generate unified condition maps of bridge decks [18, 19].

<br>
<div align="center">
    <b>Figure 2.1.</b> A conceptual diagram of a multi-sensor fusion pipeline for robotic inspection, illustrating the flow from raw sensor data (Camera, LiDAR, IMU) through calibration and synchronization to fused outputs like a 3D defect map.
</div>
<br>
<div align="center">
    <b>Figure 2.2.</b> The RABIT robotic inspection platform with its multi-sensor NDE suite labeled, including GPR arrays, cameras, and impact-echo sensors. (Source: [18])
</div>
<br>

*   **Sensor Calibration and Synchronization:** Reliable fusion hinges on precise spatio-temporal alignment. **Extrinsic calibration**, determining the relative pose (position and orientation) between different sensors, is a critical first step that must be performed with high accuracy [h1_17]. **Temporal synchronization** is equally crucial, especially for tightly-coupled algorithms like LIO-SAM [27], where even millisecond-level timing errors between IMU and LiDAR data can cause catastrophic failure of the state estimator. The need for robust, automated calibration frameworks is a recurring theme in the development of advanced multi-sensor robotic systems.

A summary of common sensor modalities and their characteristics is provided in Table 2.1.

<br>

**Table 2.1.** Sensor Modalities for Robotic Crack Detection.

| Sensor Modality          | Primary Function                                  | Corresponding Robotic Task(s)          | Key Limitations                                                              |
| ------------------------ | ------------------------------------------------- | -------------------------------------- | ---------------------------------------------------------------------------- |
| **RGB Camera**           | Capture high-resolution surface texture and color | Defect Detection, Visual Servoing      | Sensitive to illumination, motion blur, limited to surface features          |
| **LiDAR**                | Provide accurate 3D geometric point clouds        | Mapping, Localization (SLAM), Collision Avoidance during navigation | Sparse data, high cost, struggles with reflective or transparent surfaces    |
| **Thermal Camera**       | Measure surface temperature distribution          | Subsurface Defect Detection (delamination, voids) | Requires thermal contrast, interpretation can be complex, lower resolution |
| **IMU**                  | Measure linear acceleration and angular velocity  | State Estimation, Localization (SLAM)  | High drift over time, requires fusion with other sensors for accuracy      |
| **GPR / Eddy Current / Acoustic** | Detect internal material properties/defects       | Subsurface/Internal Defect Quantification | Often requires contact/close proximity, material-dependent, heavy payload    |
| **RGB-D / Structured Light** | Provide dense, short-range 3D depth maps        | Local Mapping, Collision Avoidance during navigation, 3D Reconstruction | Limited range, sensitive to ambient light (especially structured light)    |
| **Event Camera**             | Capture asynchronous brightness changes           | High-speed motion, challenging lighting conditions | Lower spatial resolution, different data processing paradigm |
| **Fiber Optic Sensor**       | Measure distributed strain and temperature        | Continuous Health Monitoring, Early crack detection | Requires installation on the structure, measures strain not direct cracks |

<br>

With the proliferation of multi-sensor robotic platforms, the challenge shifts from data acquisition to intelligent interpretation. The next section explores how advanced learning-based methods are leveraged to extract actionable insights from the rich, multimodal data streams produced by these sensor systems.

---

## Chapter 3. Algorithmic Advances in Intelligent Defect Perception

The preceding chapters established the physical robotic platforms and sensor systems that serve as the eyes and ears for structural assessment. This chapter delves into the brain of the inspection system: the sophisticated algorithms that translate raw, often noisy, sensor data into actionable knowledge about structural integrity. The automated detection of defects, particularly cracks, is a cornerstone of Structural Health Monitoring (SHM) and Non-Destructive Testing (NDT) across a vast spectrum of engineering domains. As highlighted in numerous reviews, the field spans from civil infrastructure like bridges and tunnels [52, 53, 54] to critical energy components such as pipelines [55, 56], wind turbine blades [57], and even in advanced materials manufacturing [58, 59] and medical diagnostics [60, 61]. The timely and accurate identification of defects is paramount for ensuring safety, operational reliability, and economic efficiency.

The evolution of these detection algorithms has been marked by a significant paradigm shift from methods based on handcrafted rules to data-driven deep learning (DL) models [62, 63, 64]. While traditional techniques laid a foundational groundwork, they often struggled with the robustness required for diverse real-world conditions. The advent of DL has revolutionized the field, offering unprecedented performance by learning complex defect features directly from data [65]. This chapter charts the trajectory of these algorithmic advances by systematically analyzing the specific innovations presented in recent literature. We will begin by addressing the foundational challenge of data quality through an examination of data-centric enhancement strategies. We will then review the architectural evolution of core DL models, followed by an exploration of how attention mechanisms and multi-modal data fusion are being employed to enhance perception. Finally, we will look beyond standard supervised training to discuss emerging learning paradigms that promise more autonomous and adaptable inspection systems.

### 3.1. Data-Centric Enhancements for Robust Detection

The performance of any deep learning model is fundamentally tethered to the quality and diversity of its training data. In practice, data acquired from field inspections is rarely pristine, suffering from a host of issues that can severely degrade algorithmic performance. Consequently, a significant stream of research has focused not on model architectures alone, but on data-centric strategies to enhance data quality. These strategies primarily involve data preprocessing, enhancement, and augmentation.

Data preprocessing aims to standardize inputs and accentuate salient defect features against complex backgrounds. This is a crucial first step addressed in many studies. For example, the review by **Azouz et al. [66]** systematically categorizes these initial steps, highlighting the common use of image filtering techniques to remove noise and blur, alongside image enhancement methods to improve contrast and visibility before any detection algorithm is applied. Expanding on this, **Peng et al. [67]**, in their review of visual perception for high dams, specifically point to histogram equalization (and its adaptive variant, CLAHE) and Retinex-based algorithms as key methods for mitigating the effects of variable illumination. The Retinex algorithm, inspired by the human visual system's ability to perceive consistent colors under different lighting, is particularly effective for normalizing images captured in challenging environments like dam tunnels where lighting is often poor and inconsistent. Beyond these pixel-level adjustments, more advanced data-centric strategies are gaining traction. These include the use of Generative Adversarial Networks (GANs) to synthesize realistic defect images for data augmentation, thereby enriching the training set, and the application of domain adaptation techniques to improve model generalization across different inspection environments and conditions [68]. Furthermore, when dealing with multi-modal data, precise sensor calibration and spatio-temporal data alignment are critical preprocessing steps to ensure meaningful feature fusion later on [69]. These enhancement and augmentation stages are not mere formalities; they are foundational for ensuring that subsequent deep learning models can learn discriminative features effectively, moving beyond spurious correlations from noise and environmental artifacts to the true characteristics of defects.

*Once the raw data is enhanced and preprocessed, the next logical step is to design models capable of robust and accurate defect identification across varying conditions.*

### 3.2. Deep Learning-Based Detection Models: Architectures and Innovations

At the heart of any modern, automated inspection system lies the deep learning model responsible for identifying defects. The application of these models has progressed along a clear trajectory of increasing precision and efficiency, evolving from patch-based classifiers to end-to-end object detectors and finally to pixel-level segmentation networks.

#### 3.2.1. The Spectrum of Core Architectures: From Patches to Pixels

The initial application of deep learning to defect detection often involved transforming the problem into an image classification task. In this paradigm, a large image is divided into smaller patches, and a CNN is trained to classify each patch as "defective" or "non-defective". While foundational, this approach is computationally inefficient and ignores the global context of a defect, though it retains value in scenarios requiring few-shot or weakly-supervised learning where labeled data is scarce.

To address these limitations, the field rapidly adopted **end-to-end object detection** frameworks. These models process an entire image at once, localizing defects within rectangular bounding boxes. Among these, single-stage detectors like the You Only Look Once (YOLO) family became a dominant force due to their superior balance of speed and accuracy. The review by **Aromoye et al. [70]** highlights the critical role of such DL algorithms in autonomous UAV-based pipeline inspection, where real-time performance is paramount. The evolution of the YOLO series, from its early versions to more recent iterations like YOLOv5, YOLOv7, and YOLOv8, reflects a continuous drive towards optimizing this speed-accuracy trade-off for industrial applications, with numerous studies adapting these frameworks for specific defect detection tasks [71, 72, 73].

However, for rigorous engineering analysis, a coarse bounding box is often insufficient. This necessity drove the widespread adoption of **pixel-level semantic segmentation**, which has become the standard for high-precision defect analysis. These models classify every pixel in an image, producing a detailed mask that precisely outlines the defect's geometry. The U-Net architecture is the undisputed cornerstone of this domain. Its application is broad, as shown in the review by **Giannuzzi et al. [74]** on assessing the historic built environment. They document how U-Net and its variants are a primary tool for classifying decay phenomena, including cracks, on the surfaces of heritage buildings from image data, forming the basis for automated diagnostic systems. Similarly, **Khan [75]**, in a review focused on water resource management, points to segmentation models as a key technology for monitoring and inspecting hydraulic structures, where precise crack delineation is vital for assessing structural integrity.

#### 3.2.2. The Rise of Transformers and Hybrid Architectures

While CNNs excel at extracting local features through their convolutional kernels, their inherently local receptive fields can be a limitation for modeling the long-range dependencies characteristic of continuous, tortuous crack structures. This architectural constraint paved the way for the introduction of the **Vision Transformer (ViT)**. The Transformer's core **self-attention mechanism** allows it to model the contextual relationships between all parts of an image, enabling it to better perceive the global continuity of a defect. However, standard ViTs often struggled with modeling fine-grained local textures, a strength of CNNs. The review by **Qiao et al. [76]** on metal surface defect detection notes the emergence of Transformer-based models like the Swin Transformer, which introduces a hierarchical structure and shifted windows to improve local context modeling, as a state-of-the-art method. Consequently, the research frontier is increasingly focused on **hybrid architectures** that synergize the strengths of both paradigms, typically using a CNN as a powerful feature encoder and a Transformer as a context-aware decoder [77]. As these models grow in complexity, a parallel research trend towards lightweight Transformers (e.g., MobileViT) is emerging, aiming to make their global reasoning capabilities accessible to resource-constrained applications.

#### 3.2.3. Lightweight Models for Practical Deployment

The pursuit of higher accuracy often leads to larger and more complex models. However, for many real-world applications involving mobile or embedded systems such as UAVs, computational resources are strictly limited, necessitating a critical research focus on **model lightweighting and optimization**. The literature presents a clear toolbox of strategies to achieve this, primarily centered on adopting efficient backbone networks, integrating modular lightweight design principles, and leveraging automated architecture search.

A primary strategy is the replacement of standard backbones like ResNet with architectures purpose-built for efficiency. **GhostNet**, for instance, which generates more feature maps from cheaper linear operations, has seen significant adoption [78, 79]. Other popular lightweight backbones include **ShuffleNetV2** [71] and **MobileNet**, whose variants are used for tasks from track fastener detection to crack classification [80, 81]. Beyond selecting an efficient backbone, researchers have focused on fine-grained modular optimization by replacing standard convolutions with more efficient alternatives like **depth-wise separable convolution** [82], **Ghost Convolution**, and **GSConv** [79, 73]. Another powerful technique is **structural re-parameterization**, where a complex training-time module is fused into a simple, fast inference-time convolution, a principle demonstrated in the `RepBSB` module [72] and the `MobileOne` backbone [83]. Furthermore, automated methods like Neural Architecture Search (NAS), which led to architectures like MobileNetV3, are increasingly used to discover optimal lightweight model configurations automatically. These specific, targeted optimizations are crucial for developing models deployable on edge platforms like the NVIDIA Jetson series that can achieve real-time inference speeds suitable for practical field use.

Nevertheless, balancing model accuracy and efficiency remains a challenging trade-off, especially in edge-computing scenarios with dynamic real-time constraints.

<br>

**Table 1: Summary of Lightweighting Techniques and Applications**

| Lightweighting Strategy | Core Principle | Representative Model/Module | Application Example | Detection Target | Deployment Platform | Reference |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **Efficient Backbone** | Design a computationally efficient base network. | GhostNet | Improved YOLOv4 | Insulator Defects | UAV | [78], [79] |
| | | ShuffleNetV2 | YOLO-LRDD | Road Damage | Mobile Devices | [71] |
| | | MobileNet | Custom CNN | Track Fasteners | - | [81] |
| **Modular Optimization** | Replace standard components with lighter ones. | Depth-wise Separable Conv. | Modified U-Net | Bridge Cracks | UAS | [82] |
| | | GSConv | Modified YOLO | Transmission Line Defects| - | [73] |
| **Structural Re-parameterization** | Fuse complex training-time blocks into simple inference-time ones. | RepBSB Module | EMB-YOLO | Meter Box Defects | - | [72] |
| | | MobileOne | OFN Network | Transmission Line Defects| Edge Device | [83] |
| **Automated Search** | Automatically search for an optimal lightweight architecture. | NAS (e.g., for MobileNetV3)| - | Rail Surface Defects | - | [81] |

<br>

#### 3.2.4. Architectural Enhancements: Attention and Fusion

To further boost model performance beyond architectural modifications, researchers have increasingly focused on integrating mechanisms that intelligently process features and incorporate complementary data sources. Two key strategies have become prominent: employing attention mechanisms to dynamically focus computational resources, and fusing data from multiple sensor modalities to create a more comprehensive understanding of a defect's nature.

##### *******. Focusing on Salient Features with Attention Mechanisms
Attention mechanisms, inspired by human visual cognition, enable a network to dynamically weigh the importance of different features or spatial locations, allowing the model to focus on the most salient information and suppress irrelevant background noise. The reviewed literature reveals a significant and innovative application of various attention modules to enhance defect detection, which can be broadly categorized into channel attention, combined spatial-channel attention, and the self-attention mechanisms inherent to Transformers.

A common strategy involves integrating established, lightweight attention modules into existing network backbones to improve feature representation without significantly increasing computational cost. **Channel attention**, which adaptively re-calibrates channel-wise feature responses, is a popular choice. For instance, modules like Squeeze-and-Excitation (SE) [78] and Efficient Channel Attention (ECA) [71] have been successfully incorporated to improve model performance. Building on this, **combined channel and spatial attention** modules like the Convolutional Block Attention Module (CBAM) [72, 84, 85] and Coordinate Attention (CA) [86], which learn to focus on 'what' and 'where' simultaneously, have demonstrated strong results in enhancing feature discriminability in complex backgrounds.

Beyond simply applying existing modules, several studies propose **novel or modified attention mechanisms** tailored for specific defect detection tasks. For example, **Qiao et al. [77]** proposed the Defect Transformer (DefT), which includes a lightweight multi-pooling self-attention block to model global context efficiently. **Zhang et al. [87]** designed a two-branch lightweight transformer module (LGT) that leverages both coordinate attention for local features and Bi-Level Routing Attention for global context. Furthermore, custom modules like the Lightweight Channel-Spatial Attention (LCSA) [79] and Mixed Local Channel Attention (MLCA) [88] have been designed specifically to balance the trade-off between performance and efficiency for deployment on mobile platforms. These innovations demonstrate a clear trend towards designing specialized attention strategies that address the unique challenges of industrial defect detection.

*While attention mechanisms optimize how a model processes information from a single data source, multimodal fusion offers a path to a more holistic understanding by combining data from entirely different sensor types.*

##### *******. Creating a Holistic View with Multimodal Fusion
Of greater emphasis in the reviewed literature is **multimodal feature fusion**, which offers a holistic view by combining information from disparate sensor types. Fusion strategies can be broadly categorized by the level at which integration occurs: **early fusion** combines raw data at the input level, **intermediate fusion** merges features at various depths within the networks, and **late fusion** combines the outputs or decisions of separate models. A single sensor modality often provides an incomplete picture. By fusing these data streams, a system can make a far more reliable and comprehensive assessment. The literature reveals a rich landscape of fusion strategies across NDT/SHM:
*   **Vision and Thermal:** Fusing RGB images with data from Infrared Thermography (IRT) is highly effective for detecting subsurface defects that create thermal anomalies. The review by **Alsuhaibani et al. [69]** on NDT for FRP-concrete structures highlights the power of this combination. Active thermography techniques, where an external heat source is applied, can reveal delaminations and voids, and fusing this thermal data with a visual image allows for precise localization of the subsurface flaw. A specific application is detailed by **Oswald-Tranta [89]**, who reviews inductive thermography for metallic parts. An induced eddy current heats the component, and an IRT camera captures the thermal response; cracks disrupt the heat flow, creating clear thermal patterns that, when mapped to a visual image, provide a complete diagnostic.
*   **Vision and Acoustic/Ultrasonic:** Correlating visual evidence with data from Acoustic Emission (AE) or Guided-Wave Ultrasonic Testing (GUT) can provide insight into a defect's activity and severity. For example, the review by **Ding et al. [57]** on wind turbine blade monitoring explains that AE sensors can detect the high-frequency stress waves released by active crack growth inside the blade's composite structure long before the damage is visible. Fusing this early warning with subsequent visual inspection from a drone allows for targeted and efficient maintenance. Similarly, **Nuthalapati's review [90]** on stress corrosion cracking discusses how AE can monitor the initiation and propagation of micro-cracks in stainless steel components in real-time.
*   **Vision and Electromagnetic:** For metallic structures, fusing visual inspection with methods like Eddy Current Testing (ECT) can provide comprehensive diagnostics. As reviewed by **Machado et al. [91]**, ECT probes are highly sensitive to surface and near-surface cracks in conductive materials. By combining the precise crack detection of ECT with the broader contextual awareness of a vision system, inspectors can gain a more complete understanding of the structural integrity of critical metallic components.

### 3.3. Learning Paradigms Beyond Supervised Training

While the majority of research in defect detection is based on supervised learning, the reliance on large, meticulously annotated datasets remains a significant bottleneck. This has motivated the exploration of alternative learning paradigms that can reduce the dependency on labeled data and enhance model adaptability.

**Transfer learning** is arguably the most widely adopted and impactful of these paradigms. Instead of training a model from scratch, this approach initializes the network with weights that have been pre-trained on a massive, general-purpose dataset (e.g., ImageNet). The model, having already learned a rich hierarchy of generic visual features, can then be fine-tuned on a much smaller, domain-specific dataset of defects. This strategy dramatically reduces the amount of labeled data and training time required while often improving final performance. Its ubiquity is such that it is a foundational, though often implicitly stated, technique in many of the application-focused papers reviewed, such as those employing established architectures like U-Net or YOLO for specific detection tasks.

Moving beyond standard transfer learning, a promising frontier is the application of **reinforcement learning (RL)** to create more intelligent and efficient inspection workflows, particularly for robotic agents. An RL-based agent can learn an optimal inspection policy through trial and error. As reviewed by **Zhang et al. [92]**, the navigation of UAVs in complex, GPS-denied environments like tunnels is a major challenge. They highlight deep reinforcement learning (DRL) as a key enabling technology. A DRL agent can be trained in simulation to learn how to navigate, avoid obstacles, and maintain optimal camera angles for inspection, receiving rewards for efficient and comprehensive data acquisition. This "active vision" approach signals a shift from simply building accurate detectors to creating truly intelligent and autonomous inspection systems that can learn and adapt with minimal human supervision.

### 3.4. Chapter Summary

In summary, the algorithmic backbone of intelligent defect perception has evolved rapidly, moving from conventional patch classifiers to sophisticated hybrid architectures and optimized lightweight models suitable for deployment on embedded platforms. The integration of attention mechanisms and multi-modal data fusion further empowers these systems to handle complex real-world scenarios with enhanced precision. However, challenges remain in balancing model complexity, data diversity, and generalization capability—issues that are increasingly being addressed by emerging learning paradigms and will be central to the discussion of future challenges and research directions in the subsequent chapters. 

## Chapter 4. From Perception to Action: System Integration and Autonomous Behaviors

<br>
<div align="center">
<pre class="mermaid">
graph TD
    subgraph "Perception Pipeline"
        A[Sensor Data<br>(e.g., Images, LiDAR)] --> B{Crack Detection Model<br>(On-Device)};
        B -- Detection Result --> C{Behavioral Trigger Logic};
    end

    subgraph "Robotic Behavior"
        C -- Command --> D[Motion Planning & Control<br><b>(e.g., BT, MPC, RL)</b>];
        D --> E[Actuator Execution];
    end
    
    E --> A;

    linkStyle 4 stroke:red,stroke-width:2px,stroke-dasharray: 5 5;
</pre>
<br>
<b>Figure 4.2.</b> The fundamental perception-action loop in an intelligent inspection robot. On-device perception (left) generates results that feed into a planning and control module (right), which in turn executes physical actions, creating a closed-loop system.
</div>
<br>

While advanced perception algorithms (Chapter 3) and robust robotic platforms (Chapter 2) are fundamental pillars, their true value is unlocked only when they are effectively integrated into a cohesive, field-ready system. This chapter bridges the gap between theoretical models and practical application, charting the critical path from an abstract algorithm to an intelligent agent capable of autonomous action. We adopt a process-oriented perspective, dissecting the engineering pipeline into four key stages: (1) preparing and optimizing the model for on-device deployment under stringent resource constraints; (2) integrating perception into the robot's decision-making loop to enable intelligent behavior; (3) examining real-world deployment cases through the lens of their behavioral autonomy; and (4) synthesizing the discussion into a set of guiding principles for developing robust robotic inspection systems.

### 4.1. Embedded AI: Balancing Computational Efficiency and Perceptual Fidelity

The sophisticated deep learning models that achieve state-of-the-art accuracy are often computationally demanding, rendering them unsuitable for direct deployment on mobile robots. These platforms operate under strict **runtime constraints**, including real-time latency requirements, limited onboard memory, stringent power consumption budgets, and thermal stability limits [93, 94]. Consequently, a critical first step in the deployment pipeline is model optimization, a field often termed "On-Device AI" or "Resource-Aware AI" [95, 94], which tailors large models for efficient inference on edge-computing hardware.

The pathway to deployment is supported by a rich and diverse ecosystem of tools. These tools can be categorized into three main groups:

1.  **Production-Grade Frameworks from Major Ecosystems:** These are the most common tools for deploying models on mobile and embedded devices. Google's **TensorFlow Lite (TFLite)**[96] and **PyTorch Mobile** [98]are mature frameworks that optimize models for a wide array of platforms, including Android, iOS, and single-board computers like the Jetson series. For instance, **Memon et al. [97]** showcased the effectiveness of TFLite by deploying a quantized model for MRI classification on a low-cost Android phone.

2.  **Hardware-Specific Acceleration Libraries:** To achieve maximum performance, hardware vendors provide specialized libraries that are tightly integrated with their processors. **NVIDIA's TensorRT** is a prime example, applying aggressive, hardware-specific optimizations to maximize throughput on NVIDIA GPUs, as leveraged by **Zhao et al. [99]** to achieve real-time remote sensing detection on a Jetson Orin NX. Apple's **CoreML** [100]serves a similar role within its ecosystem, optimizing models for the CPU, GPU, and Neural Engine on its devices.

3.  **Intermediate Representation & Compilation Frameworks:** A third category focuses on model portability and optimization across diverse hardware backends. Frameworks like **ONNX Runtime** provide a standardized format for model representation, while compilers like **Apache TVM** can automatically optimize models for various targets, from CPUs and GPUs to more specialized accelerators. This approach enhances flexibility but may require more complex integration.

Across these categories, several key optimization techniques are universally employed (Table 4.1). Beyond deploying on powerful single-board computers, research is also pushing towards ultra-low-power microcontrollers (MCUs) using specialized libraries like **CMSIS-NN**, enabling simple inference on platforms like the STM32 or Arduino.

<br>

**Table 4.1.** Overview of Common Model Optimization Techniques for Edge Deployment.

| Optimization Technique          | Core Principle                                                              | Primary Benefit(s)                                | Potential Drawback(s)                      |
| :------------------------------ | :-------------------------------------------------------------------------- | :------------------------------------------------ | :--------------------------------------- |
| **Quantization**                | Reduces the bit-precision of model weights and activations (e.g., FP32 → INT8). | Significant reduction in model size and latency. | Potential for minor accuracy degradation.       |
| **Pruning**                     | Removes redundant (low-magnitude) weights or network structures.            | Reduces model size and computational complexity.  | Can be complex to implement; may harm accuracy. |
| **Knowledge Distillation**      | A large "teacher" model trains a smaller "student" model.                    | Transfers knowledge to a compact model.           | Requires a powerful teacher model and extra training. |
| **Hardware-Aware NAS**          | Automates the search for optimal network architectures for specific hardware. | Discovers highly efficient, platform-specific models. | Computationally very expensive search process. |

<br>

The selection of a deployment framework involves navigating these trade-offs based on the project's specific needs regarding platform support, performance requirements, and development resources, as outlined in Table 4.2.

<br>

**Table 4.2.** Comparison of Representative Edge AI Deployment Toolchains.

| Toolchain               | Developer/Origin | Primary Ecosystem | Key Characteristic                                     | Target Platform(s)                     |
| :---------------------- | :--------------- | :--------------- | :----------------------------------------------------- | :------------------------------------- |
| **TensorFlow Lite**     | Google           | TensorFlow       | Mature, versatile, broad platform support.             | Android, iOS, Linux (inc. RPi), MCUs     |
| **PyTorch Mobile**      | Meta AI          | PyTorch          | Seamless transition from research to mobile deployment.  | Android, iOS, Linux                  |
| **TensorRT**            | NVIDIA           | CUDA / NVIDIA    | High-performance inference optimization for NVIDIA GPUs. | NVIDIA GPUs (Jetson, Tesla, etc.)      |
| **CoreML**              | Apple            | Apple            | Tightly integrated for optimal performance on Apple hardware. | iOS, macOS, watchOS                  |
| **OpenVINO**            | Intel            | Intel            | Optimizes inference for Intel CPUs, iGPUs, and VPUs.     | Intel Hardware                       |
| **ONNX Runtime**        | Microsoft        | Agnostic         | Cross-platform engine for a standard model format.     | Cross-Platform (CPU, GPU)              |
| **Apache TVM**          | Apache           | Agnostic         | Compiles models to optimized code for diverse hardware. | Cross-Platform (CPU, GPU, Accelerators) |

4.2. From Detection to Decision: Enabling Robotic Crack-Driven Behaviors

Once a model is deployed, its outputs must be translated into robotic action. This section bridges this gap by exploring the architectural designs and control strategies that form the perception-to-action loop.

#### 4.2.1. Architectural Design: Modular vs. Tightly Integrated Systems
The first design decision is architectural: how tightly should perception, planning, and control be coupled? This trade-off between modularity and responsiveness determines the system's flexibility, latency, and fault tolerance.

Loosely-coupled systems, often built on middleware such as the Robot Operating System (ROS), divide perception, mapping, planning, and actuation into separate nodes communicating via message-passing interfaces [101]. This promotes modularity and development flexibility. For example, a crack detection module might operate independently of the motion planner, simply publishing detection results to a shared topic. While suitable for regular survey tasks or offline data collection, such architectures introduce latency and are less effective in dynamic, time-critical scenarios.

Tightly-coupled systems integrate perception and control more deeply, typically through shared state representations or unified optimization frameworks. A prime example is visual-inertial odometry (VIO), where inertial measurements are fused with image features at high frequency to estimate precise robot pose in real time [102]. This low-latency coupling is critical when crack detection directly affects motion—e.g., when the robot must adjust its path immediately upon defect discovery.

In crack inspection, loosely-coupled architectures are typically sufficient for routine path-following, logging, and offline analysis. However, tightly-coupled systems become essential when perception outputs must dynamically trigger in-situ behavioral adjustments—such as stopping, slowing down, or reorienting toward defects.

#### 4.2.2. Closing the Loop: From Visual Detection to Motion Behavior

The integration of perception and action manifests in a spectrum of autonomous behaviors, which can be classified into a three-level taxonomy based on the depth of the feedback loop.

The most fundamental level consists of **Type I: Open-Loop Systems**, which function primarily as mobile data loggers. In this paradigm, the robot's perception algorithms do not influence its motion during a mission. The robot executes a pre-programmed trajectory, such as a lawnmower pattern, to ensure systematic coverage. This approach is exemplified by the pavement inspection UGV from **Khan et al. [103]** and the ROADS rover by **Mei et al. [104]**, which navigate a predefined grid to collect imagery for later analysis, producing a final "severity map." The design of such systems prioritizes simplicity and robustness, but this comes at the cost of efficiency, as the robot spends equal time on both pristine and damaged surfaces without the ability to dynamically adapt its path.

The next level of autonomy is found in **Type II: Reactive Systems**, which establish a direct, rule-based link between perception and action. These systems implement a "sense-act" loop where a specific detection triggers a hard-coded behavior, often structured using **Behavior Trees (BTs)** for modularity and transparency [105]. A prime example is the mine shaft inspection robot by **Tang et al. [114]**, whose control logic explicitly switches from an "area search" mode to a "crack following" mode upon detection, with the crack's geometry dictating the subsequent path. This reactive design is highly efficient for detailed characterization but is limited by its rigidity, as the pre-programmed "if-then" rules may not adapt well to unforeseen situations.

The most advanced level is **Type III: Deliberative Systems**, which exhibit goal-driven replanning. Here, perception outputs update a world model or high-level mission goal, leading to a complete re-evaluation of the robot's plan. This dynamic decision-making is enabled by advanced methodologies. **Model Predictive Control (MPC)** allows the robot to proactively optimize its path based on perceived defects while adhering to dynamic constraints, generating smooth, anticipatory trajectories [106, 107]. **Reinforcement Learning (RL)** offers a model-free alternative where an agent learns an optimal action policy through trial and error, enabling complex adaptive behaviors that are difficult to hand-engineer [108, 109]. Furthermore, a deliberative system must be reliable. This is achieved through **Fault-Tolerant Control**, which uses perception to detect anomalies and trigger recovery policies to maintain mission continuity [110, 111], and through **semantic-aware SLAM**, where the robot's understanding of the environment is enriched with object meaning (e.g., "support column" vs. "guardrail"), allowing for more intelligent, context-aware mission planning and replanning [112, 113]. While this represents true task-level autonomy, its high technical complexity and the challenge of verifying safety remain significant barriers.

This hierarchy is summarized in Table 4.3.

<br>

**Table 4.3.** Comparative Taxonomy of Robotic Inspection Systems by Autonomy Level.

| Feature                      | Type I: Open-Loop Systems              | Type II: Reactive Systems                 | Type III: Deliberative Systems            |
| :--------------------------- | :------------------------------------- | :---------------------------------------- | :---------------------------------------- |
| **Core Philosophy**          | Robot as a mobile data logger.         | Robot as a feature-following agent.       | Robot as an autonomous mission manager.   |
| **Perception-Action Link**   | **None (Offline Analysis).** Perception does not affect motion. | **Direct & Rule-Based.** Detection triggers a specific, pre-programmed action. | **Indirect & Goal-Based.** Perception updates a world model or mission goal, leading to replanning. |
| **Enabling Technologies**    | Pre-scripted Paths, GPS/Odometry       | State Machines, Behavior Trees [105]       | MPC [107], RL [109], Semantic SLAM [112] |
| **Example Behavior**         | Scans a bridge deck in a grid pattern and produces a crack map later. | Sees a crack and immediately follows its path to scan it completely. | Sees a dense cluster of cracks and decides to abandon its current survey to investigate the new area. |
| **Primary Limitation**       | Inefficient; no real-time adaptation.  | Rigid; struggles with situations not covered by rules. | High complexity; safety verification is challenging. |
| **Representative Refs**      | [103], [104]                           | [114, 105]                                | [107, 109, 112]                           |

### 4.3. Lessons Learned and Key Principles for Deployment

Synthesizing the journey from algorithm to field-deployed robot, four key principles emerge as crucial for the successful development of the next generation of robotic inspection systems.

1.  **Hardware-Software Co-Design is Non-Negotiable.** An effective system cannot be designed in silos. The choice of perception algorithm is constrained by the hardware's limitations (power, compute), and the hardware selection must be informed by the algorithmic requirements (sensor type, data bandwidth). Advanced approaches even use hardware characteristics, such as via Roofline analysis, to guide the design of the neural network architecture itself [99].
2.  **From Open-Loop to Closed-Loop Intelligence.** The most significant value is unlocked when systems move beyond passive data collection to create a closed perception-action loop. This enables the robot to behave intelligently—adapting its path, reacting to unforeseen events, and ensuring mission safety and success through dynamic feedback [110, 111, 109].
3.  **Bridge the Sim-to-Real Gap with Data-Centric Strategies.** A persistent challenge in robotics is that models trained in simulation or on clean datasets often fail in the unstructured real world. Overcoming this "sim-to-real" gap requires a data-centric approach: creating diverse, representative datasets [115, 116], employing robust data augmentation, and leveraging modern simulation platforms to test algorithms under a wide range of conditions before deployment [117, 118].
4.  **Embrace Holistic, System-Level Thinking.** Progress in robotic inspection requires a shift from metric-centric evaluation to system-level optimization, where perception, planning, actuation, and computational infrastructure are designed as an integrated whole. The overall reliability and performance of the integrated system is what ultimately determines its real-world utility. The frontier is moving towards **end-to-end learning architectures** that jointly optimize perception and control, and the integration of **semantic scene understanding** [112] will be paramount for making more intelligent and safe decisions.

## Chapter 5. Discussion: Persistent Challenges and Future Paradigms

Building on the state-of-the-art methods and system-level advances surveyed in the previous chapters, this section reflects on the remaining bottlenecks and their implications for future progress. The significant progress detailed in Chapters 2-4 demonstrates a clear trajectory towards autonomous robotic inspection systems. From versatile mobile platforms and sophisticated sensors to advanced perception algorithms and integrated system architectures, the foundational components for automating structural assessment are rapidly maturing. However, despite these advancements, the transition from controlled laboratory demonstrations to robust, scalable, and commercially viable field deployments is impeded by several persistent and interconnected challenges. Acknowledging these hurdles is crucial for steering future research effectively.

This chapter first dissects the most critical challenges confronting the field, spanning algorithm reliability, hardware-software co-design, and system-level integration. Subsequently, it outlines promising future research paradigms that directly address these issues, holding the potential to overcome existing limitations and pave the way for the next generation of truly intelligent and collaborative inspection robotics.

### 5.1. Persistent Challenges in Achieving Scalable and Generalizable Robotic Inspection

While the literature showcases numerous successful prototypes, their operational scope is often confined to specific, well-defined conditions. Broader adoption is hindered by fundamental issues of robustness, efficiency, integration complexity, and trustworthiness.

**5.1.1. Robustness and Generalization in Uncontrolled Environments**

A primary challenge is the limited robustness and generalization capability of perception algorithms when faced with the variability of real-world environments. As discussed in Chapter 3, the performance of deep learning models can degrade significantly due to domain shift—discrepancies between the training data and the operational environment. Factors such as inconsistent illumination (e.g., shadows, glare), diverse surface textures (e.g., concrete, asphalt, corroded metal), and the presence of confounding features (e.g., stains, rust, sealant marks) can easily lead to false positives or missed detections. The highly specialized nature of systems like the mine shaft inspector [114] underscores this point: its success is tied to a custom design for a single, albeit challenging, environment, highlighting the gap that remains towards achieving universal, "all-weather" inspection solutions.

**5.1.2. The Trade-off Between Lightweight Design and Algorithmic Accuracy**

As detailed in Sections 3.2.3 and 4.1, deploying complex deep learning models on resource-constrained robotic platforms necessitates aggressive optimization. Techniques like quantization (e.g., INT8 precision), model pruning, and knowledge distillation are essential for achieving real-time inference on edge devices like the Jetson series [99]. However, these optimization techniques risk impairing the model's sensitivity to fine-grained visual cues, such as incipient or low-contrast cracks, which are often precursors to major structural failures. Balancing compactness with accuracy thus remains a central dilemma for embedded inspection systems. This hardware-software coupling poses a significant challenge, as the optimal optimization strategy is deeply dependent on the specific target hardware.

**5.1.3. The Complexity of System Integration and Heterogeneous Data Fusion**

The development of a functional inspection robot is a multidisciplinary engineering challenge that extends far beyond the perception algorithm itself. As the system architectures in Chapter 4 illustrate, a cohesive integration of perception, planning, and control modules is paramount. The choice between loosely-coupled and tightly-coupled designs [102] involves complex trade-offs between modularity, latency, and performance. Furthermore, effectively fusing data from heterogeneous sensors (e.g., synchronizing high-resolution RGB images with sparse LiDAR point clouds and IMU readings) remains a non-trivial task, meaning many systems still rely predominantly on a single primary sensor, leaving the potential of multi-modal sensing largely untapped.

**5.1.4. Lack of Interpretability and Trust in Decision-Making**

As inspection results are often used to inform high-stakes maintenance decisions, the black-box nature of most deep learning models poses a significant barrier to real-world adoption. Without interpretable justifications for detections, human inspectors may struggle to trust the system's output, especially in ambiguous or safety-critical scenarios. This underscores the need for eXplainable AI (XAI) techniques, such as attention heatmaps or counterfactual visualizations, to foster user trust and ensure accountable system behavior.

### 5.2. Future Paradigms for Intelligent and Collaborative Inspection Robotics

From the challenges identified, an accelerating trajectory toward more holistic, adaptive, and trustworthy systems emerges. The following future paradigms directly address the bottlenecks discussed above, representing not merely incremental improvements but a fundamental shift in how robotic inspection systems are conceived and developed.

<br>
<div align="center">
<pre class="mermaid">
graph TD
    subgraph "A. Traditional Inspection Pipeline"
        A1[1. Manual Data Collection] --> A2[2. Offline Data Analysis];
        A2 --> A3[3. Static Report Generation];
    end

    subgraph "B. Future Intelligent & Collaborative Pipeline"
        B1[1. Multi-Agent Autonomous Data Acquisition<br>(e.g., UAV-UGV Team)] --> B2(2. Edge-Cloud Collaborative Inference);
        B2 -- Real-time Detections --> B3{3. Human-in-the-Loop Verification<br>(via XAI Interfaces)};
        B3 -- Verified Anomalies --> B4[4. Dynamic 3D Digital Twin Update];
        B4 --> B5[5. Predictive Maintenance Planning];
    end
    
    style A1 fill:#f8cecc,stroke:#b85450,stroke-width:1.5px
    style A2 fill:#f8cecc,stroke:#b85450,stroke-width:1.5px
    style A3 fill:#f8cecc,stroke:#b85450,stroke-width:1.5px

    style B1 fill:#cde4ff,stroke:#6699ff,stroke-width:2px
    style B2 fill:#cde4ff,stroke:#6699ff,stroke-width:2px
    style B3 fill:#cde4ff,stroke:#6699ff,stroke-width:2px
    style B4 fill:#cde4ff,stroke:#6699ff,stroke-width:2px
    style B5 fill:#cde4ff,stroke:#6699ff,stroke-width:2px
</pre>
<br>
<b>Figure 5.1.</b> A comparative schematic of traditional versus future inspection workflows, illustrating the shift from a linear, offline process to a dynamic, collaborative, and intelligent ecosystem.
</div>
<br>

Table 5.1 provides a high-level overview of how these emerging solutions map to the persistent challenges.

<br>

**5.2.1. Towards Unified and Foundational Models for Inspection**

To address the robustness and generalization issues discussed in 5.1.1, the adoption of large-scale, pre-trained foundation models offers a promising solution. While these models offer significant promise, applying them to defect inspection introduces unique challenges. Domain mismatch between general web-scale data and specific engineering materials, a scarcity of defect-specific labels for fine-tuning, and the constraints of edge deployment all pose significant hurdles. However, these same limitations create opportunities for innovative adaptation strategies. Emerging Vision-Language Models (VLMs) and other self-supervised learners acquire a rich visual understanding that can be adapted for crack detection with minimal fine-tuning. This paradigm enables a "large-model-small-model" collaborative architecture, which is underpinned by techniques such as **knowledge distillation**, where the foundation model acts as a "teacher" guiding the training of lightweight "student" models. Furthermore, **edge-cloud collaborative inference** strategies can balance real-time response and complexity by dynamically offloading heavier analytical tasks to remote servers when connectivity allows, directly tackling the trade-offs mentioned in 5.1.2.

**5.2.2. Data-Centric AI: Automated and Human-in-the-Loop Curation**

Given that model performance is critically dependent on data, a stronger focus on data-centric AI is warranted. Instead of solely pursuing novel model architectures, future research must invest in techniques for automated data generation and curation. While advanced generative models (e.g., GANs, diffusion models) and high-fidelity simulators can create diverse, realistic training data, ensuring domain relevance remains a key challenge. This is where **human-in-the-loop annotation** systems become critical. By using active learning algorithms to intelligently select the most informative unlabeled data for a human expert to review, these systems maximize the value of limited expert time and accelerate the creation of robust, domain-specific datasets. This semi-automated approach not only improves model robustness but also enhances trustworthiness by embedding expert knowledge directly into the training loop.

**5.2.3. Multi-Task Learning for Integrated Navigation and Inspection**

To overcome the integration bottlenecks highlighted in 5.1.3, a shift away from siloed modules towards unified, multi-task learning models is essential. Despite their conceptual appeal, current integration methods remain brittle under shifting operational contexts. A single, end-to-end model could be trained to perform localization, mapping, and defect analysis simultaneously. These tasks are often synergistic; for instance, geometric features from the SLAM pipeline could provide strong contextual priors for identifying structural elements. Such integrated models would not only be more computationally efficient but could also lead to enhanced performance and reliability in complex, GPS-denied environments. Nonetheless, designing such unified architectures requires careful task balancing, as conflicting objectives (e.g., precise navigation vs. defect sensitivity) may lead to suboptimal convergence if not handled appropriately.

**5.2.4. Collaborative Multi-Agent Systems for Large-Scale Inspection**

Finally, for inspecting vast and complex infrastructure, the future lies in collaborative, multi-agent systems. Scalability remains elusive without a fundamental shift from single-robot solutions to coordinated, heterogeneous teams. This vision involves diverse platforms working in concert; for example, a swarm of UAVs could provide a rapid aerial survey to identify areas of interest, which would then guide ground-based UGVs for detailed, close-range inspection. Realizing this vision requires overcoming formidable research challenges in decentralized coordination, robust inter-agent communication, and fusing heterogeneous data from multiple mobile sources in real-time.

A crucial, often-overlooked aspect of these future systems is the necessity for deeper **interdisciplinary collaboration**. The design of a truly effective inspection robot cannot happen in a vacuum. It requires a convergence of expertise from robotics and AI with insights from civil engineering (to understand failure modes), materials science (to characterize surface properties), and human-computer interaction (to design usable and trustworthy interfaces for operators). Factoring in these human elements is key to translating technological potential into deployed reality.

In conclusion, achieving deployable, intelligent robotic inspection requires not only technical breakthroughs in perception and control, but also a holistic rethinking of how AI systems interact with uncertain environments, with human operators, and with one another. As the field moves forward, research must transition from task-specific optimizations to developing generalizable frameworks that can be adapted, interpreted, and trusted in real-world, safety-critical deployments.

## Chapter 6. Conclusion

Motivated by the growing demand for scalable, safe, and automated inspection systems, this review has systematically charted the evolving landscape of robotic crack inspection. Our work has aimed to provide a holistic and critical synthesis of these advancements to guide future research toward the development of truly practical and scalable solutions.

This review's primary contribution is a novel systems-level synthesis that connects perception algorithm design with robotic hardware constraints and deployment considerations, offering actionable guidance for co-optimization across the full visual inspection pipeline. This distinguishes our review from prior works that either remain algorithm-centric or focus narrowly on hardware platforms without addressing the interdependence between them. Through a deployment-oriented perspective, this review bridges the often-siloed domains of computer vision, robotics, and civil engineering, providing a unified framework for understanding the multifaceted challenges and opportunities. It is posited that future breakthroughs are more likely to emerge not from isolated component-level optimizations, but from a holistic co-design philosophy that tightly couples perception, planning, control, and hardware constraints.

Ultimately, this review serves as both a comprehensive summary of the state-of-the-art and a strategic roadmap. It clarifies that the grand challenge is no longer just about detecting cracks with higher accuracy in a laboratory, but about building robust, efficient, and trustworthy robotic systems that can operate autonomously in the complex, uncontrolled environments of our aging infrastructure. While this review provides a comprehensive synthesis, it is worth noting that the fast-paced evolution of AI and robotics may soon render some trends obsolete. As such, continual reassessment of emerging paradigms will be essential to maintain relevance and practical value in this rapidly transforming field.

Moving forward, we identify two strategic directions as particularly impactful for accelerating the transition from laboratory to field.

First, there is a pressing need for public, large-scale benchmark datasets and standardized evaluation protocols. These benchmarks must reflect the complexities of real-world inspection scenarios, including varied lighting conditions, occlusions, material textures, and environmental disturbances. The development of such standardized benchmarks is not merely an academic exercise but a prerequisite for driving meaningful, reproducible progress and enabling fair comparison across different approaches.

Second, tighter integration of visual perception with robust autonomous planning and control is critical. Future systems must move beyond simple detection to "active vision," where perception directly informs the robot's next action. This requires advances in real-time uncertainty modeling to quantify model confidence and human-in-the-loop frameworks that allow for seamless operator correction, ensuring both safety and trustworthiness in dynamic field conditions.

参考文献：
[1]Ahmed, Habib, Hung Manh La, and Nenad Gucunski. "Review of non-destructive civil infrastructure evaluation for bridges: State-of-the-art robotic platforms, sensors and algorithms." Sensors 20.14 (2020): 3954.
[2]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[3]Ahmed, Habib, Chuong Phuoc Le, and Hung Manh La. "Pixel-level classification for bridge deck rebar detection and localization using multi-stage deep encoder-decoder network." Developments in the Built Environment 14 (2023): 100132.
[4]Mohan, Arun, and Sumathi Poobal. "Crack detection using image processing: A critical review and analysis." alexandria engineering journal 57.2 (2018): 787-798.
[5]Prasanna, Prateek, et al. "Automated crack detection on concrete bridges." IEEE Transactions on automation science and engineering 13.2 (2014): 591-599.
[6]Alejo, David, et al. "SIAR: A ground robot solution for semi-autonomous inspection of visitable sewers." Advances in Robotics Research: From Lab to Market: ECHORD++: Robotic Science Supporting Innovation (2020): 275-296.
[7]Jung, Sungwook, et al. "Mechanism and system design of MAV (Micro Aerial Vehicle)-type wall-climbing robot for inspection of wind blades and non-flat surfaces." 2015 15th international conference on control, automation and systems (ICCAS). IEEE, 2015.
[8]Kanellakis, Christoforos, et al. "Towards visual inspection of wind turbines: A case of visual data acquisition using autonomous aerial robots." IEEE access 8 (2020): 181650-181661.
[9]Myung, Hyun, and Yang Wang. "Robotic sensing and systems for smart cities." Sensors 21.9 (2021): 2963.
[10]Merkle, Dominik, and Alexander Reiterer. "Automated Method for SLAM Evaluation in GNSS-Denied Areas." Remote Sensing 15.21 (2023): 5141.
[11]Shen, Yaoyang, et al. "A Texture-Based Simulation Framework for Pose Estimation." Applied Sciences 15.8 (2025): 4574.
[12]Burri, Michael, et al. "Aerial service robots for visual inspection of thermal power plant boiler systems." 2012 2nd international conference on applied robotics for the power industry (CARPI). IEEE, 2012.
[13]Jung, Sungwook, et al. "Bridge inspection using unmanned aerial vehicle based on HG-SLAM: Hierarchical graph-based SLAM." Remote Sensing 12.18 (2020): 3022.
[14]Wang, Xiaohui, Xi Ma, and Zhaowei Li. "Research on SLAM and path planning method of inspection robot in complex scenarios." Electronics 12.10 (2023): 2178.
[15]Ge, Liangfu, and Ayan Sadhu. "Deep learning-enhanced smart ground robotic system for automated structural damage inspection and mapping." Automation in Construction 170 (2025): 105951.
[16]Bui, Hoang-Dung, et al. "Control framework for a hybrid-steel bridge inspection robot." 2020 IEEE/RSJ International Conference on Intelligent Robots and Systems (IROS). IEEE, 2020.
[17]S. T. Nguyen, H. M. La, A Climbing Robot for Steel Bridge Inspection. Journal of Intelligent & Robotic Systems 102, 75 (2021).
[18]Gucunski, Nenad, et al. "Implementation of a fully autonomous platform for assessment of concrete bridge decks RABIT." Structures Congress 2015. 2015.
[19]La, Hung Manh, et al. "Autonomous robotic system for bridge deck data collection and analysis." 2014 IEEE/RSJ International Conference on Intelligent Robots and Systems. IEEE, 2014.
[20]Gibb, Spencer, et al. "A multi-functional inspection robot for civil infrastructure evaluation and maintenance." 2017 IEEE/RSJ international conference on intelligent robots and systems (IROS). IEEE, 2017.
[21]Zhang, Kong, et al. "Inspection of floating offshore wind turbines using multi-rotor unmanned aerial vehicles: literature review and trends." Sensors 24.3 (2024): 911.
[22]Lim, Ronny Salim, et al. "Developing a crack inspection robot for bridge maintenance." 2011 IEEE International Conference on Robotics and Automation. IEEE, 2011.
[23]La, Hung M., et al. "Mechatronic systems design for an autonomous robotic system for high-efficiency bridge deck inspection and evaluation." IEEE/ASME transactions on mechatronics 18.6 (2013): 1655-1664.
[24]Pfändler, Patrick, et al. "Non-destructive corrosion inspection of reinforced concrete structures using an autonomous flying robot." Automation in Construction 158 (2024): 105241.
[25]Lile, Cai, and Li Yiqun. "Anomaly detection in thermal images using deep neural networks." 2017 IEEE International conference on image processing (ICIP). IEEE, 2017.
[26]Menendez, Elisabeth, et al. "Tunnel structural inspection and assessment using an autonomous robotic system." Automation in Construction 87 (2018): 117-126.
[27]Shan, Tixiao, et al. "Lio-sam: Tightly-coupled lidar inertial odometry via smoothing and mapping." 2020 IEEE/RSJ international conference on intelligent robots and systems (IROS). IEEE, 2020.
[28]Xu, Wei, and Fu Zhang. "Fast-lio: A fast, robust lidar-inertial odometry package by tightly-coupled iterated kalman filter." IEEE Robotics and Automation Letters 6.2 (2021): 3317-3324.
[29]Jung, Sungwook, et al. "Multi-layer coverage path planner for autonomous structural inspection of high-rise structures." 2018 IEEE/RSJ International Conference on Intelligent Robots and Systems (IROS). IEEE, 2018.
[30]Xia, Zhe, et al. "Complete‐coverage path planning for surface inspection of cable‐stayed bridge tower based on building information models and climbing robots." Computer‐Aided Civil and Infrastructure Engineering (2025).
[31]La, Hung M., et al. "Autonomous robotic system for high-efficiency non-destructive bridge deck inspection and evaluation." 2013 IEEE International Conference on Automation Science and Engineering (CASE). IEEE, 2013.
[32]Kim, Yeeun, et al. "Step: State estimator for legged robots using a preintegrated foot velocity factor." IEEE Robotics and Automation Letters 7.2 (2022): 4456-4463.
[33]Bellicoso, C. Dario, et al. "Advances in real‐world applications for legged robots." Journal of Field Robotics 35.8 (2018): 1311-1326.
[34]Lee, Hyungyu, et al. "CAROS-Q: climbing aerial robot system adopting rotor offset with a quasi-decoupling controller." IEEE Robotics and Automation Letters 6.4 (2021): 8490-8497.
[35]Myeong, Wancheol, and Hyun Myung. "Development of a wall-climbing drone capable of vertical soft landing using a tilt-rotor mechanism." IEEE Access 7 (2018): 4868-4879.
[36]Myeong, Wan Cheol, et al. "Drone-type wall-climbing robot platform for structural health monitoring." Proc. Int. Conf. Advances in Experimental Structural Engineering. 2015.
[37]Myeong, Wan Cheol, et al. "Development of a drone-type wall-sticking and climbing robot." 2015 12th international conference on ubiquitous robots and ambient intelligence (URAI). IEEE, 2015.
[38]Choset, Howie. Bridge inspection with serpentine robots. Transportation Research Board, IDEA Program, 2002.
[39]Pasetto, Alberto, Yash Vyas, and Silvio Cocuzza. "Zero reaction torque trajectory tracking of an aerial manipulator through Extended Generalized Jacobian." Applied Sciences 12.23 (2022): 12254.
[40]Shu, Jiangpeng, Zhe Xia, and Yifan Gao. "BIM-Based Trajectory Planning for Unmanned Aerial Vehicle-Enabled Box Girder Bridge Inspection." Remote Sensing 17.4 (2025).
[41]Bristeau, Pierre-Jean, et al. "The navigation and control technology inside the ar. drone micro uav." IFAC Proceedings Volumes 44.1 (2011): 1477-1484.
[42]Salaan, Carl John O., et al. "Close visual bridge inspection using a UAV with a passive rotating spherical shell." Journal of Field Robotics 35.6 (2018): 850-867.s
[43]Muthugala, MA Viraj J., et al. "Raptor: a design of a drain inspection robot." Sensors 21.17 (2021): 5742.
[44]Ma, Ke, et al. "Robot mapping and localisation for feature sparse water pipes using voids as landmarks." Towards Autonomous Robotic Systems: 16th Annual Conference, TAROS 2015, Liverpool, UK, September 8-10, 2015, Proceedings 16. Springer International Publishing, 2015.
[45]Menendez, Elisabeth, et al. "Tunnel structural inspection and assessment using an autonomous robotic system." Automation in Construction 87 (2018): 117-126.
[46]Kim, Youngseok, and Jaesuk Ryou. "A study of sonar image stabilization of unmanned surface vehicle based on motion sensor for inspection of underwater infrastructure." Remote Sensing 12.21 (2020): 3481.
[47]Lu, Bing-Xian, Yu-Chung Tsai, and Kuo-Shih Tseng. "GRVINS: tightly coupled GNSS-range-visual-inertial system." Journal of Intelligent & Robotic Systems 110.1 (2024): 36.
[48]Lee, Alex Junho, et al. "Vivid++: Vision for visibility dataset." IEEE Robotics and Automation Letters 7.3 (2022): 6282-6289.
[49]Tang, Chongrui, et al. "Lay‐up defects inspection for automated fiber placement with structural light scanning and deep learning." Polymer Composites (2025).
[50]Mineo, Carmelo, et al. "Fine alignment of thermographic images for robotic inspection of parts with complex geometries." Sensors 22.16 (2022): 6267.
[51]Tian, Wei, et al. "Acoustic Signal‐Based Deep Learning Approach and Device for Detecting Interfacial Voids in Steel–Concrete Composite Structures." Advances in Civil Engineering 2025.1 (2025): 2347213.
[52]Luo, Kui, et al. "Computer vision-based bridge inspection and monitoring: A review." Sensors 23.18 (2023): 7863.
[53]Jiang, Yandan, et al. "Tunnel lining detection and retrofitting." Automation in Construction 152 (2023): 104881.
[54]Chen, Dong, Ben Huang, and Fei Kang. "A review of detection technologies for underwater cracks on concrete dam surfaces." Applied Sciences 13.6 (2023): 3564.
[55]Hussain, Muhammad, et al. "Review of prediction of stress corrosion cracking in gas pipelines using machine learning." Machines 12.1 (2024): 42.
[56]Wang, Hao, et al. "A comprehensive review of polyethylene pipes: Failure mechanisms, performance models, inspection methods, and repair solutions." Journal of Pipeline Science and Engineering 4.2 (2024): 100174.
[57]Ding, Shaohu, Chenchen Yang, and Sen Zhang. "Acoustic-signal-based damage detection of wind turbine blades—A review." Sensors 23.11 (2023): 4987.
[58]Javidrad, Hamidreza, et al. "Fatigue performance of metal additive manufacturing: A comprehensive overview." Virtual and Physical Prototyping 19.1 (2024): e2302556.
[59]Tonga, Danladi Agadi, et al. "Nondestructive evaluation of fiber-reinforced polymer using microwave techniques: A review." Coatings 13.3 (2023): 590.
[60]Yap, Rei Chiel, Meshal Alghanem, and Nicolas Martin. "A narrative review of cracks in teeth: Aetiology, microstructure and diagnostic challenges." Journal of Dentistry 138 (2023): 104683.
[61]Qi, Jinjin, and Zhen Li. "Non-destructive testing of human teeth using microwaves: a state-of-the-art review." Journal of Electrical Engineering 74.1 (2023): 40-47.
[62]Cha, Young-Jin, et al. "Deep learning-based structural health monitoring." Automation in Construction 161 (2024): 105328.
[63]Jia, Jing, and Ying Li. "Deep learning for structural health monitoring: Data, algorithms, applications, challenges, and trends." Sensors 23.21 (2023): 8824.
[64]Spencer Jr, Billie F., et al. "Advances in artificial intelligence for structural health monitoring: A comprehensive review." KSCE Journal of Civil Engineering 29.3 (2025): 100203.
[65]Zhang, Yishuang, Cheuk Lun Chow, and Denvid Lau. "Artificial intelligence-enhanced non-destructive defect detection for civil infrastructure." Automation in Construction 171 (2025): 105996.
[66]Azouz, Zakrya, Barmak Honarvar Shakibaei Asli, and Muhammad Khan. "Evolution of crack analysis in structures using image processing technique: A review." Electronics 12.18 (2023): 3862.
[67]Peng, Zhangjun, et al. "A Comprehensive Survey on Visual Perception Methods for Intelligent Inspection of High Dam Hubs." Sensors 24.16 (2024): 5246.
[68]Shim, Seungbo, et al. "Road damage detection using super-resolution and semi-supervised learning with generative adversarial network." Automation in construction 135 (2022): 104139.
[69]Alsuhaibani, Eyad. "Nondestructive Testing of Externally Bonded FRP Concrete Structures: A Comprehensive Review." Polymers 17.9 (2025): 1284.
[70]Aromoye, Ibrahim Akinjobi, et al. "Significant Advancements in UAV Technology for Reliable Oil and Gas Pipeline Monitoring." Computer Modeling in Engineering & Sciences (CMES) 142.2 (2025).
[71]Wan, Fang, et al. "YOLO-LRDD: A lightweight method for road damage detection based on improved YOLOv5s." EURASIP Journal on Advances in Signal Processing 2022.1 (2022): 98.
[72]Liu, Zhiyong, et al. "EMB-YOLO: Dataset, method and benchmark for electric meter box defect detection." Journal of King Saud University-Computer and Information Sciences 36.2 (2024): 101936.
[73]Hao, Shuai, et al. "Transmission Line Defect Target-Detection Method Based on GR-YOLOv8." Sensors 24.21 (2024): 6838.
[74]Giannuzzi, Valeria, and Fabio Fatiguso. "Historic Built Environment Assessment and Management by Deep Learning Techniques: A Scoping Review." Applied Sciences (2076-3417) 14.16 (2024).
[75]Iqbal, Umair, et al. "The last two decades of computer vision technologies in water resource management: A bibliometric analysis." Water and Environment Journal 37.3 (2023): 373-389.
[76]Qiao, Qi, et al. "A Review of Metal Surface Defect Detection Technologies in Industrial Applications." IEEE Access (2025).
[77]Wang, Junpu, et al. "Defect transformer: An efficient hybrid transformer architecture for surface defect detection." Measurement 211 (2023): 112614.
[78]Zhang, Shihai, et al. "Multi-objects recognition and self-explosion defect detection method for insulators based on lightweight GhostNet-YOLOV4 model deployed onboard UAV." IEEE Access 11 (2023): 39713-39725.
[79]Lu, Yang, et al. "A Lightweight Insulator Defect Detection Model Based on Drone Images." Drones 8.9 (2024): 431.
[80]Hou, Yue, et al. "MobileCrack: Object classification in asphalt pavements using an adaptive lightweight deep learning." Journal of Transportation Engineering, Part B: Pavements 147.1 (2021): 04020092.
[81]Zhang, Ye, et al. "An Improved Target Network Model for Rail Surface Defect Detection." Applied Sciences (2076-3417) 14.15 (2024).
[82]Song, Fei, Ying Sun, and Guixia Yuan. "Autonomous identification of bridge concrete cracks using unmanned aircraft images and improved lightweight deep convolutional networks." Structural Control and Health Monitoring 2024.1 (2024): 7857012.
[83]Pei, Shaotong, et al. "Lightweight transmission line defect identification method based on OFN network and distillation method." IET Image Processing 18.12 (2024): 3518-3529.
[84]Liu, Yanxing, et al. "Lightweight Insulator and Defect Detection Method Based on Improved YOLOv8." Applied Sciences 14.19 (2024): 8691.
[85]Yu, Jiangli, et al. "Lcg-yolo: A real-time surface defect detection method for metal components." IEEE Access 12 (2024): 41436-41451.
[86]Jiang, Tingyao, Xuan Hou, and Min Wang. "Insulator defect detection based on the cddcr–yolov8 algorithm." International Journal of Computational Intelligence Systems 17.1 (2024): 245.
[87]Chen, Weixun, Siming Meng, and Xueping Wang. "Local and Global Context-Enhanced Lightweight CenterNet for PCB Surface Defect Detection." Sensors 24.14 (2024): 4729.
[88]Wen, Runyuan, et al. "LESM-YOLO: An Improved Aircraft Ducts Defect Detection Model." Sensors (Basel, Switzerland) 24.13 (2024): 4331.
[89]Oswald-Tranta, Beate. "Inductive thermography–review of a non-destructive inspection technique for surface crack detection." Quantitative InfraRed Thermography Journal (2025): 1-25.
[90]Qiu, Zhaomei, et al. "DCS-YOLOv5s: A lightweight algorithm for multi-target recognition of potato seed potatoes based on YOLOv5s." Agronomy 14.11 (2024): 2558.
[91]Machado, Miguel A. "Eddy currents probe design for NDT applications: A review." Sensors (Basel, Switzerland) 24.17 (2024): 5819.
[92]Zhang, Ran, et al. "Unmanned aerial vehicle navigation in underground structure inspection: A review." Geological Journal 58.6 (2023): 2454-2472.
[93]Bodenham, Matthew, and Jaeha Kung. "Skipformer: Evolving Beyond Blocks for Extensively Searching On-Device Language Models With Learnable Attention Window." IEEE Access (2024).
[94]Zawish, Muhammad, et al. "Energy-Efficient Uncertainty-Aware Biomass Composition Prediction at the Edge." Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. 2024.
[95]Rizk, Mostafa, and Adel Chehade. "Efficient Oil Tank Detection Using Deep Learning: A Novel Dataset and Deployment on Edge Devices." IEEE Access (2024).
[96]TensorFlow Lite Documentation. Available online: https://www.tensorflow.org/lite (accessed on 8 June 2025).
[97]Memon, Khuhed, et al. "Edge Computing for AI-Based Brain MRI Applications: A Critical Evaluation of Real-Time Classification and Segmentation." Sensors 24.21 (2024): 7091.
[98]PyTorch Mobile Documentation. Available online: https://pytorch.org/mobile/home/<USER>
[99]Zhao, Boya, et al. "A Fast Target Detection Model for Remote Sensing Images Leveraging Roofline Analysis on Edge Computing Devices." IEEE Journal of Selected Topics in Applied Earth Observations and Remote Sensing (2024).
[100]CoreML Documentation. Available online: https://developer.apple.com/documentation/coreml (accessed on 8 June 2025).
[101]Grigorescu, Sorin, and Mihai Zaha. "CyberCortex. AI: An AI‐based operating system for autonomous robotics and complex automation." Journal of Field Robotics 42.2 (2025): 474-492.
[102]Guan, Qi, et al. "A dual-mode automatic switching feature points matching algorithm fusing IMU data." Measurement 185 (2021): 110043.
[103]Khan, Md Al-Masrur, et al. "Development of AI-and robotics-assisted automated pavement-crack-evaluation system." Remote Sensing 15.14 (2023): 3573.
[104]Mei, Alessandro, et al. "ROADS—rover for bituminous pavement distress survey: an unmanned ground vehicle (UGV) prototype for pavement distress evaluation." Sensors 22.9 (2022): 3414.
[105]Yang, Shuo, and Qi Zhang. "Towards efficient robotic software development by reusing behavior tree structures for task planning paradigms." Complex System Modeling and Simulation 3.4 (2023): 357-380.
[106]Lindemann, Lars, et al. "Safe planning in dynamic environments using conformal prediction." IEEE Robotics and Automation Letters 8.8 (2023): 5116-5123.
[107]Achirei, Stefan-Daniel, et al. "Model-predictive control for omnidirectional mobile robots in logistic environments based on object detection using CNNs." Sensors 23.11 (2023): 4992.
[108]Lakshmanan, Anirudh Krishna, et al. "Complete coverage path planning using reinforcement learning for tetromino based cleaning and maintenance robot." Automation in Construction 112 (2020): 103078.
[109]Pintos Gómez de las Heras, Borja, Rafael Martínez-Tomás, and José Manuel Cuadra Troncoso. "Self-Learning Robot Autonomous Navigation with Deep Reinforcement Learning Techniques." Applied Sciences 14.1 (2023): 366.
[110]Luo, Shuangqi, et al. "Endowing robots with longer-term autonomy by recovering from external disturbances in manipulation through grounded anomaly classification and recovery policies." Journal of Intelligent & Robotic Systems 101.3 (2021): 51.
[111]Zhou, Xingyu, et al. "Event-Triggered Robust Adaptive Fault-Tolerant Tracking and Vibration Control for the Rigid-Flexible Coupled Robotic Mechanisms With Large Beam-Deformations." IEEE Transactions on Systems, Man, and Cybernetics: Systems (2025).
[112]Zheng, Chen, et al. "Semantic map construction approach for human-robot collaborative manufacturing." Robotics and Computer-Integrated Manufacturing 91 (2025): 102845.
[113]Huang, Xiaotao, et al. "ADM-SLAM: Accurate and Fast Dynamic Visual SLAM with Adaptive Feature Point Extraction, Deeplabv3pro, and Multi-View Geometry." Sensors 24.11 (2024): 3578.
[114]Tang, Chaoquan, et al. "Inspection robot and wall surface detection method for coal mine wind shaft." Applied Sciences 13.9 (2023): 5662.
[115]Wei, Hexiang, et al. "Fusionportablev2: A unified multi-sensor dataset for generalized slam across diverse platforms and scalable environments." The International Journal of Robotics Research (2024): 02783649241303525.
[116]Feng, Dapeng, et al. "S3E: A Multi-Robot Multimodal Dataset for Collaborative SLAM." IEEE Robotics and Automation Letters (2024).
[117]Pérez-Higueras, Noé, et al. "Hunavsim: A ros 2 human navigation simulator for benchmarking human-aware robot navigation." IEEE robotics and automation letters 8.11 (2023): 7130-7137.
[118]Bakirci, Murat. "Simulation of Autonomous Driving for a Line-Following Robotic Vehicle: Determining the Optimal Manoeuvring Mode." Elektronika ir Elektrotechnika 29.6 (2023): 4-11.



