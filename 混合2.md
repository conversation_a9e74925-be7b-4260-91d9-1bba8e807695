【文献 111】
作者: <PERSON>, SF
来源: INDUSTRIAL ROBOT-THE INTERNATIONAL JOURNAL OF ROBOTICS RESEARCH AND
题目: Design and implementation of a magnetic suction fan tower inspection
摘要: PurposeThe purpose of this paper is to design a new type of magnetic suction wall-climbing robot suitable for the wall inspection of wind turbine towers to solve the problems in manual maintenance tasks.Design/methodology/approachBy analyzing the shortcomings of existing wall-climbing robots, a magnetic suction integrated wheel structure is designed to effectively combine the adsorption structure and transmission structure. To enable the robot to adapt to the curvature of the wall surface of a wind turbine tower, a passive adaptive curvature structure is designed. The effects of the air gap, the thickness of the wheel plates on both sides, the size of permanent magnets and the size of aluminum rings on the adsorption force are studied. Through mechanical model analysis under different instability conditions, the magnetic circuit of the magnetic wheel is optimized and designed.FindingsApplying the wall-climbing robot to engineering practice, experiments have shown that the developed wall-climbing robot can move safely and stably on the wall of the wind turbine tower. The robot can also carry a load of 20 kg, and the designed adaptive structure can cause the magnetic wheel to deflect up to 20 degrees relative to the vehicle body, fully meeting the curvature requirements of the minimum diameter end of the wind turbine tower.Originality/valueThis paper proposes a magnetic suction integrated wheel structure through analysis of the working environment. And the parameters affecting the magnetic wheel adsorption performance were optimized. Meanwhile, a passive adaptive wind turbine tower curvature structure was proposed.

【文献 112】
作者: Zhang, XX
来源: 2024 9TH ASIA-PACIFIC CONFERENCE ON INTELLIGENT ROBOT SYSTEMS, ACIRS
题目: General Design of Endoscopic Inspection Robot Faced to Aeroengine Stator
摘要: Endoscopic inspection technology is mainly used in regular maintenance for aeroengines. However, there are some inner components that are hard to be observed by this means, such as stator blades, which leaves many potential safety troubles. Recently there have been some robots emerging for aeroengine maintenance and inspection, which have undergone significant changes compared to current methods. But at present they are still unable to achieve in engineering applications for their immaturities. To increase the possibility of problem solving, this paper provided a summary of them, and presented a novel approach against their shortcomings, which involves an endoscopic inspection robot. According to the general design of this robot, the typical structures of both main parts, the snakelike arm and the passive blade grasper were given.

【文献 113】
作者: Wertjanz, D
来源: 2023 IEEE INTERNATIONAL INSTRUMENTATION AND MEASUREMENT TECHNOLOGY
题目: Robot-based measurement system for double-sided inspection of optical
摘要: This paper presents a robotic measurement system for the precise double-sided inline inspection of optical elements. The robotic system includes an electromagnetically actuated measurement platform (MP), capable of inducing a stiff link between the integrated high precision confocal chromatic sensor (CCS) and the sample by means of feedback control. In this way, disturbing relative motion between the CCS and the sample are compensated, establishing the desired lab-like conditions during the point-wise acquired 3D measurement. The high-precision positioning capability of the MP is used to precisely move the CCS' measurement spot across the sample surface in a tailored scan pattern. The robotic 3D measurement system provides a lateral measurement area of 4 x 4mm2 and achieves a lateral and axial resolution of 5 mu m and 130 nm, respectively. In addition, high-precision 3D measurements of arbitrary regions of interest can be performed. Experimental results of a commercial lenslet array demonstrate, that the robotic sample-tracking system increases the double-sided surface and thickness measurement performance by at least one order of magnitude compared to the state-of-the-art approach, while enabling the detection of surface defects on the sub-micrometer scale.

【文献 114】
作者: Niu, SF
来源: 2024 3RD INTERNATIONAL CONFERENCE ON ENERGY AND ELECTRICAL POWER
题目: Machine vision based autonomous cruise inspection vehicle hole
摘要: A machine vision based autonomous cruise inspection car hole recognition and positioning system is designed to address the issues of low operating efficiency, high labor intensity, and potential safety accidents in high-voltage switchgear maintenance cars. Using a visual system to take photos of the fixed holes on the switchgear, image filtering, hole processing, circular fitting, and other processing are performed on the collected images to obtain the position information of the holes. Combined with the algorithm for calculating the angle between the positioning holes on the switchgear and the algorithm for coordinate positioning of the holes, the rotation angle and displacement of the motor control system are obtained. The experiment was conducted using a circular hole with a diameter of 8mm, and the results showed that the accuracy of hole alignment and positioning reached 100%, which can meet the automated detection requirements of hole recognition and alignment for maintenance vehicles.

【文献 115】
作者: Terres, VD
来源: IEEE ROBOTICS AND AUTOMATION LETTERS
题目: Enhanced Optical Tracking of Weld Beads in Autonomous Inspection of
摘要: Inspection robots have been developed to support the maintenance of separator vessels. One challenge for such robots is to identify and navigate along the weld bead. This paper proposes a new solution to the weld bead recognition problem, including its tracking, which aims to automate weld bead inspection. A reliable weld bead detection method was developed through five steps that process the line profile sensor data. The weld bead was identified based on the estimation of its center. A non-linear controller paired with a module that compensates for uncertainties generated by gravity was designed to ensure the tracking of weld bead. Such a controller can reduce the impact of the variation in the robot's orientation on the weld bead tracking. An algorithm redirects the robot at the end of the weld bead. The experiments were conducted in a laboratory-scale setup, allowing movement along horizontal and diagonal surfaces. The results show that the robot tracked the weld line in all simulated conditions, with a maximum error of 2.86 mm.

【文献 116】
作者: Wang, HX
来源: APPLIED SCIENCES-BASEL
题目: Path Planning of Inspection Robot Based on Improved Ant Colony Algorithm
摘要: The conventional Ant Colony Optimization (ACO) algorithm, applied to logistics robot path planning in a two-dimensional grid environment, encounters several challenges: slow convergence rate, susceptibility to local optima, and an excessive number of turning points in the planned paths. To address these limitations, an improved ant colony algorithm has been developed. First, the heuristic function is enhanced by incorporating artificial potential field (APF) attraction, which introduces the influence of the target point's attraction on the heuristic function. This modification accelerates convergence and improves the optimization performance of the algorithm. Second, an additional pheromone increment, calculated from the difference in pheromone levels between the best and worst paths of the previous generation, is introduced during the pheromone update process. This adjustment adaptively enhances the path length optimality. Lastly, a triangle pruning method is applied to eliminate unnecessary turning points, reducing the number of turns the logistics robot must execute and ensuring a more direct and efficient path. To validate the effectiveness of the improved algorithm, extensive simulation experiments were conducted in two grid-based environments of varying complexity. Several performance indicators were utilized to compare the conventional ACO algorithm, a previously improved version, and the newly proposed algorithm. MATLAB simulation results demonstrated that the improved ant colony algorithm significantly outperforms the other methods in terms of path length, number of iterations, and the reduction of inflection points, confirming its superiority in logistics robot path planning.

【文献 117】
作者: Lyu, GZ
来源: INDUSTRIAL ROBOT-THE INTERNATIONAL JOURNAL OF ROBOTICS RESEARCH AND
题目: A heavy-load wall-climbing robot for bridge concrete structures
摘要: PurposeThe purpose of this paper is to present a wall-climbing robot platform for heavy-load with negative pressure adsorption, which could be equipped with a six-degree of freedom (DOF) collaborative robot (Cobot) and detection device for inspecting the overwater part of concrete bridge towers/piers for large bridges.Design/methodology/approachBy analyzing the shortcomings of existing wall-climbing robots in detecting concrete structures, a wall-climbing mobile manipulator (WCMM), which could be compatible with various detection devices, is proposed for detecting the concrete towers/piers of the Hong Kong-Zhuhai-Macao Bridge. The factors affecting the load capacity are obtained by analyzing the antislip and antioverturning conditions of the wall-climbing robot platform on the wall surface. Design strategies for each part of the structure of the wall-climbing robot are provided based on the influencing factors. By deriving the equivalent adsorption force equation, analyzed the influencing factors of equivalent adsorption force and provided schemes that could enhance the load capacity of the wall-climbing robot.FindingsThe adsorption test verifies the maximum negative pressure that the fan module could provide to the adsorption chamber. The load capacity test verifies it is feasible to achieve the expected bearing requirements of the wall-climbing robot. The motion tests prove that the developed climbing robot vehicle could move freely on the surface of the concrete structure after being equipped with a six-DOF Cobot.Practical implicationsThe development of the heavy-load wall-climbing robot enables the Cobot to be installed and equipped on the wall-climbing robot, forming the WCMM, making them compatible with carrying various devices and expanding the application of the wall-climbing robot.Originality/valueA heavy-load wall-climbing robot using negative pressure adsorption has been developed. The wall-climbing robot platform could carry a six-DOF Cobot, making it compatible with various detection devices for the inspection of concrete structures of large bridges. The WCMM could be expanded to detect the concretes with similar structures. The research and development process of the heavy-load wall-climbing robot could inspire the design of other negative-pressure wall-climbing robots.

【文献 118】
作者: Wang, YM
来源: ADVANCES IN MECHANICAL ENGINEERING
题目: Design and implementation of the wheel-clamping stay cable inspection
摘要: A new wheel-clamping type inspection robot for bridge stay cables was designed. Its clamping mechanism adopts a four-auxiliary-two-drive wheel clamping scheme, and the driving unit utilizes a single motor with double output shaft. A simple automatic control system of the robot was designed based on Arduino. Then, the diameter range of the stay cable that the robot can hold was calculated. The mechanical model of the robot under clamping condition was established. The curves for the minimum thrust F-e and driving force F required by the robot under different stay cable diameters Phi and inclined angles gamma were obtained through Matlab data processing. Based on Adams dynamic simulation, the appropriate shape and material of the wheel, the optimal position of the centroid distribution and how to improve the wind resistance of the wheel were determined. Finally, a prototype robot was developed and a climbing experiment was carried out. The results show that the inspection robot is easy to clamp, simple to operate and control, and the detection speed is 0-5m/min. The robot can grab stay cables with diameters ranging from 70 to 245mm and can be used for stay cables with angles ranging from 0 degrees to 90 degrees.

【文献 119】
作者: Blanco, K
来源: 2024 7TH IBERIAN ROBOTICS CONFERENCE, ROBOT 2024
题目: Soft bellow-based 3D printed robot for in-pipe inspection applications
摘要: In-pipe inspection frequently involves toxic chemicals, fluids, and spaces that are inaccessible to human operators. To ensure the well-being of workers in this sector, various approaches have been explored, most notably the use of rigid robotics. These robots use rigid elements linked together, along with multiple actuators, to achieve movement. However, the main drawback of rigid robotics is their difficulty in adapting to changing, unstructured environments. However, in the past decade, the use of soft robots, which employ soft materials as the basis for their operation, has begun to be studied for these types of tasks. The use of these materials in the robot's actuators offers significant adaptability, enabling more efficient movement and performance in unstructured environments. Additionally, the soft actuators allow the robot to navigate irregular surfaces, overcome small obstacles, and change direction within pipes without the need for extra actuation systems.

【文献 120】
作者: Horton, LM
来源: 18TH ANNUAL IEEE INTERNATIONAL SYSTEMS CONFERENCE, SYSCON 2024
题目: A Framework for Autonomous Inspection of Bridge Infrastructure using
摘要: Traditional bridge inspections are labor-intensive, time-consuming, and costly, relying on human inspectors for close visual and physical examinations, making them subjective, inaccurate, and non-repetitive. Leveraging Uncrewed Aerial Vehicles (UAVs) for UAV-enabled bridge inspection (UBI) has the potential to save over 50% of costs compared to traditional methods. However, existing UBI research lacks a unified, end-to end autonomous solution, often presenting isolated automation solutions at each stage of the inspection process. In this work, a comprehensive framework for autonomous bridge inspection using a single UAV is presented, encompassing mission planning, data acquisition, data analysis, and decision-making. Bridges were chosen as the focus of the framework due to their mandated inspection requirements compared to other infrastructure assets. We define the UBI system architecture, evaluate specific methods for each system element, and finally present how the UBI can be achieved through a phased procedure. Using physical experiments, the proposed procedure is validated with low-cost off-the-shelf hardware and software components, which demonstrates not only the feasibility but also the simplicity of the proposed framework using currently available technology. This research offers a comprehensive solution to revolutionize bridge maintenance, improve safety, reduce expenses, and streamline the inspection process for the longevity of critical transportation infrastructure.

【文献 121】
作者: Sehgal, A
来源: IEEE ROBOTICS AND AUTOMATION LETTERS
题目: WireFlie: A Novel Obstacle-Overcoming Mechanism for Autonomous
摘要: Robotic inspection of transmission lines presents significant challenges due to the complexity of navigating along the wires. Existing systems often rely on either flight modes for visual inspection or articulated crawling mechanisms for contact-based inspection. However, these approaches face limitations in effectively bypassing in-line obstacles or pylons, which are common in transmission line environments. This letter presents WireFlie, a novel hybrid robotic system that integrates rolling and flight modes to overcome these challenges. The system consists of a pair of underactuated arms mounted on a drone platform, designed for secure, collision-free locking and detaching, enabling seamless transitions between locomotion modes. WireFlie supports both single-arm and dual-arm rolling, allowing it to bypass in-line obstacles such as Stockbridge dampers, dual spacers, and sleeves, and to overcome larger obstacles like pylons using flight. Additionally, we propose a high-level controller for autonomous locking, detaching, and obstacle avoidance. Experiments are conducted on a custom-made setup that closely resembles a transmission wire. We evaluate both the design and control aspects of our system, with results including kinematic analysis, wire detection, autonomous locking and the corresponding trajectory, and obstacle detection and avoidance strategies. This research contributes to the field of robotic infrastructure inspection by merging aerial and wire-based locomotion, providing efficient and autonomous monitoring of power lines.

【文献 122】
作者: Jia, ZX
来源: PROCEEDINGS OF THE 36TH CHINESE CONTROL AND DECISION CONFERENCE, CCDC
题目: Design and Validation of Pipeline Inspection Robot based on Panoramic
摘要: Pipeline-type workpieces are prevalent in industrial production. However, manually inspecting the inner walls is an inefficient quality control activity that may pose safety risks to inspection personnel, especially for specialized workpieces with internal ramps. The complex internal structure further increases the difficulty of manual inspection. Therefore, designing a robot capable of navigating inside tube-shaped workpieces and stitching images of the inner walls into a single composite image is crucial for improving the efficiency of quality control processes for such workpieces. To meet this demand, we have designed a robot as an experimental platform capable of traveling and climbing ramps within pipes. The robot can handle a maximum climbing angle of 30 degrees and is equipped with four 180-degree wideangle cameras arranged at 90-degree intervals, facilitating the capture of images around the pipe. Addressing the challenge of narrow environments inside pipes where conventional stitching algorithms may not be effectively applied, we have designed a stitching method capable of seamlessly stitching larger disparity images in narrow pipe environments. This algorithm optimizes the processes of feature point matching and optimal seam line selection, achieving higher-quality image stitching from the four cameras. The robot we designed will significantly enhance the efficiency of inspecting the inner walls of tube-shaped workpieces, particularly those with ramps.

【文献 123】
作者: Nodehi, SE
来源: ROBOTICA
题目: Porcospino, spined single-track mobile robot for inspection of narrow
摘要: This paper discusses the design and the experimental tests on Porcospino, a bio-inspired single-track mobile robot for inspection of unstructured environments characterized by narrow spaces. It is an evolution of SnakeTrack, a single-track robot with steering capabilities; differently from SnakeTrack, the track modules of Porcospino are characterized by elastic spines, which improve traction on uneven and irregular terrains. The main body is a vertebral column, comprising a series of vertebrae connected by compliant joints and two end modules. Each end module carries two sprockets, sharing a common actuator, to drive the single peripherical track. Moreover, each end module hosts an actuator for track steering. The remaining mobilities of the vertebral column allow it to cope passively with the terrain profile, to enhance traction. The control unit, batteries, drivers, and environmental sensors are placed along the vertebral column. Both the end modules are equipped with a camera for intermittent vision, which is possible thanks to openings realized on the track modules. The experimental campaign on the first Porcospino prototype is discussed, highlighting the differences with its earlier version.

【文献 124】
作者: Chakraa, H
来源: TRANSACTIONS OF THE INSTITUTE OF MEASUREMENT AND CONTROL
题目: Integrating collision avoidance strategies into multi-robot task
摘要: Recent research topics have been placed on reinforcing security and safety measures within high-risk industries to protect both equipment and the environment. Numerous industries carry substantial implications for their surroundings. During significant incidents like chemical spills, or nuclear accidents, swiftly gathering precise and dynamic data poses a considerable challenge. Subsequently, this paper focuses on optimizing a mission involving multiple mobile robots charged with inspecting an industrial zone. The aim of this research is to efficiently collect measurements from diverse positions using a fleet of sensing robots operating from a central depot, that is, developing an algorithm for robot decision-making that optimizes mission planning by minimizing an objective function. Initially, we explore our previous proposed solutions and we improve the system by integrating a navigational layer to manage collision avoidance between robots. Then, we delve into scenarios involving multiple homogeneous tasks distributed in a limited geographical environment. To demonstrate feasibility, extensive simulations, numerical experiments, and comparative analysis are conducted, showing the efficiency of the proposed approaches in terms of solution quality and computational complexity.

【文献 125】
作者: Li, JW
来源: AUTOMATION IN CONSTRUCTION
题目: Motion planning for a quadruped robot in heat transfer tube inspection
摘要: Steam generators (SGs) are essential in nuclear power facilities and require regular inspection to maintain their safety and operational effectiveness. This paper presents a quadruped robot designed to inspect SG heat- transfer tubes. The point-to-point crawling-motion planning problem of the robot is addressed by integrating an improved A* algorithm with an offline motion-posture library established for the tube-sheet environment. The planner can provide the global path, the step size for each crawling cycle, and the strategic placement of footholds. The proposed method can facilitate the robot in navigating obstacles on a tube sheet, seamlessly adapt to various postures and step lengths, and eliminate the necessity for turning or superfluous posture adjustments, thereby ensuring crawling efficiency. The efficacy of the proposed planner is validated rigorously through simulations and experimental trials using an actual SG tube sheet.

【文献 126】
作者: Jeon, KW
来源: INTELLIGENT AUTONOMOUS SYSTEMS 18, VOL 1, IAS18-2023
题目: Development of In-Pipe Inspection Robot for Large-Diameter Water Pipe
摘要: This paper describes the development of in-pipe inspection robot system for large-diameter water pipe with magnetic flux leakage (MFL) sensor module. The target pipe diameter range of the in-pipe inspection robot system is 900-1200 mm. The composition of the in-pipe inspection robot is the front and rear driving part and the centrally located inspection part. The in-pipe inspection robot described in this paper was developed in a form equipped with a wireless communication module and battery. The developed in-pipe inspection robot was tested on a test-bed with a diameter of 900-1200 mm and a length of 16.5 m. As a result of the driving test, it was confirmed that the in-pipe inspection robot could drive along the center of the pipe despite the slope of 22.5 and various types of diameter changes. It is considered that it is possible to reduce economic costs and secure the safety of inspectors when inspecting old pipes through the developed in-pipe inspection robot.

【文献 127】
作者: Chao, CX
来源: INDUSTRIAL ROBOT-THE INTERNATIONAL JOURNAL OF ROBOTICS RESEARCH AND
题目: A balanced walking-clamp mechanism for inspection robot of transmission
摘要: PurposeThis paper aims to design a walking-clamp mechanism for the inspection robot of transmission line. The focus for this design is on climbing ability and obstacle-crossing ability with a goal to create a novel walking-clamp mechanism that can clamp not only the line but also the obstacle. Design/methodology/approachA novel clamping jaw used in the walking-clamp mechanism is proposed. The clamping wheel is mounted on the lower end of clamping jaw to reduce the friction between the clamping jaw and the line, and the top end of clamping jaw is designed as a hook structure to clamp the obstacle. The working principle and force states of the walking-clamp mechanism clamping the line and obstacle are analyzed, and the simulation and prototype experiments are carried out. FindingsThe experimental results show that this mechanism can clamp the obstacle steadily, and the clamping forces of the front and back pairs of clamping jaws are almost equal during robot walking along the catenary-shaped line. It is in agreement with the theoretical analysis, and it demonstrates that this mechanism can meet the working requirements of inspection robot. Practical implicationsThis novel mechanism can be used for inspection robot of transmission line, and it is beneficial for robot to complete long-distance inspection works. Social implicationsIt stands to reduce costs related to inspection and improve the inspection efficiency. Originality/valueInnovative features include its structure, working principle and force states.

【文献 128】
作者: Cox, A
来源: IEEE-ASME TRANSACTIONS ON MECHATRONICS
题目: Design and Static Equilibrium Analysis of a Modular Pipe Inspection
摘要: Inspection of tube networks is an important process in the manufacturing and maintenance of aircraft. Currently, borescopes are commonly used with limited success. To improve the inspection process, a prototype design based on a vertebra concept is introduced, analyzed, and optimized. The modular robotic inspection system (MoRIS) allows for a microsized camera to be driven noninvasively through a 3-m-long, 36-mm internal diameter tube network. MoRIS provides a complete view of the tube walls. Our system includes a localization algorithm that tracks the progress of the robot inside the tubes. This algorithm results in an experimentally validated error of 19.7 mm. Using MoRIS, operators can inspect tube networks that previously required hazardous disassembly of aircraft.

【文献 129】
作者: Lyu, Y
来源: IEEE TRANSACTIONS ON SYSTEMS MAN CYBERNETICS-SYSTEMS
题目: Vision-Based Plane Estimation and Following for Building Inspection With
摘要: In this article, we focus on enabling the autonomous perception and control of a small unmanned aerial vehicle (UAV) for a facade inspection task. Specifically, we consider the perception as a planar object pose estimation problem by simplifying the building structure as a concatenation of planes, and the control as an optimal reference tracking control problem. First, a vision-based adaptive observer is proposed for plane pose estimation which converges fast and is insensitive to noise under very mild observation conditions. Second, a model predictive controller (MPC) is designed to achieve stable plane following and smooth transition in a multiple-plane scenario, while the persistent excitation (PE) condition of the observer and the maneuver constraints of the UAV are satisfied. The stability of the observer and the MPC controller is also investigated to ensure theoretical completeness. The proposed autonomous plane pose estimation and plane tracking methods are tested in both simulation and practical building facade inspection scenarios, which demonstrate their effectiveness and practicability.

【文献 130】
作者: Huang, XY
来源: FRONTIERS IN ENERGY RESEARCH
题目: Like-attracts-like optimizer-based video robotics clustering control
摘要: A new meta-heuristic algorithm called like-attracts-like optimizer (LALO) is proposed in this article. It is inspired by the fact that an excellent person (i.e., a high-quality solution) easily attracts like-minded people to approach him or her. This LALO algorithm is an important inspiration for video robotics cluster control. First, the searching individuals are dynamically divided into multiple clusters by a growing neural gas network according to their positions, in which the topological relations between different clusters can also be determined. Second, each individual will approach a better individual from its superordinate cluster and the adjacent clusters. The performance of LALO is evaluated based on unimodal benchmark functions compared with various well-known meta-heuristic algorithms, which reveals that it is competitive for some optimizations.

【文献 131】
作者: Hong, KL
来源: BUILDINGS
题目: Inspection-Nerf: Rendering Multi-Type Local Images for Dam Surface
摘要: For the surface defects inspection task, operators need to check the defect in local detail images by specifying the location, which only the global 3D model reconstruction can't satisfy. We explore how to address multi-type (original image, semantic image, and depth image) local detail image synthesis and environment data storage by introducing the advanced neural radiance field (Nerf) method. We use a wall-climbing robot to collect surface RGB-D images, generate the 3D global model and its bounding box, and make the bounding box correspond to the Nerf implicit bound. After this, we proposed the Inspection-Nerf model to make Nerf more suitable for our near view and big surface scene. Our model use hash to encode 3D position and two separate branches to render semantic and color images. And combine the two branches' sigma values as density to render depth images. Experiments show that our model can render high-quality multi-type images at testing viewpoints. The average peak signal-to-noise ratio (PSNR) equals 33.99, and the average depth error in a limited range (2.5 m) equals 0.027 m. Only labeled 2% images of 2568 collected images, our model can generate semantic masks for all images with 0.957 average recall. It can also compensate for the difficulty of manual labeling through multi-frame fusion. Our model size is 388 MB and can synthesize original and depth images of trajectory viewpoints within about 200 m(2) dam surface range and extra defect semantic masks.

【文献 132】
作者: Liu, HS
来源: IEEE TRANSACTIONS ON INDUSTRIAL INFORMATICS
题目: UAV Trajectory Planning via Viewpoint Resampling for Autonomous Remote
摘要: The autonomous remote inspection method based on unmanned aerial vehicle (UAV) has potential benefits in solving the safety inspection problem of large-scale industrial facilities. However, the low coverage rate caused by unsatisfactory trajectory quality is the main challenge of autonomous inspection operations. Therefore, in order to effectively optimize the trajectory quality of UAV, in this article, a mathematical model for trajectory planning considering UAV energy consumption, mapping efficiency, and target structure coverage is established while respecting various constraints related to hardware limitations of visual sensors and UAVs. To solve the above model to achieve autonomous inspection, a two-stage heuristic algorithm is designed, aiming to optimize a set of viewpoints for maximizing coverage of the target structure and reducing energy and time consumption. Finally, computational experiments were conducted based on three real industrial scenarios, proving that the model with the proposed algorithm in this study outperforms other advanced methods.

【文献 133】
作者: Matlekovic, L
来源: DRONES
题目: Constraint Programming Approach to Coverage-Path Planning for Autonomous
摘要: This article presents a constraint modeling approach to global coverage-path planning for linear-infrastructure inspection using multiple autonomous UAVs. The problem is mathematically formulated as a variant of the Min-Max K-Chinese Postman Problem (MM K-CPP) with multi-weight edges. A high-level constraint programming language is used to model the problem, which enables model execution with different third-party solvers. The optimal solutions are obtained in a reasonable time for most of the tested instances and different numbers of vehicles involved in the inspection. For some graphs with multi-weight edges, a time limit is applied, as the problem is NP-hard and the computation time increases exponentially. Despite that, the final total inspection cost proved to be lower when compared with the solution obtained for the unrestricted MM K-CPP with single-weight edges. This model can be applied to plan coverage paths for linear-infrastructure inspection, resulting in a minimal total inspection time for relatively simple graphs that resemble real transmission networks. For more extensive graphs, it is possible to obtain valid solutions in a reasonable time, but optimality cannot be guaranteed. For future improvements, further optimization could be considered, or different models could be developed, possibly involving artificial neural networks.

【文献 134】
作者: Ferri, G
来源: OCEANS 2024 - SINGAPORE
题目: RAMI23: an Inspection & Maintenance Robotics Challenge to Form the New
摘要: We describe the Robotics for Asset Maintenance and Inspection (RAMI), a competition focused on Inspection and Maintenance (I&M) tasks for aerial and underwater robots. RAMI is one of the four challenge-led robotic competitions organised in the framework of the Metrological Evaluation and Testing of Robots in International CompetitionS (METRICS) European project, and is focused on the Inspection and Maintenance (I&M) domain. In this work, we describe the RAMI23 competition dedicated to autonomous underwater vehicles (AUVs). It was organised by the NATO STO Centre for Maritime Research and Experimentation (CMRE) at La Spezia, Italy, in its protected seawater basin, from 16 to 21 July 2023. RAMI23 proposed AUVs missions set in an Oil&Gas mock-up scenario, characterised by realistic conditions. Advanced perception and autonomy were required to succeed in the proposed tasks. These feautures are highly required in real-world I&M missions to increase the mission performance, especially considering the typical difficult communication conditions, which make a direct link between operator and robot unreliable.

【文献 135】
作者: Zhang, XH
来源: IEEE ACCESS
题目: Path Planning of Inspection Robot Based on Improved Intelligent Water
摘要: In response to the limitations observed in the Intelligent Water Droplet (IWD) algorithm for path planning, including its weak problem-solving capability and susceptibility to local optimization, this paper presents an enhanced Intelligent Water Droplet algorithm. The improved algorithm incorporates the distance factor between nodes and the target point into the original algorithm's path probability, enhancing its problem-solving prowess and expediting algorithmic convergence. Simultaneously, a roulette-based probability selection method is introduced to circumvent local optimization during the solution process. Additionally, the algorithm is coupled with the Floyd algorithm to refine the planned path, reducing the number of inflection points to align with the motion characteristics of the inspection robot. Simulation results underscore the effectiveness of the enhanced Intelligent Water Droplet algorithm in mitigating the impact of local optimization in path planning. In comparison to the original IWD algorithm, the optimized path exhibits a 17.42% reduction in length, a 58.3% decrease in the minimum number of iterations required for path convergence, and a 36.3% reduction in the number of path inflection points. Furthermore, the total path length is decreased by 7.7% following path optimization via the Floyd algorithm.

【文献 136】
作者: Lu, ZQ
来源: JOURNAL OF MARINE SCIENCE AND ENGINEERING
题目: Design and Testing of an Autonomous Navigation Unmanned Surface Vehicle
摘要: In response to the inefficiencies and high costs associated with manual buoy inspection, this paper presents the design and testing of an Autonomous Navigation Unmanned Surface Vehicle (USV) tailored for this purpose. The research is structured into three main components: Firstly, the hardware framework and communication system of the USV are detailed, incorporating the Robot Operating System (ROS) and additional nodes to meet practical requirements. Furthermore, a buoy tracking system utilizing the Kernelized Correlation Filter (KCF) algorithm is introduced. Secondly, buoy image training is conducted using the YOLOv7 object detection algorithm, establishing a robust model for accurate buoy state recognition. Finally, an improved Line-of-Sight (LOS) method for USV path tracking, assuming the presence of an attraction potential field around the inspected buoy, is proposed to enable a comprehensive 360-degree inspection. Experimental testing includes validation of buoy image target tracking and detection, assessment of USV autonomous navigation and obstacle avoidance capabilities, and evaluation of the enhanced LOS path tracking algorithm. The results demonstrate the USV's efficacy in conducting practical buoy inspection missions. This research contributes insights and advancements to the fields of maritime patrol and routine buoy inspections.

【文献 137】
作者: Tse, KW
来源: SENSORS
题目: A Novel Real-Time Autonomous Crack Inspection System Based on Unmanned
摘要: Traditional methods on crack inspection for large infrastructures require a number of structural health inspection devices and instruments. They usually use the signal changes caused by physical deformations from cracks to detect the cracks, which is time-consuming and cost-ineffective. In this work, we propose a novel real-time crack inspection system based on unmanned aerial vehicles for real-world applications. The proposed system successfully detects and classifies various types of cracks. It can accurately find the crack positions in the world coordinate system. Our detector is based on an improved YOLOv4 with an attention module, which produces 90.02% mean average precision (mAP) and outperforms the YOLOv4-original by 5.23% in terms of mAP. The proposed system is low-cost and lightweight. Moreover, it is not restricted by navigation trajectories. The experimental results demonstrate the robustness and effectiveness of our system in real-world crack inspection tasks.

【文献 138】
作者: Peng, X
来源: EUROPEAN WORKSHOP ON STRUCTURAL HEALTH MONITORING (EWSHM 2022), VOL 2
题目: A Virtual Reality Environment for Developing and Testing Autonomous
摘要: Unmanned aerial vehicles (UAVs) equipped with imaging and laser sensors have shown their benefits in structural health inspection due to their aerial mobility, low cost, and efficiency. However, UAV applications in practice are limited by their level of automation, and man-piloted operation dominates to date. With vehicle automation on the horizon, autonomous structural inspection systems via robotic vehicles have become a possibility. Nonetheless, significant challenges exist for testing and validating in a physical environment. This paper proposes a virtual reality framework for developing autonomous UAV-based structural health inspection systems. The framework, built atop a gaming engine, implements algorithmic virtual sensing and control of a UAV that flies virtually in a complex built environment. In this paper, we test this framework with a virtual UAV with an open-loop control approach for structural health inspection, including waypoint-based control and simultaneous localization and mapping. We further discuss its full potential as an aerial robotics learning and validation platform for developing advanced data-enabled structural-space exploration, optimal control, and damage assessment.

【文献 139】
作者: Chen, R
来源: ADVANCED INTELLIGENT SYSTEMS
题目: A Motion-Sensing Integrated Soft Robot with Triboelectric Nanogenerator
摘要: The challenging and unstructured environment within pipelines demands the robotic exploration platforms with high adaptability, maneuverability, and recognition ability. Current soft robots equipped with cutting-edge actuators have demonstrated inherent benefits in navigating pipeline environments due to their material compliance and morphological adaptability. However, achieving inner-pipe detection for pipeline-climbing robots challenges the integration of sensors without compromising the robot's flexibility and operational functionalities. Herein, a soft robot that locomotes within pipelines and performs exteroception is presented. The main body of the robot is fabricated based on origami designs, powered by pneumatic actuators for locomotion and incorporates triboelectric nanogenerators as tactile sensors (T-TENGs). Physical experiments have demonstrated the soft robot's capacity in crawling in various pipeline conditions such as the horizontal, vertical, and curved configurations. The T-TENG-based sensory system outputs distinct voltage signals upon exposed to different material and structural conditions, for which a 1D-convolutional neutral network algorithm is exposed to process with the sequential signals. The robot achieves an overall recognition accuracy of 99% for distinguishing between eight distinct pipe inner surface structures and four different types of materials.

【文献 140】
作者: Qin, YF
来源: PROCEEDINGS OF TEPEN 2022
题目: A Study of Semantic Map Construction Methods for Power Plant Inspection
摘要: The power inspection robots in power plants is widely used in recent years, but the power inspection robots for transformer substation can not use in power plant directly because of the complex environment in power plant. In response to the needs of power plant power inspection robots, this paper investigates the localization and navigation techniques of mobile robots in the power plant environment. Target detection is performed byYOLOv3-tiny on visual image information, and the information obtained from both is then calibrated and fused by clustering LiDAR data to obtain semantic laser information, and the feasibility of the algorithm is verified through experiments to achieve target localization in the working scenario.

【文献 141】
作者: Derakhshan, S
来源: INTERNATIONAL JOURNAL OF HUMAN-COMPUTER INTERACTION
题目: A Situated Inspection of Autonomous Vehicle Acceptance - A Population
摘要: The successful integration of Autonomous Vehicles (AVs) into daily life is influenced by various individual and contextual factors. This study examines key factors affecting acceptance and trust in AVs by assessing how interactive experiences influence user perceptions and adoption. Utilizing a Virtual Reality (VR) environment to simulate real-world driving conditions, the research investigates the impact of different levels of vehicle interaction-manual control, semi-autonomous, fully autonomous, and taxi-riding modes-on user and passenger trust, anxiety, perceived ease of use, and overall acceptance. A dual-method approach was employed, combining objective data collection through eye-tracking, which measures engagement and vigilance, with subjective user feedback gathered via the Autonomous Vehicle Acceptance Model (AVAM) questionnaire. The findings reveal a complex relationship between user control and trust in AV systems. While semi-autonomous conditions that incorporate user participation in decision-making enhance trust and intention to use, fully autonomous conditions influence trust but not intention to use significantly. However, semi- autonomous conditions also elicited higher anxiety levels compared to fully autonomous modes, suggesting a trade-off between interaction and comfort. Engagement levels, as indicated by visual metrics such as gaze patterns, were found to be critical for understanding user acceptance. These results highlight the importance of considering both subjective perceptions and objective behaviors in developing AV interfaces. The study contributes significant insights into the cognitive underpinnings of AV acceptance and underscores the need for strategies that both enhance user trust and minimize anxiety by finding an optimal balance between automation and user control. Further research into this balance is recommended to better accommodate the evolving nature of user trust and the cognitive complexities associated with semi-autonomous systems.

【文献 142】
作者: Hong, L
来源: IEEE TRANSACTIONS ON INTELLIGENT VEHICLES
题目: Vision-Based Underwater Inspection With Portable Autonomous Underwater
摘要: This paper presents the development of a portable autonomous underwater vehicle (AUV) named Shark, along with the design of its hovering control system for vision-based underwater inspection. Firstly, the mechanical structure design, electrical system configuration, and software system development of the Shark AUV are introduced, highlighting its cost-effectiveness, compact size, and streamlined torpedo shape to enhance the flexibility of underwater inspections. Secondly, the dynamic model of the Shark AUV is established, and the hydrodynamic coefficients are numerical estimated utilizing computational fluid dynamics (CFD) simulations. Thirdly, considering that the developed Shark AUV is underactuated in the sway degree of freedom, a novel heading control model is developed that reasonably tackles the motion coupling between sway and yaw. This model facilitates the design of a robust H-infinity controller to regulate the heading angle of the Shark AUV for vision-based underwater inspections. Finally, the effectiveness of the designed hovering control method and vision-based underwater inspection system are validated in both a 3D simulation platform and a laboratory test pool. The experimental results demonstrate that the Shark AUV prototype can achieve satisfactory vision-based underwater inspection performance within a pixel error of 5 x 5, and the proposed H-infinity heading controller consistently provides superior performance in handling motion coupling, parametric uncertainty, and external disturbances.

【文献 143】
作者: Tosello, E
来源: IEEE ROBOTICS & AUTOMATION MAGAZINE
题目: Opportunistic (Re)planning for Long-Term Deep-Ocean Inspection An
摘要: Robots are increasingly used in subsea environments because of their positive impact on human safety and operational capabilities in the deep ocean. However, achieving full autonomy remains challenging because of the extreme conditions they encounter. In this context, we propose an autonomous underwater architecture for long-term deep-ocean inspection that robustly plans activities and efficiently deliberates with no human help. It combines the innovative Saipem's Hydrone-R subsea vehicle with an advanced planning architecture, resulting in a robot that autonomously perceives its surroundings, plans a mission, and adapts in real time to contingencies and opportunities. After describing the robot hardware, we present the technological advancements achieved in building its software, along with several compelling use cases. We explore scenarios where the robot conducts long-term underwater missions operating under resource constraints while remaining responsive to opportunities, such as new inspection points. Our solution gained significant reliability and acceptance within the oil and gas community as evidenced by its current deployment on a real field in Norway.

【文献 144】
作者: Wang, C
来源: INDUSTRIAL ROBOT-THE INTERNATIONAL JOURNAL OF ROBOTICS RESEARCH AND
题目: Design and experiment of climbing robot for steel I-beam girder
摘要: PurposeRegular inspection of steel I-beam girders is crucial to ensure structural safety. This study aims to develop a four-wheeled climbing robot capable of traversing typical obstacles on girders, including 20-mm high steps, concave and convex corners and 10-mm thick edges.Design/methodology/approachThe robot features a configuration adjustment mechanism, consisting of lead screws and linkages, which allows the wheelbase to adjust for traversing various obstacles. The authors analyze the mechanical and geometric constraints necessary for the robot to traverse these obstacles. Finally, the robot's feasibility is validated through laboratory and field tests.FindingsThe experimental results show that the robot achieves a load capacity exceeding 5 kg and successfully traverses the obstacles mentioned above. Meanwhile, the results confirm the validity of the proposed mechanical and geometric constraints.Originality/valueCompared to previous studies, the robot can traverse a wider variety of obstacle types and sizes. The authors introduced a steering mechanism that combines multi-joint steering with differential steering, enhancing the four-wheeled robot's steering flexibility. The proposed analysis of mechanical and geometric constraints provides theoretical support for the future design of climbing robots.

【文献 145】
作者: Li, XZ
来源: 2023 62ND ANNUAL CONFERENCE OF THE SOCIETY OF INSTRUMENT AND CONTROL
题目: Design of Wheel Mechanism of Inspection Mobile Robot Using Electro
摘要: In recent years, civil engineering and construction structures such as bridges and tunnels are aging, and periodic inspections are becoming necessary. Therefore, robotics is expected to inspect bridges more efficiently and safely, and robots for inspection of civil engineering and construction structures are widely researched. In this study, we propose a combination of a propeller mechanism and an EPM (Electro Permanent Magnet) wheel to allow the robot to move on bridges of different materials. On concrete and other parts, the robot is driven by a propeller. When it detects that the bridge material is iron, the propeller is turned off and moved by the magnetic force of the EPM wheel.

【文献 146】
作者: Ma, WT
来源: ADVANCED INTELLIGENT SYSTEMS
题目: A Soft, Centimeter-Scaled, Thin-Cable-Crawling Robot for Narrow Space
摘要: Cables are critical in engineering structures for load-bearing, electronic connection, and mechanical transmission. Various cable-crawling robots (CCRs) have been developed to perform scheduled inspection or convey supplies. Most existing CCRs are often actuated by motors and used in large-scaled engineering structures. The heavy bodies of these CCRs can cause damage or even casualties once slippage or drop occurs. A small and lightweight CCR that can crawl on thin cables is highly demanded for safety inspection in narrow and confined inner spaces of engineering structures. Herein, a soft CCR (weight, 2.1 g; length, 43 mm) is developed by utilizing multilayered dielectric elastomer actuators. Compared with existing solutions, this CCR achieves crawling on thin cables (diameter: <1 mm) while crawling fastest (horizontal: 0.72 body length per second). The CCR is also capable of transporting objects (horizontal: 3.69 times its own weight; vertical: 0.76 times its own weight), climbing upward on a vertical cable, and locomoting across the water-air interface. The CCR is also demonstrated to crawl on a slack cable and circular/spiral cables. Finally, the soft robot, equipped with an endoscope, demonstrates inspections on a tensegrity structure as well as in an airplane wing model with a preplaced cable.

【文献 147】
作者: Tasneem, O
来源: 2024 IEEE 20TH INTERNATIONAL CONFERENCE ON AUTOMATION SCIENCE AND
题目: Automatic Robot Path Planning for Active Visual Inspection on Free-Form
摘要: Visual inspection is a crucial yet time-consuming task across various industries. Numerous established methods employ machine learning in inspection tasks, necessitating specific training data that includes predefined inspection poses and training images essential for the training of models. The acquisition of such data and their integration into an inspection framework is challenging due to the variety in objects and scenes involved and due to additional bottlenecks caused by the manual collection of training data by humans, thereby hindering the automation of visual inspection across diverse domains. This work proposes a solution for automatic path planning using a single depth camera mounted on a robot manipulator. Point clouds obtained from the depth images are processed and filtered to extract object profiles and transformed to inspection target paths for the robot end-effector. The approach relies on the geometry of the object and generates an inspection path that follows the shape normal to the surface. Depending on the object size and shape, inspection paths can be defined as single or multi-path plans. Results are demonstrated in both simulated and real-world environments, yielding promising inspection paths for objects with varying sizes and shapes. Code and video are open-source available at: https: //github.com/ CuriousLad1000/ Auto- Path-Planner

【文献 148】
作者: Lu, K
来源: 2024 IEEE 99TH VEHICULAR TECHNOLOGY CONFERENCE, VTC2024-SPRING
题目: Application of IPv6 protocol conformance test in 5G visual inspection
摘要: With the rapid development of AI technology, robots are being used more and more widely. This project uses artificial intelligence technology to develop a 5G based visual inspection robot system, introduces the inspection robot into the daily inspection and operation and maintenance work of the computer room, and explores and studies a set of fully autonomous visual operation robot system, including 5G control, real-time visualization, environmental monitoring, monitoring and obstacle avoidance. The system is tested for IPv6 protocol consistency, and the test results of terminal cameras are analyzed to improve the network security of the system.

【文献 149】
作者: Wei, L
来源: SENSORS
题目: Trajectory Tracking Control of Transformer Inspection Robot Using
摘要: To overcome the difficulty in tracking the trajectory of an inspection robot inside a transformer, this paper proposes a distributed model predictive control method. First, the kinematics and dynamics models of a robot in transformer oil are established based on the Lagrange equation. Then, by using the nonlinear model predictive control method and following the distributed control theory, the motion of a robot in transformer oil is decoupled into five independent subsystems. Based on this, a distributed model predictive control (DMPC) method is then developed. Finally, the simulation results indicate that a robot motion control system based on DMPC achieves high tracking accuracy and robustness with reduced computing complexity, and it provides an effective solution for the motion control of robots in narrow environments.

【文献 150】
作者: Wu, XW
来源: IEEE ACCESS
题目: Flexible Switching Control of Aircraft Skin Inspection Robot via
摘要: This article considers the flexible switching control problem of a two-frame aircraft skin inspection robot (TFASIR) with full-state time-varying constraints, input saturation, uncertainty, and unknown disturbance. Initially, this control problem is also treated as a tracking control problem of the dual-coupled adsorption system (DCAS). A novel nonlinear time-varying state-dependent function (NTVSDF) is first designed to tackle the full-state constraint problem. Subsequently, a feedforward tracking control method is designed, that uses the command-filtered backstepping technique, to transform the tracking control problem into an equivalent differential game problem (DGP) of closed-loop systems. Then, a zero-sum game strategy is presented, that uses the idea of adaptive dynamic programming (ADP) algorithm, to determine the DGP. The whole control method ensures that the closed-loop signals are uniformly ultimately bounded (UUB). Furthermore, another problem is that the partial system states are not accessible. To overcome this problem, a high-gain observer is utilized to reconstruct the state vector, and an output feedback controller is developed. The feasibility of the proposed control scheme is demonstrated in simulation.

【文献 151】
作者: Zhao, MH
来源: SENSORS
题目: Welding Seam Tracking and Inspection Robot Based on Improved YOLOv8s-Seg
摘要: A weld is the main connection form of special equipment, and a weld is also the most vulnerable part of special equipment. Therefore, an effective detection of a weld is of great significance to improve the safety of special equipment. The traditional inspection method is not only time-consuming and labor-intensive, but also expensive. The welding seam tracking and inspection robot can greatly improve the inspection efficiency and save on inspection costs. Therefore, this paper proposes a welding seam tracking and inspection robot based on YOLOv8s-seg. Firstly, the MobileNetV3 lightweight backbone network is used to replace the backbone part of YOLOv8s-seg to reduce the model parameters. Secondly, we reconstruct C2f and prune the number of output channels of the new building module C2fGhost. Finally, in order to make up for the precision loss caused by the lightweight model, we add an EMA attention mechanism after each detection layer in the neck part of the model. The experimental results show that the accuracy of weld recognition reaches 97.8%, and the model size is only 4.88 MB. The improved model is embedded in Jetson nano, a robot control system for seam tracking and detection, and TensorRT is used to accelerate the reasoning of the model. The total reasoning time from image segmentation to path fitting is only 54 ms, which meets the real-time requirements of the robot for seam tracking and detection, and realizes the path planning of the robot for inspecting the seam efficiently and accurately.

【文献 152】
作者: Ahmed, MF
来源: ELECTRIC POWER COMPONENTS AND SYSTEMS
题目: Autonomous Site Inspection of Power Transmission Line Insulators with
摘要: The inspection of overhead power transmission line and assets is an essential aspect to improve the overhead power transmission efficiency and to ensure an uninterrupted power supply. This article has mainly focused on the progression and fabrication of indigenous quadcopter/Unmanned Aerial Vehicle (UAV) for carrying autonomous operations in a coordinated movement along the overhead transmission towers for capturing the images and videos of transmission insulators and assets. A custom based dataset of power line insulators is created by using the quadcopter for overcoming the data scarcity and to perform Deep Learning (DL) assessment for (i) inadequate data for training and (ii) power line insulator detection and faults. The experimental results showcase that, the suggested DL architecture identifies power line insulators and associated faults, such as cracks, broken disk and missing top caps etc. With a detection speed of 56.8 frames/sec and an accuracy of 94.1%, the proposed DL technique has much promise for intelligent examination of power grid insulators. Ecological Footprint assessment of different power line inspection methods are also examined in this study.

【文献 153】
作者: Wibisono, A
来源: IEEE ACCESS
题目: An Autonomous Underwater Vehicle Navigation Technique for Inspection and
摘要: This article introduces an innovative approach to the navigation of autonomous underwater vehicles (AUVs) in inspection and data acquisition missions within underwater wireless sensor networks (UWSNs). We combine at least five underwater navigation techniques to accomplish this mission, adapted from related works. These five techniques are employed to address at least four main challenges identified in inspection and data acquisition missions in UWSNs involving the use of AUVs, namely: communication constraints, energy usage optimization, precise navigation, and effective data acquisition. Limited simulations conducted demonstrate the reliability of the proposed model. The model successfully navigates to the target point in 3D coordinates X Y Z, assuming the launch point as d0 (10 40 40), and reaches the q-goal target point (45 45 0) within 21 seconds, with the addition of uattractive and urepulsive (magnetic beacon attraction force and repulsion force as simulation of underwater current disturbance factor). Furthermore, in the inspection and data acquisition mission in UWSN simulated as node points (o) in pink, AUV (*) in blue effectively follows the predetermined points while acquiring data, as indicated by green lines (-) within just 5 seconds, achievable by increasing the value of alpha (angle of attack) of the target node to reduce delay time. The evaluation of the experimental simulations has raised issues and future research challenges, including the development of environmental simulation challenges that can closely resemble real conditions, the measurement of energy usage effectiveness to reach each target point, and the potential development of underwater recharging techniques. Furthermore, there is a need for advanced precise navigation and the advancement of effective data acquisition techniques.

【文献 154】
作者: Manduca, G
来源: PROCEEDINGS OF 2023 IEEE INTERNATIONAL WORKSHOP ON METROLOGY FOR
题目: Development of an Autonomous Fish-Inspired Robotic Platform for
摘要: Aquaculture applications are increasingly utilizing precision techniques such as computer vision technologies to perform a variety of inspection tasks. This work presents the development of three activities essential for the creation of a biomimetic robotic platform with onboard intelligence and autonomous task execution capabilities. The proposed robot is inspired by carangiform movement and achieves various trajectories through a magnetic actuation system with a single motor for propulsion. Fluid dynamics studies can improve the performance of the proposed propulsion system, thus ensuring greater energy efficiency. Thanks to its modular and scalable structure, the platform can integrate different components such as a vision system. The investigated vision-based model shows promising results for deployment in marine environments and can be adapted to detect various marine species. This fish-inspired robot platform has potential applications in the sustainable inspection and management of aquaculture facilities.

【文献 155】
作者: Tang, CQ
来源: APPLIED SCIENCES-BASEL
题目: Inspection Robot and Wall Surface Detection Method for Coal Mine Wind
摘要: The coal mine wind shaft is an important ventilation channel in coal mines, and it is of great significance to ensure its long-term safety. At present, the inspection of wind shafts still depends on manual work, which has low reliability and high risk. There are two main problems in the shaft wall detection of ventilation shafts: (1) The humidity and dust concentration in ventilation shafts are high, which makes imaging difficult; (2) the cracks on the shaft wall are long and irregular, so it is impossible to acquire the information of the whole crack from a single photo. Firstly, the mapping analysis between the concentration of water vapor and dust in the wind shaft and the image definition is determined by experiments. Then, the inspection robot is designed to move along the axial and circumferential directions to get close to the shaft wall, and the rack-and-rail drive design is adopted to ensure the real-time position feedback of the robot. Then, through the crack parameter detection method based on depth learning, the movement direction of the robot is controlled according to the crack direction so as to ensure that the complete crack parameters are obtained. Finally, the crack detection algorithm is verified by experiments.

【文献 156】
作者: Campos, DF
来源: JOURNAL OF FIELD ROBOTICS
题目: Nautilus: An autonomous surface vehicle with a multilayer software
摘要: The increasing adoption of robotic solutions for inspection tasks in challenging environments is becoming increasingly prevalent, particularly in the offshore wind energy industry. This trend is driven by the critical need to safeguard the integrity and operational efficiency of offshore infrastructure. Consequently, the design of inspection vehicles must comply with rigorous requirements established by the offshore Operation and Maintenance (O&M) industry. This work presents the design of an autonomous surface vehicle (ASV), named Nautilus, specifically tailored to withstand the demanding conditions of offshore O&M scenarios. The design encompasses both hardware and software architectures, ensuring Nautilus's robustness and adaptability to the harsh maritime environment. It presents a compact hull capable of operating in moderate sea states (wave height up to 2.5 m), with a modular hardware and software architecture that is easily adapted to the mission requirements. It has a perception payload and communication system for edge and real-time computing, communicates with a Shore Control Center and allows beyond visual line-of-sight operations. The Nautilus software architecture aims to provide the necessary flexibility for different mission requirements to offer a unified software architecture for O&M operations. Nautilus's capabilities were validated through the professional testing process of the ATLANTIS Test Center, involving operations in both near-real and real-world environments. This validation process culminated in Nautilus's reaching a Technology Readiness Level 8 and became the first ASV to execute autonomous tasks at a floating offshore wind farm located in the Atlantic.

【文献 157】
作者: Balan, K
来源: 2024 IEEE INTERNATIONAL SYMPOSIUM ON SAFETY SECURITY RESCUE ROBOTICS,
题目: Robot Path Planning Utilizing Object Recognition for Inspection of
摘要: Recent advances in machine learning (ML) based object recognition have enabled robotic systems to autonomously navigate to and investigate sources of radiation such as nuclear material containers. This paper demonstrates an autonomous system using a quadruped robotic platform. It autonomously surveys a nuclear facility and iteratively identifies nuclear material containers to locate target objects and investigates them by performing radiological measurements, which can assist in nuclear safeguards inspections. This capability is enabled through a suite of sensors called Localization and Mapping Platform mounted on the robot that maps a local scene and fuses the spectroscopic radiation data with camera imagery and a 3D representation of the environment. This paper demonstrates automatic navigation to a target object within the scene in minutes using a Robot Operating System interface, automatically detect target objects in the camera imagery using an instance segmentation model, and quantitatively assess the radiation signatures emitted from the identified objects. The instance segmentation model was trained on images of nuclear material containers, which were labeled using a novel semiautomatic annotation method, allowing for hundreds of highquality images to be labeled within 15 minutes. After training, the object detection results were fused with LiDAR data to create a consistent representation of the observed space and the locations of the detected object. A measurement campaign was conducted at the Nevada National Security Site where the robot's path planning and object recognition capabilities were tested. The robot's path-planning algorithm received the 3D coordinates of objects identified through ML-based object detection and autonomously navigated to those targets, performing radiation detection measurements that could support compliance determinations for nuclear safeguards protocols by quantitatively reconstructing the gamma-ray activity emitted from the detected objects.

【文献 158】
作者: Aljalaud, F
来源: MATHEMATICS
题目: Autonomous Multi-UAV Path Planning in Pipe Inspection Missions Based on
摘要: This paper presents a novel path planning heuristic for multi-UAV pipe inspection missions inspired by the booby bird's foraging behavior. The heuristic enables each UAV to find an optimal path that minimizes the detection time of defects in pipe networks while avoiding collisions with obstacles and other UAVs. The proposed method is compared with four existing path planning algorithms adapted for multi-UAV scenarios: ant colony optimization (ACO), particle swarm optimization (PSO), opportunistic coordination, and random schemes. The results show that the booby heuristic outperforms the other algorithms in terms of mean detection time and computational efficiency under different settings of defect complexity and number of UAVs.

【文献 159】
作者: Franceschini, R
来源: 2024 33RD IEEE INTERNATIONAL CONFERENCE ON ROBOT AND HUMAN INTERACTIVE
题目: Point, Segment, and Inspect: Leveraging Promptable Segmentation Models
摘要: Operating unmanned aerial vehicles (UAVs) for assets inspections poses distinct challenges encompassing the need to maintain a safe distance from the inspection area, ensure correct orientation towards the inspected surface, and achieve comprehensive coverage of the entire surface. Achieving these tasks is inherently complex and stressful. Therefore, a novel approach that seeks to enhance the piloting experience by harnessing the latest advancements in segmentation models, such as Segment Anything Model (SAM), is proposed. These models, thanks to their prompting capabilities, allow seamless communication between the operator and the UAV, opening up the possibility of defining intricate inspection regions through simple interactions. Within this approach, decision-making authority remains with the operator, while the UAV takes on the demanding task of segmenting the designated area and devising an appropriate traversal plan. Throughout this process, the operator's situational awareness is heightened through visual cues overlaid on the camera stream and a 3D panel presenting information of the drone position and spatially sensed data. This teleoperation framework allows the operator to maintain continuous control of the ongoing operation through a simplified interface. The paper delineates both the system and the methodology employed, showcasing the effectiveness of integrating segmentation models into the decision-making workflow. The validity of the proposed framework is established through testing within a photorealistic UAV simulator along with real experiments in a controlled laboratory environment.

【文献 160】
作者: Chang, CC
来源: AUTOMATION IN CONSTRUCTION
题目: Autonomous dimensional inspection and issue tracking of rebar using
摘要: Accurate and efficient inspection of rebar dimensions has proven to be a persistent challenge for researchers and practitioners. This paper introduces a semantically enriched 3D model-based system that employs computer vision and deep learning for location-aware identification and tracking of rebar issues. The system comprises four modules: (A) digital twin generation, (B) segmentation, (C) inspection, and (D) issue identification and tracking. The generation module constructs 3D models from rebar structures. The segmentation and inspection modules analyze the 3D models, enriching them with semantic information. The issue identification and tracking module exchanges information between the semantically enriched 3D models and the building information models across time. An experiment on a column rebar cage is conducted. A precision of over 90% and a recall of over 97% are reported in 3D instance segmentation. Diameter inspection achieves an accuracy of 95.5% for large-size rebars. Spacing inspection achieves a mean relative error of 0.98%. The defective spacing is identified and tracked.

【文献 161】
作者: Zhang, YH
来源: INTERNATIONAL JOURNAL OF ROBOTICS & AUTOMATION
题目: A NOVEL MINING BELT CONVEYOR INSPECTION ROBOT USED IN EXTREMELY COLD
摘要: Belt conveyors have been widely used in coal transportation, but the problems caused by the movement of belt conveyors threaten the production efficiency of coal mines, and also threaten the safety of workers. In order to realise the real-time detection of belt conveyor in a cold environment, a new belt conveyor detection robot in openpit mine is designed. In the special environment of extremely low temperature, the driving motor parameters and automatic charging requirements of the belt conveyor in an open-pit mine.

【文献 162】
作者: Moon, S
来源: 2024 IEEE INTERNATIONAL SYMPOSIUM ON SAFETY SECURITY RESCUE ROBOTICS,
题目: Efficient Line-of-Sight Viewpoint Sampling in Complex Environments for
摘要: This paper presents a novel approach to generate a set of viewpoints in order to maximize the surface coverage for robotic inspection missions with limited field-of-view onboard sensing and operation time. The problem encounters challenges in validating line-of-sight visibility within obstacle-rich environments, achieving feasible optimization to maximize surface coverage with minimal viewpoints, and ensuring tractable computational complexity for onboard durability. The proposed approach leverages sensor and environment geometric information to ensure surface coverage for inspection by visiting a minimal number of viewpoints. A computational complexity analysis reveals the proposed approach alleviates the computational power required compared with former viewpoint samplings in free space. The proposed viewpoint sampling approach is experimentally validated using a quadrupedal robot performing autonomous inspection missions in diverse urban environments, demonstrating better performance toward total surface area coverage, the computational time required, and the surface coverage rate during operation.

【文献 163】
作者: Song, S
来源: INTERNATIONAL JOURNAL OF CONTROL AUTOMATION AND SYSTEMS
题目: Development of a Biomimetic Underwater Robot for Bottom Inspection of
摘要: Underwater inspection of marine structures is important to ensure the safety and integrity of infrastructure; however, stable exploration using conventional underwater robots is limited because of various factors such as biofouling and currents. This study focuses on inspecting the surface under a marine structure. To inspect the surfaces covered with biofouling, a biomimetic underwater robot for inspection of marine structures (BRIM) with swimming, walking, and obstacle-negotiation capabilities was developed. The design parameters for walking on uneven terrain were adjusted, and a gait was developed to push aside obstacles that obstruct the view. Simulations of the dynamic models were implemented and stability measures of walking with various attitudes were computed to verify the proposed method. The feasibility of the robot in real-life scenarios was verified by performing unit and feasibility tests inside a water tank, demonstrating the effectiveness of the proposed system.

【文献 164】
作者: He, L
来源: JOURNAL OF NONDESTRUCTIVE EVALUATION
题目: Safety Assessment of Vertical Storage Tank Based on Robot Ultrasonic
摘要: Robot online inspection is a new technology for nondestructive and quantitative detection corrosion of vertical storage tank without emptying the liquid. The storage tank safety assessment methods for robot online inspection are still at the exploratory stage of development. An online inspection robot has been developed by the authors. This paper studies the evaluation methods of bottom plate corrosion with this robot detection. Aiming to characterize localized corrosion and pitting corrosion, multi-angle evaluation indices are proposed. Mathematical models based on reliability evaluation are derived. Above models and evaluation methods are then validated by a case study. Assessment results show that the maximum corrosion depth of storage tank floor is 2.44 mm, the residual useful life of the storage tank is 5 years, and the center coordinates of localized corrosion region of numbered A is (0.58 m, - 0.01 m), the area is 0.484 m(2), and the average corrosion depth is 2.03 mm. Abundant quantitative conclusions are obtained by using the proposed evaluation methods, instead of rough qualitative descriptions. This research provides references for storage tank safety assessments and formation standard based on robot online inspection.

【文献 165】
作者: Wang, JD
来源: JOURNAL OF ZHEJIANG UNIVERSITY-SCIENCE A
题目: Kinematic modeling and stability analysis for a wind turbine blade
摘要: Robots are used to conduct non-destructive defect detection on wind turbine blades (WTBs) and to monitor their integrity over time. However, current inspection robots are often bulky and heavy, and struggle to detect defects in the blade's main beam, thus presenting difficulties in portability and effectiveness. To address these issues, we designed a wheel-wing composite robot equipped with a curved surface-adaptive phased array ultrasonic detection device for the detection of defects in the WTB's main beam. We determined the pose equation under different section characteristics and identified the robot's stable range of motion, thus developing a model of its kinematics. A detection device adapted for variable curvature surfaces was designed to ensure tight coupling between the robot's probe and the blade. Additionally, element differential and least-square ellipse-fitting methods were employed to analyze blades with irregular sections. The simulation results demonstrated that the prototype can stably traverse an area with a vertical angle of +/- 14.06 degrees at a speed of 0.25 m/s, fully covering the main beam area of the blade during walking operations. Moreover, the robot can scan the main beam area at a speed of 0.10 m/s, enabling the accurate detection of defects.

【文献 166】
作者: Martinez-Sanchez, DE
来源: MACHINES
题目: Soft Robot for Inspection Tasks Inspired on Annelids to Obtain
摘要: Soft robotics is a rapidly advancing field that leverages the mechanical properties of flexible materials for applications necessitating safe interaction and exceptional adaptability within the environment. This paper focuses on developing a pneumatic soft robot bio-inspired in annelids or segmented worms. Segmentation, also called metamerism, increases the efficiency in body movement by allowing the effect of muscle contraction to generate peristaltic locomotion. The robot was built using elastomers by the casting technique. A sequence of locomotion based on two stages, relaxation and contraction, was proposed; the contraction stage is actuated by a vacuum pump. The locomotion performances are compared using different elastomers, such as Ecoflex 00-30, Dragon Skin 20, Mold Star 15 Slow, and Mold Star 30. Experimental tests were carried out inside a plexiglass pipe, 1 inch in diameter; a wide range of frequencies was tested for relaxation and contraction stages to evaluate the effect on the speed of the robot.

【文献 167】
作者: Chen, MH
来源: IEEE ROBOTICS AND AUTOMATION LETTERS
题目: A Passive Compliance Obstacle-Crossing Robot for Power Line Inspection
摘要: In scenarios of the overhead power line system, manual methods are inefficient and unsafe. Meanwhile, the majority of cantilevered robots have poor efficiency when crossing obstacles. This letter proposes a novel power line inspection and maintenance robot to solve these problems. The robot employs a passive compliance obstacle-crossing principle, which could rapidly cross obstacles with the cooperation of gas springs and climbing wheels. Under high payload, the robot could take 5-15 seconds without any complex strategies to roll over obstacles. A variable configuration platform is also designed, which has a multiple line mode and a single line mode. It makes the robot suitable for different kinds of overhead power lines. Meanwhile, the related adaptability analyses are presented. Manipulators are also installed to help the robot perform specific maintenance tasks. The results of lab experiments and field tests reveal that the robot could stably and rapidly cross obstacles, such as suspension clamps, vibration dampers, and spacers, and could perform three kinds of maintenance tasks on the line.

【文献 168】
作者: Quan, Q
来源: 2024 5TH INTERNATIONAL CONFERENCE ON MACHINE LEARNING AND HUMAN-COMPUTER
题目: Research and Design of Intelligent Inspection Robot for Large-Scale
摘要: In order to solve the problems of chicken status monitoring and farming decision making faced by intelligent livestock and poultry farming, this study aims to develop an intelligent inspection robot for chicken farms to explore the solution of intelligent inspection of chicken coops using Internet information technology and Internet of Things (IoT) technology.

【文献 169】
作者: Hu, YJ
来源: IEEE ROBOTICS AND AUTOMATION LETTERS
题目: A Portable Cable-Suspended Parallel Robot and Its Applications in Indoor
摘要: Cable-driven parallel robots (CDPRs) are now widely used in tasks such as filming and inspection due to their large workspace. Compared to unmanned aerial vehicles (UAVs), there's no need to navigate or position indoors for CDPRs, and CDPRs have lower noise levels and longer endurance. Therefore, this letter proposes a portable cable-suspended parallel robot (CSPR) to perform inspection tasks more effectively than UAVs in some indoor workplaces. It is highly integrated, compact, and lightweight, facilitating carrying, transportation and deployment. Then, the kinetostatic model of the proposed CSPR is established to analyze the advantages of the adopted cable configuration. To suppress the oscillation of the proposed under-actuated CSPR, reaction wheels are applied for active stabilization. Disturbance and trajectory experiments are carried out to validate the effectiveness of this oscillation suppression method. Moreover, with reaction wheels, the precision of the end-effector trajectory tracking improves by approximately 60%. At last, a demonstration experiment is conducted to show the application in tracking and filming a swimmer. The proposed CSPR could also be useful for other indoor applications, such as the volume measurement of indoor coal piles.

【文献 170】
作者: Jeon, KW
来源: SENSORS
题目: Development of an In-Pipe Inspection Robot for Large-Diameter Water
摘要: This paper describes the development of an in-pipe inspection robot system designed for large-diameter water pipes. The robot is equipped with a Magnetic Flux Leakage (MFL) sensor module. The robot system is intended for pipes with diameters ranging from 900 mm to 1200 mm. The structure of the in-pipe inspection robot consists of the front and rear driving parts, with the inspection module located centrally. The robot is powered by 22 motors, including eight wheels with motors positioned at both the bottom and the top for propulsion. To ensure that the robot's center aligns with that of the pipeline during operation, lifting units have been incorporated. The robot is equipped with cameras and LiDAR sensors at the front and rear to monitor the internal environment of the pipeline. Pipeline inspection is conducted using the MFL inspection modules, and the robot's driving mechanism is designed to execute spiral maneuvers while maintaining contact with the pipeline surface during rotation. The in-pipe inspection robot is configured with wireless communication modules and batteries, allowing for wireless operation. Following its development, the inspection robot underwent driving experiments in actual pipelines to validate its performance. The field test bed used for these experiments is approximately 1 km in length. Results from the driving experiments on the field test bed confirmed the robot's ability to navigate various curvatures and obstacles within the pipeline. It is posited that the use of the developed in-pipe inspection robot can reduce economic costs and enhance the safety of inspectors when examining aging pipes.

【文献 171】
作者: Deng, HF
来源: ANIMALS
题目: Visual Navigation of Caged Chicken Coop Inspection Robot Based on Road
摘要: Simple Summary In the process of large-scale cage chicken breeding, using inspection robots instead of manual detection can solve the problems of large workload and low detection efficiency. However, poor driving stability of an inspection robot will lead to low inspection accuracy. In order to ensure high efficiency, accuracy, and stability of detection, this study takes the stability of visual navigation for inspection robots as the optimization goal, and designs an efficient and accurate road extraction algorithm and a navigation line fitting algorithm with better robustness. The experimental results show that the algorithm proposed in this study can improve the stability of detection, achieve a faster running speed, and achieve a better detection effect and a higher detection efficiency while keeping the detection effect unchanged. The navigation algorithm can accelerate the automation process of large-scale cage chicken breeding and promote the realization of fast and accurate monitoring.Abstract The speed and accuracy of navigation road extraction and driving stability affect the inspection accuracy of cage chicken coop inspection robots. In this paper, a new grayscale factor (4B-3R-2G) was proposed to achieve fast and accurate road extraction, and a navigation line fitting algorithm based on the road boundary features was proposed to improve the stability of the algorithm. The proposed grayscale factor achieved 92.918% segmentation accuracy, and the speed was six times faster than the deep learning model. The experimental results showed that at the speed of 0.348 m/s, the maximum deviation of the visual navigation was 4 cm, the average deviation was 1.561 cm, the maximum acceleration was 1.122 m/s2, and the average acceleration was 0.292 m/s2, with the detection number and accuracy increased by 21.125% and 1.228%, respectively. Compared with inertial navigation, visual navigation can significantly improve the navigation accuracy and stability of the inspection robot and lead to better inspection effects. The visual navigation system proposed in this paper has better driving stability, higher inspection efficiency, better inspection effect, and lower operating costs, which is of great significance to promote the automation process of large-scale cage chicken breeding and realize rapid and accurate monitoring.

【文献 172】
作者: Wang, JX
来源: 2024 INTERNATIONAL CONFERENCE ON ELECTRONIC ENGINEERING AND INFORMATION
题目: Weld Detection and Tracking Algorithm for Inspection Robot Based on Deep
摘要: Due to the particularity and security of the special equipment, its welds should be inspected regularly. Compared to the manually operation mode, the inspection robot are able to improve the inspection efficiency. However, there are still technical problems in the accurate detection and tracking of the weld for the inspection robot. To the illustration, we develop an efficient algorithm based on the deep learning to the weld detection and tracking. In this algorithm, the improved YOLOv5n instance is used to segment the network for the weld detection. For the Backbone, the MixConv convolution module is introduced to replace the Cony module of the original network, which provides a larger receptive field and richer semantic information for the network. To improve the computational speed, the Ghost-C3 structure is introduced to replace the C3 structure in the Neck of the original network. In addition, the least square method is used to segment the weld to extract the weld trajectory with high accuracy. The experimental results of our algorithm show that, the size of the improved segmentation model is only 3Mb, and the weight is 26.8% smaller than the original model. Moreover, the precision of the improved model reaches to 99.0%, which is 1.6% higher than the original model.

【文献 173】
作者: Li, LQ
来源: JOURNAL OF INTELLIGENT & ROBOTIC SYSTEMS
题目: Research on the Key Technology of a Small Rock Hole Inspection Robot
摘要: The vertical stable walking problem of the rock hole inspection robot is a difficult and key point in design and control, which solves the important problem of the robot being able to walk vertically. In this paper, a rock hole inspection robot is designed, which can inspect the pore morphology and rock cracks in rock holes by carrying cameras, test sensors and other monitors. It can adapt to the inspection function of rock holes at an angle of 0-90 & DEG; with a diameter of 95 mm-105 mm. ADAMS software is used to carry out dynamic simulation analysis of the robot, and the reasonable design of the pre-tightening reducing mechanism is obtained. At the same time, the important influence of the pre-tightening pressure between the walking wheel and the hole wall and the spring pre-tightening force on the robot's traction force is obtained. On this basis, the problem of vertical walking is emphatically solved, so that the robot can walk at any angle within the 90 & DEG; range rock hole, thus expanding the application scope of the robot. Based on the theoretical analysis, a robot experimental platform is built to verify the key technologies of the robot and test the performance of the robot, including visual acoustic signal acquisition, moving speed test and traction force test, which proved the correctness of theoretical analysis and design.

【文献 174】
作者: Lionel, N
来源: 9TH INTERNATIONAL CONFERENCE ON MECHATRONICS ENGINEERING, ICOM 2024
题目: Autonomous Inspection of Solar Panels and Wind Turbines Using YOLOv8
摘要: This paper introduces an autonomous inspection system for solar panels and wind turbines utilizing Tello drones and the YOLOv8 object detection algorithm. The main objective is to establish an efficient method for identifying and evaluating these renewable energy components' conditions, focusing on detecting issues such as breakage and dust accumulation. The system involves a pair of Tello drones operating as a swarm and connected to a standard router to enable real-time video streaming and data processing. The drones utilize the YOLOv8 algorithm for object detection, and Python programming is employed to manage their operations. The methodology encompasses establishing reliable communication among the drones, router, and laptop, initializing the drones, capturing real-time video, and utilizing YOLOv8 for object recognition and classification. The paper presents case studies demonstrating the system's effectiveness in detecting and classifying solar panels and wind turbines under varied conditions. While the system exhibits promise in reducing manual inspection labour and enhancing safety, limitations related to image quality suggest that using higher-resolution cameras could further improve its efficiency.

【文献 175】
作者: Vasileiou, M
来源: JOURNAL OF MECHANISMS AND ROBOTICS-TRANSACTIONS OF THE ASME
题目: <sc>kalypso</sc> Autonomous Underwater Vehicle: A 3D-Printed Underwater
摘要: In fish farms, a major issue is the net cage wear, resulting in fish escapes and negative impact on fish quality, due to holes and biofouling of the nets. To minimize fish losses, fisheries utilize divers to inspect net cages on a weekly basis. Aquaculture companies are looking for ways to maximize profit, and reducing maintenance costs is one of them. Kefalonia Fisheries spend 250,000 euros yearly on diver expenses for net cages maintenance. This work is about the design, fabrication, and control of an inexpensive autonomous underwater vehicle (AUV) intended for inspection in net cages at Kefalonia Fisheries S.A. in Greece. Its main body is 3D printed, and its eight-thruster configuration grants it six degrees-of-freedom. The main objective of the vehicle is to limit maintenance costs by increasing inspection frequency. The design, fabrication, electronic components, and software architecture of the AUV are presented. In addition, the forces affecting kalypso, mobility realization, navigation, and modeling are quoted along with a flow simulation and the experimental results. The proposed design is adaptable and durable while remaining cost-effective, and it can be used for both manual and automatic operations.

【文献 176】
作者: Ardic, O
来源: IEEE ACCESS
题目: Deep Learning-Based Real-Time Engine Part Inspection With Collaborative
摘要: Vehicle manufacturing requires error-free processes, as modern vehicles are made up of thousands of parts, including around 280 critical components for safe driving. According to the National Highway Traffic Safety Administration (NHTSA), 2% of vehicle accidents will be caused by defective parts. Current inspection systems in manufacturing plants have limitations, with a high risk of defective parts reaching consumers and leading to recalls. This study aims to develop a real-time, deep learning-based engine part inspection system to improve accuracy and efficiency in mass production. The system, implemented in a large automotive manufacturing plant, uses a Fanuc CR-15ia collaborative robot to inspect engine parts. Combining the single-shot detector (SSD) and faster region-based convolutional neural network (R-CNN) algorithms, the system achieves 99.9% accuracy measured after four months of use, with an Average Precision (AP) of 0.994 for Faster R-CNN and 0.955 for SSD. The inspection system addresses cycle time concerns and is integrated with factory systems for real-time data sharing. Ongoing enhancements aim to improve system performance further.

【文献 177】
作者: Chai, GF
来源: INTERNATIONAL JOURNAL OF SIMULATION MODELLING
题目: MULTI-ROBOT PATH OPTIMIZATION AND SIMULATION FOR MULTI-ROUTE INSPECTION
摘要: The research of multiple inspection robots' path simulation planning helps to improve the inspection ability and efficiency of the multi-robot system. This paper studies the problem of cooperative optimization and simulation of multiple robots for multiple inspections in intelligent manufacturing. A dynamic simulation model of the inspection robot is used to construct the state equation of the multi -robot inspection simulation system. The square grid is used to decompose the intelligent manufacturing workshop area and simulate the workshop space. With reinforcement learning, a multi-robot patrol simulation system is created for full coverage path simulation planning. The results show the effectiveness of the system for cooperative optimization control and reasonable path planning. (Received in October 2022, accepted in January 2023. This paper was with the authors 1 month for 2 revisions.)

【文献 178】
作者: Cai, T
来源: 2023 29TH INTERNATIONAL CONFERENCE ON MECHATRONICS AND MACHINE VISION IN
题目: Positioning and Mapping Technology for Substation Inspection Robot Based
摘要: Obtaining a planar map of the substation is crucial for achieving automated substation inspection. Currently, traditional positioning and mapping solutions like SLAM and offline GPS positioning often suffer from difficulties in feature recognition, lengthy mapping times, or excessive storage space requirements for maps. Building upon this, a substation inspection robot positioning and mapping technology is produced based on Gaussian projection. After collecting boundary points of the work area's internal and external perimeters, along with obstacle zone boundaries, geographic coordinates collected are transformed into planar Cartesian coordinates through Gaussian projection. Subsequently, a planar grid map is established using the ray casting method. The achieved map information, combined with real-time updates on the robot's current position, enables accurate robot positioning. Feasibility experiments have verified the effectiveness of this positioning and mapping technology. This methodology significantly enhances mapping speed, reduces necessary map storage space, and the resultant map can be utilized for subsequent path planning and navigation of inspection robots.

【文献 179】
作者: Ma, RC
来源: INTELLIGENT ROBOTICS AND APPLICATIONS, ICIRA 2024, PT I
题目: Analysis and Prospects of the Current Research Status of Robotics for
摘要: With the development of robotics technology, the application fields of robots are becoming more and more extensive. High-end equipment, facilities, and equipment are also more and more complex, and the structure is more precise, and miniaturized. When corrosion, wear, and cracks occur inside the equipment, it is very difficult to detect without damaging the equipment. With the development of micro-small robotic technology, in-situ detection of robots in a narrow space can solve the internal detection problems of complex high-end equipment and facilities. The types of in-situ detection robots in narrow spaces are described. The technical characteristics of miniaturization, intelligence, versatility, and remote operation of in-situ detection robots in narrow spaces are analyzed. The challenges and future development directions of perception, positioning, and autonomous control of in-situ detection robots in narrow spaces are prospected.

【文献 180】
作者: Alexiou, D
来源: 2023 INTERNATIONAL CONFERENCE ON UNMANNED AIRCRAFT SYSTEMS, ICUAS
题目: Visual Navigation based on Deep Semantic Cues for Real-Time Autonomous
摘要: In this paper, a visual guided navigation method for Unmanned Aerial Vehicles (UAVs) during power line inspections is proposed. Our method utilizes a deep learning-based image segmentation algorithm to extract semantic masks of the power lines from onboard camera images. These masks are then processed and visual characteristics along with geometrical calculations generate velocity commands for the 3D position and yaw control that feed the UAV's navigation system. The accuracy, robustness, and computational efficiency of the power line segmentation module are evaluated on real benchmark datasets. Extensive simulation experiments have been conducted to assess the proposed method's performance in terms of inspection coverage, considering various textured environments and extreme initial states. The proposed method for navigating a UAV towards target PTLs is shown to be effective in terms of robustness and stability. This is achieved through accurate segmentation of the PTLs and the generation of compact velocity directives based on visual information in various environmental conditions. The results indicate a significant improvement in the precision of autonomous UAV-based inspections of power infrastructure due to continuous scoping of the transmission lines and safe yet stable navigation.

【文献 181】
作者: Guo, YX
来源: IEEE TRANSACTIONS ON INSTRUMENTATION AND MEASUREMENT
题目: Multi-Scale-Slope Linear Transformer-Based Skeleton Detection for
摘要: Railway inspection is vital to guarantee the safety of railway transportation but now it heavily relies on manual labor, which is difficult and risky in certain regions. The primary premise of autonomous railway inspection using autonomous aerial vehicles (AAVs), with the aim to mitigate the dependency on labor, is achieving autonomous flight along the railway. To ensure the AAV aligns with the railway in high precision, vision-based navigation is more applicable and effective than GNSS-based navigation. In this article, we propose a novel end-to-end network, termed multi-scale-slope linear transformer (MSSLFormer), to detect the railway skeleton directly as target flight trajectory. Our network roots in multi-scale-slope linear attention for enhanced railway skeleton detection and adopts a concurrent head, in which a semantic segmentation branch is included for subsequent inspection tasks. To ensure real-time detection, we leverage attention parameters sharing mechanism when calculating the query-key matrix. Experiments show that our model achieves superior performance, F-score of 86.44%, for skeleton detection on our railway dataset, which contains 9210 labeled images. Relying on our flight strategy, AAV can fly autonomously along the railway up to 15 m/s, demonstrating the great potential for future railway inspection tasks.

【文献 182】
作者: Marcellini, S
来源: EXPERIMENTAL ROBOTICS, ISER 2023
题目: Development of a Semi-autonomous Framework for NDT Inspection with a
摘要: This letter investigates the problem of controlling an aerial manipulator, composed of an omnidirectional tilting drone equipped with a five-degrees-of-freedom robotic arm. The robot has to interact with the environment to inspect structures and perform non-destructive measurements. A parallel force-impedance control technique is developed to establish contact with the designed surface with a desired force profile. During the interaction, a pushing phase is required to create a vacuum between the surface and the echometer sensor mounted at the end-effector, to measure the thickness of the interaction surface. Repetitive measures are performed to show the repeatability of the algorithm.

【文献 183】
作者: Zhang, W
来源: INTERNATIONAL JOURNAL OF ROBOTICS & AUTOMATION
题目: GLASIUS BIO-INSPIRED NEURAL NETWORK ALGORITHM-BASED SUBSTATION
摘要: This study presents a glasius bio-inspired neural network (GBNN) algorithm for intelligent substation inspection robot autonomous path planning. First, a GBNN Neural map is established to represent the working environment of the inspection robot. In this model, each neuron corresponds to a grid map position unit. The GBNN model was trained to map the environment, including obstacles and potential paths, into a discrete neural network representation. Second, the motion path of the inspection robot was planned autonomously based on the activation output values of the neurons in the neural network. The robot selected the path with the highest activation output value for the next movement direction. The simulation results under dynamic obstacle scenarios or in uncertain environments demonstrated the effectiveness of the GBNN algorithm in path planning.

【文献 184】
作者: Gao, YP
来源: JOURNAL OF MANUFACTURING SCIENCE AND ENGINEERING-TRANSACTIONS OF THE
题目: A Two-Stage Focal Transformer for Human-Robot Collaboration-Based
摘要: Human-robot collaboration has become a hotspot in smart manufacturing, and it also has shown the potential for surface defect inspection. The robot can release workload, while human collaboration can help to recheck the uncertain defects. However, the human-robot collaboration-based defect inspection can be hardly realized unless some bottlenecks have been solved, and one of them is that the current methods cannot decide which samples to be rechecked, and the workers can only recheck all of the samples to improve inspection results. To overcome this problem and realize the human-robot collaboration-based surface defect inspection, a two-stage Transformer model with focal loss is proposed. The proposed method divides the traditional inspection process into detection and recognition, designs a collaboration rule to allow workers to collaborate and recheck the defects, and introduces the focal loss into the model to improve the recognition results. With these improvements, the proposed method can collaborate with workers by rechecking the defects and improve surface quality. The experimental results on the public dataset have shown the effectiveness of the proposed method, the accuracies are significantly improved by the human collaboration, which are 1.70%similar to 4.18%. Moreover, the proposed method has been implemented into a human-robot collaboration-based prototype to inspect the carton surface defects, and the results also verify the effectiveness. Meanwhile, the proposed method has a good ability for visualization to find the defect area, and it is also conducive to defect analysis and rechecking.

【文献 185】
作者: Mathur, P
来源: IEEE ACCESS
题目: Autonomous Inspection of High-Rise Buildings for Façade Detection and 3D
摘要: Given the current emphasis on maintaining and inspecting high-rise buildings, conventional inspection approaches are costly, slow, error-prone, and labor-intensive due to manual processes and lack of automation. In this paper, we provide an automated, periodic, accurate and economical solution for the inspection of such buildings on real-world images. We propose a novel end-to-end integrated autonomous pipeline for building inspection which consists of three modules: i) Autonomous Drone Navigation, ii) Facade Detection, and iii) Model Construction. Our first module computes a collision-free trajectory for the UAV around the building for surveillance. The images captured in this step are used for facade detection and 3D building model construction. The facade detection module is a deep learning-based object detection method which detects cracks. Finally, the model construction module focuses on reconstructing a 3D model of a building from captured images to mark the corresponding cracks on the 3D model for efficient and accurate inferences from the inspection. We conduct experiments for each module, including collision avoidance for drone navigation, facade detection, model construction and mapping. Our experimental analysis shows the promising performance of i) our crack detection model with a precision and recall of 0.95 and mAP score of 0.96; ii) our 3D reconstruction method includes finer details of the building without having additional information on the sequence of images; and iii) our 2D-3D mapping to compute the original location/world coordinates of cracks for a building.

【文献 186】
作者: Lee, J
来源: 2024 IEEE/RSJ INTERNATIONAL CONFERENCE ON INTELLIGENT ROBOTS AND SYSTEMS
题目: Safety-critical Autonomous Inspection of Distillation Columns using
摘要: EMIRATES

【文献 187】
作者: Tani, S
来源: OCEANS 2023 - LIMERICK
题目: Comparison of Monocular and Stereo Vision approaches for Structure
摘要: Periodical inspections are a fundamental operation to monitor the status of underwater structures and to assess their need for proper maintenance or repair interventions. Autonomous Underwater Vehicles (AUVs) could represent a viable option to carry out underwater inspection tasks, potentially bringing benefits in terms of safety for human operators and quality of the collected data. Aiming at developing a fully autonomous vision-based inspection strategy, this paper proposes a comparative analysis between monocular and stereo vision approaches for estimating the lateral velocity of an AUV and its orientation with respect to a target surface. The proposed analysis is performed by exploiting a dataset of real underwater images, collected during at-sea experiments in which the Zeno AUV was remotely driven to carry out a pier inspection. Specifically, the performance of the two solutions in terms of estimation of the robot lateral velocity is assessed by considering doppler velocity log measurements as benchmark. Instead, the accuracy of the estimation of the vehicle orientation with respect to the target is evaluated by taking into account both geographical information of the pier and AUV attitude observations. The comparison suggests that stereo vision provides better performance for estimating the relative orientation between the AUV and the target; on the contrary, the monocular approach produces more reliable lateral velocity estimates. The results obtained prove the suitability of the two vision-based strategies for inspection applications in a real underwater scenario, thus suggesting a possible implementation onboard the reference vehicle.

【文献 188】
作者: Koyama, N
来源: 2024 ASIA PACIFIC SIGNAL AND INFORMATION PROCESSING ASSOCIATION ANNUAL
题目: Hammering Inspection System Using HPSS and Gradient Boosting with a
摘要: This paper proposes an automatic judgment system that suppresses noise in the observed signal, which contains both hammering sound and noise, using Harmonic/Percussive Sound Separation (HPSS) to extract hammering sound, and then uses machine learning to judge whether the inspection area is healthy or damaged. The proposed method calculates hammering sounds from the observed signal using HPSS, and extracts features considering the timing of the hammering sound occurrence from the hammering sound. Subsequently, Gradient Boosting is used to automatically judge whether the area is healthy or damaged. The feature of the proposed method is that it suppresses noise while retaining the characteristic hammering sound necessary for judging healthy and damaged areas, and by extracting features considering the timing of the hammering sound occurrence, it allows for highly accurate classification.

【文献 189】
作者: Wu, JY
来源: INTERNATIONAL JOURNAL OF APPLIED EARTH OBSERVATION AND GEOINFORMATION
题目: UPKD: Unsupervised pylon keypoint detection from 3D LiDAR data for
摘要: The automatic extraction of inspection points for pylons is essential for intelligent Unmanned Aerial Vehicle (UAV) power inspections, especially in generating inspection route. However, current researches primarily focus on power scene perception and power component fault detection, with little attention paid to the inspection point detection. The primary reason are the lack of public pylon datasets for keypoint detection and the neglect of distinguish the inspection points from the keypoints used in feature extraction. Regarding that, this paper proposes a two-stage unsupervised pylon keypoint detection (UPKD) method to improve the efficiency of power inspections. In the first stage, the UPKD Network processes the point cloud to generate candidate keypoints, which comprises two main components: a data normalization module and an unsupervised keypoint detection network (UKD-Net). The data normalization module compresses information based on the symmetric structure of pylons, thereby reducing instability in inspection point detection. The UKD-Net incorporates a Point Transformer layer that uses self-attention mechanisms to extract features from the point cloud. In the second stage, a convex optimization strategy is applied to filter and acquire inspection points. These inspection points are then interconnected using a shortest-path strategy to generate the UAV inspection route. Our dataset is obtained using the Riegl VUX-1 laser measurement system and comprises 3,296 pylons of 10 types. Each pylon's point cloud contains up to 25,000 points, with a high point density of 100 pts/m2. Extensive experiments show that the UPKD Network achieved state-of-the-art performance on our dataset, with repeatability achieving 90.39%, effectiveness (the ratio of effective keypoints to annotation points) achieving 69.95%, and completeness (the ratio of detected annotation points to keypoints) achieving 79.55%.

【文献 190】
作者: Lin, WW
来源: APPLIED SCIENCES-BASEL
题目: Research on Digital Meter Reading Method of Inspection Robot Based on
摘要: Aiming at solving the issue of blurred images and difficult recognition of digital meters encountered by inspection robots in the inspection process, this paper proposes a deep-learning-based method for blurred image restoration and LED digital identification. Firstly, fast Fourier transform (FFT) is used to perform blur detection on the acquired images. Then, the blurred images are recovered using spatial-attention-improved adversarial neural networks. Finally, the digital meter region is extracted using the polygon-YOLOv5 model and corrected via perspective transformation. The digits in the image are extracted using the YOLOv5s model, and then recognized by the CRNN for digit recognition. It is experimentally verified that the improved adversarial neural network in this paper achieves 26.562 in the PSNR metric and 0.861 in the SSIM metric. The missing rate of the digital meter reading method proposed in the paper is only 1% and the accuracy rate is 98%. The method proposed in this paper effectively overcomes the image blurring problem caused by the detection robot during the detection process. This method solves the problems of inaccurate positioning and low digital recognition accuracy of LED digital meters in complex and changeable environments, and provides a new method for reading digital meters.

【文献 191】
作者: Li, YS
来源: ROBOTICS AND AUTONOMOUS SYSTEMS
题目: Inspection robot GPS outages localization based on error Kalman filter
摘要: In urban environments, inspection robots face complex terrain and variable motion states, posing high demands on their positioning systems. Although the integration of Micro-Electro-Mechanical Systems Inertial Navigation Systems (MEMS-INS) with the Global Positioning System (GPS) provides continuous positioning information, high buildings and tunnels in cities can block GPS signals, leading to signal interruptions and increased positioning errors. During GPS outages, MEMS-INS gradually accumulates errors, severely affecting positioning accuracy. To address this issue, this paper proposes an adaptive error state Kalman Filter (AESKF), which employs an adaptive mechanism to eliminate the noise impact of MEMS-INS and reduce reliance on the process model. Additionally, a deep learning framework based on the Self-Attention mechanism of the Transformer and a custom loss function Long Short-Term Memory (LSTM) module is proposed to predict position increments of the inspection robot. Combining AESKF with Transformer-LSTM achieves optimized positioning accuracy of the inspection robot during GPS outages in dynamic urban environments. Simulation and practical experimental results demonstrate that the combination of AESKF and Transformer-LSTM significantly improves positioning accuracy. Compared to other mature methods, the Root Mean Square Error (RMSE) of positioning is reduced by up to 83.64% in the north direction and 89.56% in the east direction. When the GPS signal interruption lasts for 10 s and 60 s, the maximum position error standard deviation (STD) is 0.1186 m and 1.0417 m, respectively.

【文献 192】
作者: Yu, X
来源: 2024 IEEE INTERNATIONAL CONFERENCE ON MECHATRONICS AND AUTOMATION, ICMA
题目: A Collision Detection Algorithm of Inspection Robot for Steam Generator
摘要: Ensuring the operational safety of inspection robot for steam generator heat transfer tubes necessitates continuous internal collision detection to prevent self-collision and external collision detection to avoid contact with the surrounding environment. However, current collision detection algorithms struggle to balance accuracy with computational efficiency and are not fully applicable to the unique conditions of a nuclear power plant. This paper introduces a novel collision detection algorithm specifically tailored for a crawler-type inspection robot for steam generator heat transfer tubes. By translating three-dimensional collision issues into two-dimensional intersection problems, the algorithm simplifies the complexity of collision detection while ensuring accuracy. Simulation results indicate that the algorithm achieves an average computation speed of 0.02 seconds per instance, which is sufficient to meet real-time planning requirements during autonomous operations. Furthermore, even in the presence of motion errors, the algorithm maintains a 100% safety rate, guaranteeing absolute safety of the robot's movement. These findings further validate the feasibility and practicality of the proposed algorithm for use within the nuclear power plant environment.

【文献 193】
作者: Du, YR
来源: OCEAN ENGINEERING
题目: A wall climbing robot based on machine vision for automatic welding seam
摘要: With the ongoing progress of industrial technology such as shipbuilding, the importance of weld quality in industrial production is becoming increasingly prominent. Intelligent and automated welding seam inspection robots are more efficient than traditional manual inspection and can avoid dangerous accidents. This article describes the design of a welding seam inspection robot suitable for high-altitude ship operation. The robot uses machine vision and object segmentation models to automatically detect the position of welding seams, and uses a cubic polynomial to fit the welding seam path. The upper and lower computers of the robot communicate through WIFI transmission and TCP protocol, which can realize remote real-time detection of weld surface defects. In addition, this article designs a permanent magnet adsorption structure for robot high-altitude wall climbing, which has been verified through simulation and experimental verification. To verify the intelligence of the robot, this paper conducted performance analysis experiments on weld line recognition and tracking models and surface defect models. The experimental results showed that the average detection accuracy of the weld line recognition and tracking algorithm was 96.8%, and the average detection accuracy of surface defects in the three types of welds was 94.2%. The method proposed in this article combines two algorithms and a special robot structure, providing a new approach for the automated inspection of welds in large industrial products such as ships.

【文献 194】
作者: Bedkowski, J
来源: SOFTWAREX
题目: End to end navigation stack for nuclear power plant inspection with
摘要: This paper describes a novel approach for nuclear facility inspection with novel automated 3D mapping system as an open source end to end navigation stack available at https://github.com/JanuszBedkowski/msas_enrich_ 2023. Incidents such as Fukushima, Majak or Chernobyl as well as the decommissioning and dismantling of old nuclear facilities ( e.g. Sellafield, Asse or Murmansk) are showing great importance of the robotic technology. Rapid inspection requires reliable, accurate, precise and repeatable simultaneous localization and mapping. Proposed SLAM approach uses only non repetitive scanning pattern Lidar (Livox Mid360) and integrated inertial measurement unit. The novelty is based on feature less single core SLAM implementation. It fuses Normal Distributions Transform and motion model for simultaneous map building and current pose estimation. Motion model bounds an optimization result, thus it is stable and reliable. It requires less than 10 ms for pose update, trajectory tracking and emergency behavior. This method is a candidate for real time application since a calculation time is bounded and it uses only one core of Intel Celeron CPU G1840 2.8 GHz. It was tested both (i) during EnRicH 2023 https://enrich.european-robotics.eu/ - the European robotics hackathon, (ii) in laboratory conditions. This open source project provides also software of base station, thus it is first end to end solution available in literature.

【文献 195】
作者: Colón, AM
来源: 2023 IEEE 50TH PHOTOVOLTAIC SPECIALISTS CONFERENCE, PVSC
题目: Undergraduate Research Experience in the Design and Construction of a
摘要: Renewable energy has become an area of intense development and innovation as the world moves towards net zero emission goals. Thus, creating demand for tools to perform specialized processes such as gathering scientific data, inspection, and maintenance of photovoltaic (PV) arrays. Said processes are needed to ensure that existing and future systems are working efficiently and safely to meet the energy industry's critical standards. The approach to address this need was to develop a robot to perform said specialized processes. The prototype design and construction of the PV Inspection Robot used power electronics, control systems, and algorithms as part of a complete undergraduate research experience with a project-based learning (PBL) approach.

【文献 196】
作者: de Souza, MB
来源: SYNERGETIC COOPERATION BETWEEN ROBOTS AND HUMANS, VOL 2, CLAWAR 2023
题目: Efficiency Optimization of the Gear Reducer of an Overhead Power Line
摘要: Electrical utility companies regularly inspect their power line networks to guarantee efficiency and reliability in energy transmission and distribution. However, the power lines inspection processes are expensive and time demanding, requiring robotized solutions to become feasible. A key issue in developing power line inspection robots is their energy efficiency, as they are required to operate for as long as possible. This work aims to reduce an inspection robot's energy consumption by optimizing the mechanical efficiency of its gear reducer's traction motor. The robot's gear reducer is a planetary gear train whose efficiency is modeled via Davies' method. The planetary gear train efficiency is optimized considering volume, size, and allowable stress constraints. The impact of the performance of the original and the optimized gearboxes in the batteries' final SOC is evaluated, and it is verified that the gearbox efficiency slightly impacts the robot's consumption.

【文献 197】
作者: Wu, SJ
来源: WALKING ROBOTS INTO REAL WORLD, CLAWAR 2024 CONFERENCE, VOL 1
题目: Linkage Length Optimization of a Climbing Inspection Robot Using an Area
摘要: This paper develops a new area overlap method (AOM) to optimize the linkage length of a ladder-climbing robot. Unlike the traditional path generation methods that use precise synthesis points, AOM evaluates the optimisation result by the area overlap ratio of two enclosed trajectories. The proposed method is suitable for the case that trajectory planning of a mechanism moves over obstacles, where the generated desired trajectory must completely contain the target area. Since the linkage trajectory optimized by AOM will not intersect with the target area, the AOM's result will help prevent the robot from being interfered with by obstacles during climbing. Based on the AOM, this paper optimizes the standard Hoeken linkage structure. The simulation results show that the AOM performs better than the traditional path generation method in this case. To further verify the effectiveness of the proposed method, a ladder-climbing robot driven by one motor was designed. The experimental evaluations show that the robot can successfully climb a ladder.

【文献 198】
作者: Shiao, YJ
来源: APPLIED SCIENCES-BASEL
题目: Steering Dynamic and Hybrid Steering Control of a Novel Micro-Autonomous
摘要: This paper aims to present a hybrid steering control method combining the self-guidance capability of a wheelset and fuzzy logic controller (FLC), which were applied to our new micro-autonomous railway inspection vehicle, enhancing the vehicle's stability. The vehicle features intelligent inspection systems and a suspension system with variable damping capability that uses smart magnetorheological fluid to control vertical oscillations. A mathematical model of the steering dynamic system was developed based on the vehicle's unique structure. Two simulation models of the vehicle were built on Simpack and Simulink to evaluate the lateral dynamic capability of the wheelset, applying Hertzian normal theory and Kalker's linear theory. The hybrid steering control was designed to adjust the torque differential of the two front-wheel drive motors of the vehicle to keep the vehicle centered on the track during operation. The control simulation results show that this hybrid control system has better performance than an uncontrolled vehicle, effectively keeps the car on the track centerline with deviation below 10% under working conditions, and takes advantage of the natural self-guiding force of the wheelset. In conclusion, the proposed hybrid steering system controller demonstrates stable and efficient operation and meets the working requirements of intelligent track inspection systems installed on vehicles.

【文献 199】
作者: Brogaard, RY
来源: ROBOTICS AND AUTONOMOUS SYSTEMS
题目: Autonomous GPU-based UAS for inspection of confined spaces: Application
摘要: Inspection of confined spaces poses a series of health risks to human surveyors, and therefore a need for robotic solutions arises. In this paper, we design and demonstrate a real-time system for collecting 3D structural and visual data from a series of inspection points within a prior map of a confined space. The system consists of a GPU accelerated 3D point cloud registration and a visual inertial odometry estimate fused in an Unscented Kalman Filter. Using the state-of-the-art deep learning-based feature descriptors, FCGF -and the robust Teaser++ 3D registration algorithm- point clouds from a narrow field of view, time-of-flight, camera can be registered to a prior map of the environment, to provide accurate cm-level absolute pose estimates. The uncertainty of the system is furthermore estimated on the basis of the novel GPU-based Stein ICP algorithm. Visual defects, represented by augmented reality fiducial markers, are automatically detected during inspection, and their positions are estimated in the map frame of the confined space. The performance of the system has been evaluated in realtime onboard a small UAV, within a mock-up model of a water ballast tank from a marine vessel, where the UAV was able to navigate and inspect the ambiguous and featureless environment. All defects were estimated within +/-10 cm of their actual position.

【文献 200】
作者: Li, ZR
来源: IEEE SENSORS JOURNAL
题目: UAV High-Voltage Power Transmission Line Autonomous Correction
摘要: With the development of technology, unmanned aerial vehicles (UAVs) are playing an increasingly important role in the inspection of high-voltage power transmission line. The traditional inspection method relies on the operator to manually control the drone for inspection. Although many companies are using real-time dynamic carrier phase differencing technology to achieve high-precision positioning of UAVs, when UAVs fly autonomously at high altitudes to photograph specific objects, the objects tend to deviate from the center of the picture. To address this error, in this article, an autonomous UAV inspection system based on object detection is designed: 1) to detect inspection objects, the corresponding dataset is established on the basis of the UAV autonomous inspection task; 2) to obtain the position information of the target object, a lightweight object detector based on the YOLOX network model is designed. First, the backbone is replaced with MobileNetv3. Next, in the neck structure, the Ghost module is introduced and depthwise convolution is applied instead of normal convolution. Then, to embed the location information into the channel attention, coordinate attention (CA) is introduced after the output feature layer of the backbone, enabling the lightweight network to operate on a larger area of focus. Finally, to improve the accuracy of the bounding box regression, the ${\alpha }$ -distance-IoU (DIOU) loss function is introduced; 3) to obtain the best image acquisition position, the results of object detection combined with the real-time status of the UAV are used; and 4) to enable the UAV to complete the final corrections, position control and altitude control are used. Compared with the original YOLOX_tiny, the new model improves the mAP_0.5:0.95 metric by about 2% points, with a significant reduction in the number of parameters and computation, while running at 56 frames/s on Nvidia NX. This system can effectively solve the problem of the target deviating from the center of the picture when the UAV takes pictures during a high-altitude autonomous inspection, verified by many actual flight experiments.

【文献 201】
作者: Brandonisio, A
来源: AEROSPACE SCIENCE AND TECHNOLOGY
题目: Closed-loop AI-aided image-based GNC for autonomous inspection of
摘要: Autonomy is increasingly crucial in space missions due to several factors driving the exploration and utilization of space. In the meanwhile, Artificial Intelligence methods begin to play a crucial role in addressing the challenges associated with and enhancing autonomy in space missions. The proposed work develops a closed-loop simulator for proximity operations scenarios, particularly for the inspection of an unknown and uncooperative target object, with a fully AI-based image processing and GNC chain. This tool is based on four main blocks: image generation, image processing, navigation filter, and guidance and control blocks. All of them have been separately tested and tuned to ensure the correct interface and compatibility in the close-loop architecture. Afterwards, the overall architecture is deployed in an extensive Montecarlo testing campaign to verify and validate the performance of the proposed IP-GNC loop.

【文献 202】
作者: Hinostroza, MA
来源: PROCEEDINGS OF ASME 2024 43RD INTERNATIONAL CONFERENCE ON OCEAN,
题目: PRELIMINARY EXPERIMENTAL RESULTS OF A TEMPORAL PLANNING-BASED SYSTEM FOR
摘要: Inspection and maintenance (IM) operations in offshore oil& gas platforms involve significant challenges due to the harsh conditions in such remote environments. Therefore, there is a need for robotic solutions that can accomplish IM missions autonomously, a goal that has become more feasible in the last years due to advances in instrumentation, perception and artificial intelligence (AI). In this paper, we present initial experimental results of the Taurob unmanned ground vehicle (UGV) performing autonomous inspection at Equinor's K-Lab Test Centre, located in Haugesund, Norway. The proposed solution employs the simultaneous task planning (STP) algorithm, introduced by Furelos-Blanco in [1], as a high-level action planner. STP can take into account durative high-level actions, such as "visit a specific location", "inspect a sensor", or "take a picture of a specified component" and, in addition, can replan online in case of events such as low battery status, or the need to revisit a location. Communication with the robot is achieved via Equinor's Integration and Supervisory Control of Autonomous Robots (ISAR) framework, which guarantees a fast and secure wi-fi connection. Two types of experiments were carried out: 1) A basic inspection round, which involves visiting a sequence of predefined waypoints; 2) A replanning scenario, which also takes into account the battery status. Our preliminary tests, where fast and efficient plans were computed and executed in real time, including replanning, showed that connecting the guidance and control system of a UGV with a high-level planner like STP is a promising approach to increased autonomy in IM missions.

【文献 203】
作者: Wang, CJ
来源: MACHINES
题目: Innovative Design and Kinematic Characteristics Analysis of Floating
摘要: In view of the problems that exist in the working plane of the inspection robot equipped with precision instruments that cannot always maintain a stable state when moving on a complex road surface, a floating mobile chassis was designed based on the Teoriya Resheniya Izobreatatelskikh Zadatch (TRIZ) theory, and the floating suspension device was also optimized based on the substance field. The kinematic model of the floating mobile chassis was established, and the obstacle-surmounting analysis has been carried out on complex road conditions such as the boss and trench. The dynamic model and mobile performance evaluation model of the obstacle crossing wheel are established. The prototype of the non-floating mobile chassis and the prototype of the floating mobile chassis were respectively established in ADAMS, and the motion comparison simulation analysis of boss, trench crossing and complex road conditions were also carried out. The results showed that the floating mobile chassis has strong adaptive performance, and the stability of the working plane can always be maintained when crossing obstacles.

【文献 204】
作者: Xie, F
来源: 2024 9TH INTERNATIONAL CONFERENCE ON AUTOMATION, CONTROL AND ROBOTICS
题目: An Underwater Defect Instance Segmentation Method for a Bridge Pier
摘要: The safety and stability of bridge piers, which are a crucial element of bridge structures, are of utmost significance due to the rapid advancement in bridge construction. Nevertheless, the intricate and imperceptible nature of the underwater environment makes inspecting the underwater section of bridge piers a challenging task. This study presents the development of a C2f-HG attention-depthwise separable convolution network (CADNet) for accurate identification and evaluation of defects in the underwater structure of bridge piers. The primary components of CADNet's infrastructure are the CADblocks and an SPPF module, which possess the capability of conducting multi-level and deep feature learning. The CADNet is employed in the advanced crawling robot for bridge pier inspection to enable the automated analysis and processing of high-definition underwater images. The advanced bridge pier inspection crawling robot uses the CADNet to automate the analysis and processing of high-definition underwater photos of bridge piers. The system can precisely detect cracks and spalling, effectively separating them from the complicated background. This provides inspectors with clear and easily understandable detection data. In the segmentation and detection tasks, CADNet achieved precision of 79.8% and 77.2% and recall of 78.7% and 76.6%, respectively. The use of the suggested underwater defect instance segmentation technique in the developed bridge pier inspection crawling robot holds significant practical importance and promising application potential.

【文献 205】
作者: Yan, R
来源: COMPUTERS AND ELECTRONICS IN AGRICULTURE
题目: A compact autonomous inspection system for greenhouse environmental
摘要: Facility environmental conditions directly affect the growth and quality of crops. The conventional method of laying multiple sensor nodes is costly and difficult to communicate, while the expensive ground robot monitoring has shortcomings such as universality and interference with agricultural activities. A compact autonomous inspection system for greenhouse environmental information sensing and three-dimensional (3D) visualization was developed, composing of a sensing module, a driver module, and a control and communication interface, to monitor the carbon dioxide concentration, relative humidity, temperature and illumination intensity information at different spatial positions of greenhouse autonomously and automatically. Sensing module connected and controlled by the driver module autonomously moved and acquired environmental information. Meanwhile the control and communication interface, based on Qt and Open Graphics Library (OpenGL) technologies, performed functions of remote controlling, data receiving and visual display. ZigBee, 4G wireless components and Ali Cloud platform cooperated to realize the hierarchical command communication and data transmission among the system. Based on Ultra Wideband (UWB) positioning technology, combined with the trilateral positioning algorithm, the fusion of sensing data and positioning data was realized the 3D visualization, further improving the display dimension of facility environmental information. The practical measurement showed that the sensing module, driven by the driver module, can monitor the facility environmental information at different spatial positions in real time. UWB positioning method realized the accurate positioning of the monitoring device, the average maximum errors of the distance information and height were 0.114 m and 0.012 m, which fully meets the positioning requirements of the facility environmental monitoring. The control and communication interface ran normally, operated friendly and displayed visually. This greenhouse 3D environmental sensing autonomous inspection system can effectively reflect the uneven environmental field of the facility agriculture with low cost and good compatibility, providing effective technical support for the accurate control of the facility environment and the high quality growth of the facility crops, with huge potential for agriculture application.

【文献 206】
作者: Tenniche, N
来源: 12TH INTERNATIONAL CONFERENCE ON SMART GRID, ICSMARTGRID 2024
题目: Nature-Inspired Algorithm Based Trajectory Planning for Inspection
摘要: Developing new trends and technologies for power line inspection is critical for smart grid reliability. Due to the drawbacks of traditional power line methods, such as time consumption, high costs, and risks to worker's safety, innovative technologies like flying robots need to be incorporated. Trajectory planning is crucial for optimizing path and conserving energy during flight, addressing challenges like collision avoidance, real-time planning, dynamic environments, and high-dimensional state spaces, for reliable motion of flying robots in inspection tasks. This study introduces a new trajectory planner for a flying robot, called quadrotor, designed for inspecting power lines within a smart grid infrastructure. The proposed approach utilizes the Water Cycle Algorithm ( WCA) to find the most efficient trajectory within the 3D environment surrounding the power lines. The WCA algorithm emulates the water cycle's dynamic processes, considering path length as an objective function while incorporating constraints such as collision avoidance, velocity limits, non-holonomic constraints, and execution time. The WCA's performance was evaluated against the Firefly Algorithm (FA) and the Particle Swarm Optimization (PSO), demonstrating superior path length minimization and enhancing efficiency for power line inspection in smart grids.

【文献 207】
作者: Afaq, M
来源: 2023 11TH INTERNATIONAL CONFERENCE ON CONTROL, MECHATRONICS AND
题目: A Mobile Inspection Robot Design Analysis in ANSYS Simulation for
摘要: Inspection robots can be deployed in a wide range of environments exhibiting abundance of hazards and extreme conditions which includes environments with chemicals and radiation, strong winds and extreme weather conditions, forest fires and explosions, high pressure and temperature applications, fluid flows, deep sea and space applications, and areas infected with dangerous micro-organisms and diseases. However, Oil and gas industry is the largest one among others with wide- range of challenging on-shore and off-shore inspection applications that demands automation. Moreover, most of the onshore applications necessitate direct human involvement at various levels of the business from oil and gas energy product extraction to distribution. The oil and gas facilities are mostly located in inhospitable environments with extreme hot and cold temperature conditions such as low temperatures up to - 40 degrees C in the northern parts of Canada. In this paper, a proposed mobile inspection robot design composed of materials: IM7/977-2 carbon fiber, HRH-10 Aramid Fiber/ Phenolic Resin Honeycomb, and polycarbonate is tested in a simulation environment to validate their structural integrity against extreme weather phenomenon like; under wind pressures due to wind speed of 40 m/s, extreme temperatures in the range from -60 degrees C to 60 degrees C, and robot body impact with 25 mm and above sized hailstone. Based on the steady-state and stress analysis, the body materials demonstrated excellent resistance against high wind pressures and hailstone impact. Moreover, the combined carbon fiber and aramid material design resulted in insignificant heat conduction and enhanced robustness of the robot body against extreme weather conditions.

【文献 208】
作者: Ortigosa, AR
来源: AEROSPACE
题目: Foundations for Teleoperation and Motion Planning Towards Robot-Assisted
摘要: The aviation industry relies on continuous inspections to ensure infrastructure safety, particularly in confined spaces like aircraft fuel tanks, where human inspections are labor-intensive, risky, and expose workers to hazardous exposures. Robotic systems present a promising alternative to these manual processes but face significant technical and operational challenges, including technological limitations, retraining requirements, and economic constraints. Additionally, existing prototypes often lack open-source documentation, which restricts researchers and developers from replicating setups and building on existing work. This study addresses some of these challenges by proposing a modular, open-source framework for robotic inspection systems that prioritizes simplicity and scalability. The design incorporates a robotic arm and an end-effector equipped with three RGB-D cameras to enhance the inspection process. The primary contribution lies in the development of decentralized software modules that facilitate integration and future advancements, including interfaces for teleoperation and motion planning. Preliminary results indicate that the system offers an intuitive user experience, while also enabling effective 3D reconstruction for visualization. However, improvements in incremental obstacle avoidance and path planning inside the tank interior are still necessary. Nonetheless, the proposed robotic system promises to streamline development efforts, potentially reducing both time and resources for future robotic inspection systems.

【文献 209】
作者: Fang, J
来源: PROCESSES
题目: Gas-Driven Endoscopic Robot for Visual Inspection of Corrosion Defects
摘要: The internal inspection of corrosion in large natural gas pipelines is a fundamental task for the prevention of possible failures. Photos and videos provide direct proof of internal corrosion defects. However, the implementation of this technique is limited by fast robot motion and poor lighting conditions, with high-quality images being key to its success. In this work, we developed a natural gas-driven pipeline endoscopic robot (GDPER) for the visual inspection of the inner wall surfaces of pipelines. GDPER consists of driving, odometer, and vision modules connected by universal joints. It is designed to work in a 154 mm gas-pressurized pipeline up to a maximum of 6 MPa, allowing it to smoothly pass through bends and cross-ring welds at a maximum speed of 3 m/s using gas pressure driving. Test results have shown that HD MP4 video files can be obtained, and the location of defects on the pipelines can be detected by intelligent video image post-processing. The gas-driven function enables the survey of very long pipelines without impacting the transport of the pipage.

【文献 210】
作者: Wang, XH
来源: ELECTRONICS
题目: Research on SLAM and Path Planning Method of Inspection Robot in Complex
摘要: Factory safety inspections are crucial for maintaining a secure production environment. Currently, inspections are predominantly performed manually on a regular basis, leading to low efficiency and a high workload. Utilizing inspection robots can significantly improve the reliability and efficiency of these tasks. The development of robot localization and path planning technologies ensures that factory inspection robots can autonomously complete their missions in complex environments. In response to the application requirements of factory inspections, this paper investigates mapping, localization, and path planning methods for robots. Considering the limitations of cameras and laser sensors due to their inherent characteristics, as well as their varying applicability in different environments, this paper proposes SLAM application systems based on multi-line laser radar and visual perception for diverse scenarios. To address the issue of low efficiency in inspection tasks, a hybrid path planning algorithm that combines the A-star algorithm and time elastic band method is introduced. This approach effectively resolves the problem of path planning becoming trapped in local optima in complex environments, subsequently enhancing the inspection efficiency of robots. Experimental results demonstrate that the designed SLAM and path planning methods can satisfy the inspection requirements of robots in complex scenarios, exhibiting excellent reliability and stability.

【文献 211】
作者: Tang, QY
来源: OCEAN ENGINEERING
题目: Study on the performance of vortex suction cup for an underwater
摘要: Using robots to detect Marine structures is very important for maintaining the safety of Marine structures. This paper presents an underwater inspection robot specifically engineered for the assessment of underwater structures. The integration of reliable adhesion technology is identified as a critical factor in the development of such robots. The vortex suction cup, employed by the robot as its primary adhesion mechanism, outperforms traditional adhesion methods by enabling non-contact attachment while consuming less power. As the vortex suction cup is the core component of the underwater inspection robot, this study comprehensively investigates how variations in the vortex suction cup's structural and operational parameters influence its adhesion performance, measured from an energy efficiency ratio perspective. Initially, we delineate the vortex suction cup's design and succinctly explicate the mechanism underpinning the generation of negative pressure. Subsequently, we establish an evaluative parameter, the energy efficiency ratio (eta = F/P), that is, the adsorption force obtained per watt of power consumption of the suction cup, serving as a metric for the adhesion performance. By simulating the structural and operational parameters of the suction cup, we extract the corresponding adhesion force, torque, and energy efficiency ratio. Lastly, we construct an experimental apparatus to measure the suction cup's adhesion force and output torque, validating the veracity of the simulated outcomes. Our simulation and experimental findings indicate that increasing the height of the suction cup housing enhances the adsorption force, however, this simultaneously diminishes its energy efficiency ratio performance. Likewise, an increase in the chamber inner radius amplifies the adhesion force, and the energy efficiency ratio commensurately increases. Augmenting the radius of the negative pressure effect board improves the adhesion force, decreases the required torque, and consequently amplifies the energy efficiency ratio. The suction cup's adhesion force and required torque display a direct relationship with its speed, but the energy efficiency ratio is minimally affected by speed. When the distance is close (h < 4 mm), changes in the adhesion gap profoundly impact the suction cup's torque and energy efficiency ratio. However, when the adhesion gap ranges between 5-15 mm, the energy efficiency ratio exhibits a gradual decline.

【文献 212】
作者: Nishimura, Y
来源: IEEE ACCESS
题目: Propeller-Type Wall-Climbing Robot for Visual and Hammering Inspection
摘要: Periodic inspections are necessary in maintaining concrete infrastructures. As efficient and low-cost inspection methods, visual inspection robots and hammering inspection robots to detect cracks and internal defects have been developed, respectively. However, these robots can perform only a single task. To realize multiple tasks by a robot, multiple measurement sensors must be installed considering the stability of the robot on the wall and measurement performance. Parameters, such as weight and size, must be considered to ensure the stability of the robot on the wall. We proposed a propeller-type wall-climbing robot that can capture images and perform hammering simultaneously while moving on the wall. The static model of robot was established by considering various parameters, such as the direction of the propeller's thrust force, weight, and placement of the measurement sensors. This model increases the payload for sensor installation and enables movement on dusty concrete walls with a low friction coefficient. The image captured by the inspection robot was employed for crack detection and width estimation. We confirmed that the hammering sounds generated by the robot showed different frequency characteristics on clear and defective areas. The developed inspection robot can collect adequate data for concrete health monitoring.


