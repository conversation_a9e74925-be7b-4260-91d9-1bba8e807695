【文献 213】
作者: <PERSON><PERSON><PERSON>, M
来源: 2023 21ST INTERNATIONAL CONFERENCE ON ADVANCED ROBOTICS, ICAR
题目: Autonomous Exploration and General Visual Inspection of Ship Ballast
摘要: EMIRATES

【文献 214】
作者: <PERSON>, H
来源: JOURNAL OF AGRICULTURAL ENGINEERING
题目: Research on inspection route of hanging environmental robot based on
摘要: Spatial irregularity is a common feature of a closed piggery's environment, and as of right now, there are no established guidelines for where different environmental monitoring sensors should be installed. In order to find environmental monitoring points and guarantee a scientific monitoring point layout, the project team employed the hanging track inspection robot (HTIR) as an environmental monitoring platform. The environmental parameter change rules at 1.6 m (alpha plane), 0.7 m (beta plane), and 0.4 m (gamma plane) above the ground were examined using the Ansys-computational fluid dynamics software. The 300 monitoring points ((x(1)similar to x(30)) x (y(1 similar to)y(10))) in each plane were analyzed to determine the most suitable monitoring points and inspection routes for HTIR. The results showed that: i) all monitoring points could be arranged directly below the y(3) track; ii) monitoring points (x(1), y(3)), (x(10), y(3)), and (x(30), y(3)) were environmental feature points. At (x(1), y(3)), the maximum relative humidity (RH) and NH3 concentration on the alpha plane could be detected, and the maximum wind speed, maximum temperature, and maximum NH3 concentration on other planes could also be detected; at (x(10), y(3)), the minimum temperature and maximum RH of the beta and gamma planes could be detected; at (x(30), y(3)), the maximum NH3 concentration in the alpha plane and the minimum RH in all planes could be detected. This study scientifically arranged the inspection track and monitoring points for HTIR, improved the accuracy of environmental monitoring, and put forward suggestions for reducing NH3 concentration in closed piggeries, laying the foundation for the next step.

【文献 215】
作者: Xu, XJ
来源: INTERNATIONAL JOURNAL OF ROBOTICS & AUTOMATION
题目: APPROACH TO MAGNETIC ACTUATION OF A SOFT INSPECTION ROBOT FOR HVDC
摘要: Currently, the existing high -voltage transmission line inspection robots used in both domestic and foreign research mostly adopt a multi -link rigid structure. However, such designs suffer low obstaclesurmounting efficiency and inadequate safety, making them difficult to be practical. In this paper, a high -voltage magnetostrictive soft inspection robot is designed, utilising the ampere force exerted on the current -carrying coil in a high -voltage direct current annular magnetic field to drive the soft robot to efficiently and flexibly traverse various obstacles in a creeping and compliant manner. The paper conducts theoretical analysis and magnetic field simulation to characterise the annular magnetic fields around different types of obstacles. The design and theoretical calculation of the magnetostrictive model for linear traction force, magnetic adhesion force (tensile force), and adjustment of posture torque are carried out to ensure the driving force theory feasibility. Additionally, force analysis is conducted to examine the motion postures required for crossing different types of obstacles. Simulation and dynamic analysis of the magnetostrictive model prove the theoretical feasibility of the proposed high -voltage magnetostrictive soft inspection robot scheme, laying a foundation for future technological implementation of high -voltage magnetostrictive soft inspection robots.

【文献 216】
作者: Dong, LJ
来源: INDUSTRIAL ROBOT-THE INTERNATIONAL JOURNAL OF ROBOTICS RESEARCH AND
题目: High climbing and obstacle-crossing performance intelligent tracked
摘要: PurposeRegular cable trench inspection is crucial, and robotics automation provides an efficient and safer alternative to manual labor. However, existing robots have limited capabilities in traversing obstacles and lack a mechanical arm for detecting cables and equipment. This study aims to develop an intelligent robot for cable trench inspection, enhancing obstacle-crossing abilities and incorporating a mechanical arm for inspection tasks.Design/methodology/approachThis study presents an intelligent robot for cable trench inspection, featuring a six-degree-of-freedom mechanical arm mounted on a six-track chassis with four flippers. The robot's climbing and obstacle-crossing stability, as well as the motion range of the mechanical arm, are analyzed. The positioning, navigation and remote monitoring systems are developed. Experiments, including climbing and obstacle-crossing performance tests, along with navigation and positioning system tests, are conducted. Finally, the robot's practicability is verified through field testing.FindingsEquipped with flipper tracks, the cable trench inspection robot can traverse obstacles up to 30 cm high and maintain stable locomotion on 30 degrees slopes. Its navigation system enables autonomous operation, while the mechanical arm performs cable current detection tasks. The remote monitoring system provides comprehensive control of the robot and environmental parameter monitoring in cable trenches.Originality/valueThe front and rear flipper tracks enhance the robot's ability to traverse obstacles in cable trenches. The mechanical arm addresses cable current and equipment contact detection issues. The navigation and remote monitoring systems improve the robot's autonomous operation and environmental monitoring capabilities. Implementing this robot can advance the automation and intelligence of cable trench inspections.

【文献 217】
作者: Zhang, TY
来源: 2024 IEEE/RSJ INTERNATIONAL CONFERENCE ON INTELLIGENT ROBOTS AND SYSTEMS
题目: Kinetic-energy-optimal and Safety-guaranteed Trajectory Planning for
摘要: EMIRATES

【文献 218】
作者: Wang, JH
来源: JOURNAL OF MARINE SCIENCE AND ENGINEERING
题目: Navigation and Obstacle Avoidance for USV in Autonomous Buoy Inspection:
摘要: To address the challenges of manual buoy inspection, this study enhances a previously proposed Unmanned Surface Vehicle (USV) inspection system by improving its navigation and obstacle avoidance capabilities using Proximal Policy Optimization (PPO). For improved usability, the entire system adopts a fully end-to-end design, with an angular deviation weighting mechanism for stable circular navigation, a novel image-based radar encoding technique for obstacle perception and a decoupled navigation and obstacle avoidance architecture that splits the complex task into three independently trained modules. Experiments validate that both navigation modules exhibit robustness and generalization capabilities, while the obstacle avoidance module partially achieves International Regulations for Preventing Collisions at Sea (COLREGs)-compliant maneuvers. Further tests in continuous multi-buoy inspection tasks confirm the architecture's effectiveness in integrating these modules to complete the full task.

【文献 219】
作者: Hamilton, C
来源: MATERIALS EVALUATION
题目: ADDRESSING CHALLENGES FOR AUTONOMOUS ROBOTIC FREEFORM EDDY CURRENT
摘要: Eddy current inspection is a critical method for assessing the health of metallic structures, but it requires precise probe placement to avoid signal variations caused by inconsistent liftoff. Robotic systems enable scanning of complex geometries, maintaining stable liftoff and ensuring accurate data collection. This paper presents a robotic eddy current array (ECA) inspection system that operates without CAD models, using computer vision to reconstruct the part's surface for path planning. Inaccuracies in robot calibration and the reconstructed mesh can disrupt the probe's precise positioning, especially in ECA scanning, where probe tilting increases liftoff variability, particularly at greater distances from the tool's center. To address these issues, we introduce a signal processing technique that reduces the impact of mesh inaccuracies and liftoff fluctuations on the acquired ECA data. The system is validated on curved steel samples with corrosion pits, approximately 50 mu m deep and ranging from 1 to 10 mm2 in area.The results demonstrate the system's effectiveness in detecting defects and its potential for integration into the NDE 4.0 framework.

【文献 220】
作者: Li, ZR
来源: REMOTE SENSING
题目: Design and Application of a UAV Autonomous Inspection System for
摘要: As the scale of the power grid continues to expand, the human-based inspection method struggles to meet the needs of efficient grid operation and maintenance. Currently, the existing UAV inspection system in the market generally has short endurance power time, high flight operation requirements, low degree of autonomous flight, low accuracy of intelligent identification, slow generation of inspection reports, and other problems. In view of these shortcomings, this paper designs an intelligent inspection system based on self-developed UAVs, including autonomous planning of inspection paths, sliding film control algorithms, mobile inspection schemes and intelligent fault diagnosis. In the first stage, basic data such as latitude, longitude, altitude, and the length of the cross-arms are obtained from the cloud database of the power grid, while the lateral displacement and vertical displacement during the inspection drone operation are calculated, and the inspection flight path is generated independently according to the inspection type. In the second stage, in order to make the UAV's flight more stable, the reference-model-based sliding mode control algorithm is introduced to improve the control performance. Meanwhile, during flight, the intelligent UAV uploads the captured photos to the cloud in real time. In the third stage, a mobile inspection program is designed in order to improve the inspection efficiency. The transfer of equipment is realized in the process of UAV inspection. Finally, to improve the detection accuracy, a high-precision object detector is designed based on the YOLOX network model, and the improved model increased the mAP0.5:0.95 metric by 2.22 percentage points compared to the original YOLOX_m for bird's nest detection. After a large number of flight verifications, the inspection system designed in this paper greatly improves the efficiency of power inspection, shortens the inspection cycle, reduces the investment cost of inspection manpower and material resources, and successfully fuses the object detection algorithm in the field of high-voltage power transmission lines inspection.

【文献 221】
作者: Gao, CX
来源: AUTOMATION IN CONSTRUCTION
题目: A UAV-based explore-then-exploit system for autonomous indoor facility
摘要: Traditional indoor facility inspections on pipelines and boilers are conducted manually and can be logistically challenging, labor-intensive, costly, and dangerous for the inspectors. With the maturity of unmanned technology, the unmanned aerial vehicle (UAV) is becoming a promising alternative to the problematic manual inspection. However, due to the lack of GPS signal indoor, the localization of UAVs is a big challenge to achieve fully autonomous inspection. Moreover, the narrow and complex indoor environment makes it difficult to guarantee flight safety. This paper presents a UAV-based explore-then-exploit system to tackle these problems for autonomous indoor facility data collection and scene reconstruction. The proposed system consists of a hardware description and integration of two UAVs, a two-step simultaneous localization and mapping (SLAM) method for UAV localization and 3D environmental mapping, a safety-guaranteed coverage path planning algorithm for inspection and data collection, as well as an obstacle-aware trajectory generation method. The proposed system is examined in GPS-denied and cluttered indoor environment and 3D scene reconstruction is conducted. The quantitative analysis shows that the positioning accuracy is centimeter -level and the reconstruction error is within 3 cm. The performance analysis demonstrates the robustness and feasibility of our system in reconstructing and inspecting complex indoor environments for high-efficiency and low-cost facility management.

【文献 222】
作者: Alcoba, MS
来源: 2024 LATIN AMERICAN ROBOTICS SYMPOSIUM, LARS 2024
题目: CuyBot: A Compact and Low-Cost Tracked Robot with vSLAM for Ventilation
摘要: Monitoring ventilation ducts is crucial for ensuring air quality in high-traffic spaces. However, this task presents significant challenges for cleaning staff, as the limited space and extensive length of the ducts make thorough inspection difficult. This article presents the development and evaluation of CuyBot, a compact and economical mobile robot specifically designed for the exploration and inspection of ventilation ducts. CuyBot combines manual control with semi-autonomous navigation to strategic points, optimizing its performance in complex environments. Equipped with a Lidar and integrating data from a stereo camera to generate visual odometry, the robot utilizes this information to implement Visual Simultaneous Localization and Mapping (vSLAM), enabling it to navigate and create 2D maps of the spaces. CuyBot's capabilities were rigorously evaluated through a phased approach that included the application of vSLAM in simulated environments, testing in physical replicas of ducts, and final validation in a real ventilation duct. The results demonstrate that CuyBot navigates effectively in these challenging environments.

【文献 223】
作者: Du, ZX
来源: INTERNATIONAL JOURNAL OF LOW-CARBON TECHNOLOGIES
题目: Path planning of substation inspection robot based on high-precision
摘要: Outdoor substation is an important part of power system. Substation inspection robot based on intelligent autonomous inspection system has become the research focus of substation unmanned inspection. In order to improve the positioning accuracy and speed of the system, a high-precision positioning algorithm of transformer detection robot is proposed in this paper. Tikhonov regularization is used to correct the pathological problem of the localization algorithm model. The observation amount of the receiver is increased by using four signals of a single base station with double frequency and double antenna, and the position is solved by using single difference carrier phase observation and the integer ambiguity is fixed. The input-output mapping of the neural network is designed according to the information acquisition and two-wheel angular velocity control of the detection robot. Using the hyperbolic tangent function as the activation function of MLP neural network, the MLP neural network with 32 neurons in each of the three hidden layers is determined. By optimizing reinforcement learning reward function, adding scoring rules, and reward parameters, this paper carries out the following simulation exploration work. The high-precision positioning algorithm of transformer inspection robot is compared with the existing algorithm, and the superiority of the algorithm is verified. The basic motion ability of the robot installed with the system was tested.

【文献 224】
作者: Mannan, F
来源: AIAA AVIATION FORUM AND ASCEND 2024
题目: Sustainable and Portable Vertiports Enabling Autonomous Drone Swarm
摘要: The aim of this paper is to showcase the work that has been done and the planned path that the STEER (Sustainable verTiport framework for autonomous dronE swarm methanE emission measurement over oRphaned wells) project will take. The goal of STEER is to develop an autonomous system that is isolated and assists in locating hard to reach orphaned wells. Using drones is a cost-effective method in locating and plugging up these wells to reduce the emissions of greenhouse gasses into the atmosphere. This system will need to detect methane (CH4) to find abandoned wells out in the environment. The system will consist of a vertiport system that will be bio inspired from trees and the drone swarm that will head out to do surveys to find orphaned wells. The vertiport will house and charge the drones via contact pads, but will also ensure that the environment is safe to fly in before sending the drone off on a mission. The drones themselves will need to be driven by a ROS software making it possible to autonomously take off, survey, then return to the vertiport for landing.

【文献 225】
作者: Xu, ZF
来源: IEEE ROBOTICS AND AUTOMATION LETTERS
题目: A Vision-Based Autonomous UAV Inspection Framework for Unknown Tunnel
摘要: Tunnel construction using the drill-and-blast method requires the 3D measurement of the excavation front to evaluate underbreak locations. Considering the inspection and measurement task's safety, cost, and efficiency, deploying lightweight autonomous robots, such as unmanned aerial vehicles (UAV), becomes more necessary and popular. Most of the previous works use a prior map for inspection viewpoint determination and do not consider dynamic obstacles. To maximally increase the level of autonomy, this letter proposes a vision-based UAV inspection framework for dynamic tunnel environments without using a prior map. Our approach utilizes a hierarchical planning scheme, decomposing the inspection problem into different levels. The high-level decision maker first determines the task for the robot and generates the target point. Then, the mid-level path planner finds the waypoint path and optimizes the collision-free static trajectory. Finally, the static trajectory will be fed into the low-level local planner to avoid dynamic obstacles and navigate to the target point. Besides, our framework contains a novel dynamic map module that can simultaneously track dynamic obstacles and represent static obstacles based on an RGB-D camera. After inspection, the Structure-from-Motion (SfM) pipeline is applied to generate the 3D shape of the target. To our best knowledge, this is the first time autonomous inspection has been realized in unknown and dynamic tunnel environments. Our flight experiments in a real tunnel prove that our method can autonomously inspect the tunnel excavation front surface.

【文献 226】
作者: González-Morgado, A
来源: 2023 INTERNATIONAL CONFERENCE ON UNMANNED AIRCRAFT SYSTEMS, ICUAS
题目: Fully-actuated, corner contact aerial robot for inspection of
摘要: This paper presents the design and development of a fully-actuated platform for the visual inspection of difficult-to-reach areas of bridges, such as bridge beams and bearings. The aerial platform incorporates a carbon fiber structure with spherical wheels that facilitates safe contact with the bridge, while the fully-actuated configuration allows the movement of the platform while keeping the contact with the surface. In addition, the system mounts a camera that allows an operator to supervise the inspection from the ground. Compared to other solutions developed for bridge inspection, our solution is able to maintain contact while moving across the inspection surface and is even capable of maintaining contact with two surfaces in corners, allowing the inspection of difficult-to-reach zones like bridge bearings. We describe the design and build process, the dynamic model and the control of the system proposed. We show the capabilities of the fully-actuated platform by indoor flights, while the proposed aerial platform is tested in a real scenario, for bridge beams and bearings inspection. The results of field tests demonstrate the feasibility and effectiveness of the proposed platform for bridge inspections.

【文献 227】
作者: Chiu, D
来源: 2024 IEEE INTERNATIONAL CONFERENCE ON ROBOTICS AND AUTOMATION, ICRA 2024
题目: Optimization and Evaluation of a Multi Robot Surface Inspection Task
摘要: Robot swarms can be tasked with a variety of automated sensing and inspection applications in aerial, aquatic, and surface environments. In this paper, we study a simplified two-outcome surface inspection task. We task a group of robots to inspect and collectively classify a 2D surface section based on a binary pattern projected on the surface. We use a decentralized Bayesian decision-making algorithm and deploy a swarm of 3-cm sized wheeled robots to inspect a randomized black and white tiled surface section of size 1mx1m in simulation. We first describe the model parameters that characterize our simulated environment, the robot swarm, and the inspection algorithm. We then employ a noise-resistant heuristic optimization scheme based on the Particle Swarm Optimization (PSO) using a fitness evaluation that combines the swarm's classification decision accuracy and decision time. We use our fitness measure definition to asses the optimized parameters through 100 randomized simulations that vary surface pattern and initial robot poses. The optimized algorithm parameters show up to 55% improvement in median of fitness evaluations against an empirically chosen parameter set.

【文献 228】
作者: Yang, BY
来源: IEEE SENSORS JOURNAL
题目: Sensor Fusion-Based UAV Localization System for Low-Cost Autonomous
摘要: Utilizing the unmanned aerial vehicle (UAV) for autonomous inspection in extremely confined environments has become a much sought research area, due to the pressing industrial needs. To provide the high-accuracy position information of UAV in such environments, the ultrawideband (UWB)-based localization technology has been one of the ideal candidates. However, the unpredictable propagation condition and the geomagnetic disturbances for inertial measurement unit (IMU) will all have huge impact on the positioning performance with the single UWB and the IMU/UWB-based loosely coupled (LC) sensor fusion approach. Therefore, a tightly coupled adaptive extended Kalman filter (TC-AEKF)-based sensor fusion UAV localization system is proposed and developed in this article for autonomous inspection in extremely confined environments. The proposed system can attain high-accuracy localization of UAV with 0.097-m median error, 0.167-m 95th percentile error, and 0.039-m average standard deviation (STD). The drift led by the geomagnetic disturbances for IMU is greatly reduced with the estimation error of the roll, pitch, and yaw angle to be 2.15 degrees, 1.54 degrees, and 4.58 degrees, respectively.

【文献 229】
作者: Qi, F
来源: INDUSTRIAL ROBOT-THE INTERNATIONAL JOURNAL OF ROBOTICS RESEARCH AND
题目: Design and analysis of a cable-driven continuum robot with used in
摘要: PurposeThis paper aims to present a kinematics performance analysis and control for a continuum robot based on a dynamic model to achieve control of the robot.Design/methodology/approachTo analyze the motion characteristics of the robot, its kinematics model is derived by the geometric analysis method, and the influence of the configuration parameters of the robot on workspace is investigated. Moreover, the dynamic model is established by the principle of virtual work to analyze the mapping relationship among the bending shape, the forces/torques applied to the robot. To achieve better control of the robot, a control strategy for continuum robot based on the dynamic model is put forward.FindingsResults of the simulations and experiments verify the proposed continuum structure and motion model, the maximum position error is 5.36 mm when the robot performs planar bending motion and the average position error of the robot in spatial circular motion is 5.84 mm. The proposed model can accurately describe the deformation movement of the robot and realize its motion control with a few position errors.Originality/valueThe kinematics analysis and control model proposed in this paper can achieve precise control of the robot, which can be used as a reference for the motion planning and shape reconstruction of continuum robot.

【文献 230】
作者: Shang, DY
来源: TRANSACTIONS OF THE CANADIAN SOCIETY FOR MECHANICAL ENGINEERING
题目: Speed control strategy for power line inspection robot servo system
摘要: Most servo systems in power line inspection robots consist of a motor, an independent joint, and a load. In the process of crossing obstacles, the parameters in the servo systems have conspicuous time-varying properties due to the posture changes. The time-varying properties of dynamic parameters and the flexibility of the load would cause the rotation speed of the inspection robot to fluctuate, thereby affecting the motion accuracy. In this paper, the pole placement strategy was proposed to optimize the parameters in the proportional integral (PI) controller. The optimal controller parameters were selected in different postures to ensure steady speed output in the inspection robot servo system. First, the dynamic equations of the inspection robot servo system were established. Both joint flexibility and load flexibility were considered in the modeling process. Then, the Arnoldi algorithm was used to reduce the order of the servo system, and the transfer function from the speed to the drive torque was obtained. Next, the controller parameters were optimized using the pole placement method. By reasonably selecting the pole damping coefficient, the inspection robot could obtain a stable speed output. Finally, the numerical analysis and speed control of the inspection robot in different postures were analyzed. The results showed that the control strategy of pole placement could achieve a stable rotation speed for the inspection robot.

【文献 231】
作者: Tian, SX
来源: MEASUREMENT SCIENCE AND TECHNOLOGY
题目: Gaussian process regression based inspection robot for predicting and
摘要: The direct current voltage gradient (DCVG) technology is adept at identifying defects and corrosion issues within the anti-corrosion layer of buried pipelines by measuring changes in voltage gradient above the ground. Its widespread adoption in the field of anti-corrosion layer defect detection for its high precision and accuracy. However, the current DCVG inspection process relies on experienced operators holding electrodes to walk along the pipeline, resulting in a huge workload. To address these challenges, this paper proposes an innovative method that combines Gaussian process regression (GPR) with an intelligent inspection robot for autonomous pipeline anti-corrosion coating defect detection. This method uses environmental data to directly predict the location of defects within a pipeline's anti-corrosion coating. Through incremental learning, the GPR model is trained to be continuously updated based on new samples such as position coordinates and voltage measurements during autonomous inspections. In addition, the intelligent inspection robot operates collaboratively with crawler wheels and UR robotic arms, enhancing motion stability and flexibility in expanding training data sets. Experimental results confirm that the intelligent inspection robot driven by Gaussian process prediction can achieve accurate defect positioning within 25 iteration cycles, with a positioning accuracy within 0.12 m. This method enhances defect detection accuracy, alleviating operator burden and offering an efficient solution for buried pipeline maintenance.

【文献 232】
作者: Ma, LY
来源: SCIENTIFIC REPORTS
题目: Exploration of using a wall-climbing robot system for indoor inspection
摘要: Indoor inspection robots operating in occupied buildings need to minimize disturbance to occupants and access high areas of a room and cramped spaces obstructed by obstacles for higher inspection coverage. However, existing indoor inspection robots are still unable to meet these requirements. This paper aims to explore the feasibility of applying wall-climbing robots to address these requirements. To this end, we propose a small-sized wall-climbing robot prototype that can move on common indoor surfaces. We extend the proposed prototype to support thermographic inspection by integrating thermal imaging technology into it. Experiment results show that the proposed robot prototype can reach more wall and floor areas for inspection than previously developed indoor inspection robots. It has also been demonstrated that the reduced size and the wall-climbing ability allow the robot to largely avoid human activity areas, thereby reducing disturbance to occupants. This study represents the first attempt to introduce wall-climbing robots into the indoor inspection domain and provides the initial validation of their advantages over existing indoor inspection robots regarding improving inspection coverage and minimizing disturbance to occupants. The findings in this study can provide valuable insights for the future design, selection and application of robotic systems for indoor inspection tasks.

【文献 233】
作者: Cheng, YQ
来源: IEEE TRANSACTIONS ON INSTRUMENTATION AND MEASUREMENT
题目: MVGR: Mean-Variance Minimization Global Registration Method for
摘要: The stitching and fusing of point clouds are indispensable for multiview 3-D measurement in robot inspection. Due to the positioning deviation of the robot, the point clouds collected by various poses inevitably have uneven density and stratification, leading to a significant compromise in the overall quality of multiview point clouds. This article proposes a mean-variance minimization global registration (MVGR) method, and the key idea is that the objective function is defined as the weighting of distance and variance between corresponding points. The constraint of variance helps mitigate the problem of uneven density. The perturbation model of Lie algebra se3 is utilized to derive the Jacobian matrix in the global optimization process of multiview point cloud poses. The iterative solution is obtained by the Newton-like iterative method. Furthermore, a point cloud slicing method is proposed to accelerate the search for corresponding points between point clouds. To demonstrate the superiority of MVGR, experiments are carried out and indicated that the point clouds registered by MVGR have minimum deviation compared with the previous SOTA methods, and the speed of searching for corresponding points can be enhanced by 50 times using a reasonable number of slices.

【文献 234】
作者: Yuan, R
来源: 2024 10TH INTERNATIONAL CONFERENCE ON ELECTRICAL ENGINEERING, CONTROL
题目: Research and Implementation of Pointer Instrument Reading Recognition
摘要: Instrument recognition is the core task of the equipment inspection subsystem, where the robot equipped with a camera sensor captures images of pointer-style instruments and returns them for further processing. This paper investigates the practical problem of accurately processing instrument images to obtain target instrument dial data. We propose a method for pointer-style instrument reading recognition based on the combination of YOLOv5 and U-2-Net. The YOLOv5 model is used to train and perform object detection to extract and locate the instrument dial region. The U-2-Net model is employed to obtain the scale and pointer positions within the dial, and the reading is calculated based on their relative positions. The whole instrument identification process is communicated to the outside world through the ROS message mechanism and Web API interface. Through this method, the equipment inspection subsystem can accurately solve the pointer dashboard reading problem, can greatly improve inspection efficiency, and can meet the daily inspection work of the environmental robot in the power plant.

【文献 235】
作者: Xu, HF
来源: 2023 IEEE/RSJ INTERNATIONAL CONFERENCE ON INTELLIGENT ROBOTS AND
题目: Design and Development of a Deformable In-pipe Inspection Robot for
摘要: Pipelines have become one of the most important infrastructures in the city. Over time, they are prone to aging, cracks, corrosion, and the demand for regular inspection is gradually increasing. Robotic solutions are effective methods for in-pipe inspection. However, existing In-pipe Inspection Robots (IPIR) require that the inner diameter of the pipe is fixed in the application scenarios, and need extra labor to control the robot and handle the cable. In this work, we design and develop a deformable robot to adapt to pipes with different inner diameters. Specifically, the passive elastic hinge is used by us to make the robot fully in contact with the pipe, generating enough friction to ensure that the robot is attached to the inner wall of the pipe. An edge device is deployed on the robot, generating velocity commands of wheels through the data from Inertial Measurement Unit (IMU), which eliminates the need for external devices. Experimental results demonstrate that the robot can move in horizontal and vertical pipelines, as well as traverse through pipe joints and scenarios where there is dirty or small obstacle.

【文献 236】
作者: Lee, J
来源: 2024 IEEE INTERNATIONAL CONFERENCE ON ROBOTICS AND AUTOMATION, ICRA 2024
题目: Safety-critical Control of Quadrupedal Robots with Rolling Arms for
摘要: This paper presents a safety-critical control framework tailored for quadruped robots equipped with a roller arm, particularly when performing locomotive tasks such as autonomous robotic inspection in complex, multi-tiered environments. In this study, we consider the problem of operating a quadrupedal robot in distillation columns, locomoting on column trays and transitioning between these trays with a roller arm. To address this problem, our framework encompasses the following key elements: 1) Trajectory generation for seamless transitions between columns, 2) Foothold re-planning in regions deemed unsafe, 3) Safety-critical control incorporating control barrier functions, 4) Gait transitions based on safety levels, and 5) A low-level controller. Our comprehensive framework, comprising these components, enables autonomous and safe locomotion across multiple layers. We incorporate reduced-order and full-body models to ensure safety, integrating safety-critical control and footstep re-planning approaches. We validate the effectiveness of our proposed framework through practical experiments involving a quadruped robot equipped with a roller arm, successfully navigating and transitioning between different levels within the column tray structure.

【文献 237】
作者: Sun, XY
来源: 2023 9TH INTERNATIONAL CONFERENCE ON MECHATRONICS AND ROBOTICS
题目: A Positioning Method Based on Kalman Filter for FAST Feed Support Cable
摘要: In this paper, a positioning system developed for cable inspection robot system which works on feed support cables of Five-hundred-meter Aperture Spherical radio Telescope (FAST) is introduced. The positioning system consists of wheeled odometers and a Real-tme kinematic (RTK) based Global Navigation Satellite System (GNSS). Firstly, the cable with fixed ends and free suspension in the middle is modeled, and the influence of wind on the cable in FAST site is analyzed. Then, the data of the four odometers on the robot are processed, and the odometer information is fused with GNSS information through Kalman filtering. Then the best position estimation of the cable inspection robot is determined. A simulation is finally established to verify the proposed robot positioning method. The result shows that the method can work pretty well under the real working situation.

【文献 238】
作者: Chakraa, H
来源: IEEE ACCESS
题目: A Centralized Task Allocation Algorithm for a Multi-Robot Inspection
摘要: Recently, considerable attention has focused on enhancing the security and safety of industries with high-risk level activities in order to protect the equipment and environment. In particular, chemical processes and nuclear power generation may have a deep impact on their surroundings. In the case of major events, such as chemical spills, oil rig explosions, or nuclear accidents, collecting accurate and rapidly evolving data becomes a challenging task. So, coordinating a fleet of autonomous mobile robots is a very promising way to deal with unpredicted events and also prevent malicious actions. This paper addresses the problem of assigning optimally a set of tasks to a set of mobile robots equipped with different sensors to minimize a global objective function. The robots perform sensing tasks in order to monitor the area and to facilitate firefighters and inspectors work if a disaster occurs by providing the necessary measures. For this purpose, a centralized Genetic Algorithm (GA) is proposed to determine the task each robot will perform and the order of execution. The proposed approach is tested through a simulation scenario of a grid map environment that represents an industrial area of the city of Le Havre, France. Moreover, a comparative study is conducted with the Hybrid Filtered Beam Search (HFBS) approach and the Mixed-Integer Linear Programming (MILP) solver Cplex. The results demonstrate that the GA approach offers a favorable balance between optimality and execution time.

【文献 239】
作者: Lin, LL
来源: SCIENTIFIC REPORTS
题目: Multi-scene application of intelligent inspection robot based on
摘要: As industries develop, the automation and intelligence level of power plants is constantly improving, and the application of patrol robots is also increasingly widespread. This research combines computer vision technology and particle swarm optimization algorithm to build an obstacle recognition model and obstacle avoidance model of an intelligent patrol robot in a power plant respectively. Firstly, the traditional convolutional recurrent neural network is optimized, and the obstacle recognition model of an intelligent patrol robot is built by combining the connection timing classification algorithm. Then, the artificial potential field method optimizes the traditional particle swarm optimization algorithm, and an obstacle avoidance model of an intelligent patrol robot is built. The performance of the two models was tested, and it was found that the highest precision, recall, and F1 values of the identification model were 0.978, 0.974, and 0.975. The highest precision, recall, and F1 values of the obstacle avoidance model were 0.97, 0.96, and 0.96 respectively. The two optimization models designed in this research have better performance. In conclusion, the two models in this study are superior to the traditional methods in recognition effect and obstacle avoidance efficiency, providing an effective technical scheme for intelligent patrol inspection of power plants.

【文献 240】
作者: O'Brien, R
来源: 2024 IEEE/RSJ INTERNATIONAL CONFERENCE ON INTELLIGENT ROBOTS AND
题目: An Autonomous, 3D Printed, Waterjet-Powered, Open-Source Robotic
摘要: EMIRATES

【文献 241】
作者: Zhang, W
来源: MEASUREMENT & CONTROL
题目: Improved A* and DWA fusion algorithm based path planning for intelligent
摘要: To solve the intelligent substation inspection robot path planning with low global efficiency, search node redundancy, and may even fail under a dynamic obstacle environment, which is normally based on the A* or dynamic window approach (DWA) algorithms. This study attempted to use the improved A* algorithm and an enhanced DWA algorithms for intelligent substation inspection robot path planning to improve its path planning ability under dynamic inspection. In this study, The neighborhood traversal rule of the A* is refined, and the DWA evaluation function is adjusted to align with the specific demands of intelligent substation inspection. Simulation results demonstrate that combining the improved A* algorithm with the enhanced DWA significantly reduces the inspection path length by 24.4% compared to traditional A* in fixed point inspection condition. This integration greatly enhances the dynamic path-planning performance of substation inspection robots, particularly in terms of path smoothness and inspection efficiency.

【文献 242】
作者: Liu, T
来源: 2024 IEEE 7TH INTERNATIONAL CONFERENCE ON AUTOMATION, ELECTRONICS AND
题目: Research on Large Angle Climbing Intelligent Inspection Rail Robot
摘要: This paper proposes a new type of corridor climbing inspection robot, aimed at solving the problems of insufficient power and poor stability of existing robots in complex terrain and steep slope environments. The robot adopts a design that combines ground rails with linear transmission bodies, optimizes the automatic walking drive device and multi axis displacement robotic arm, and achieves stable inspection with a maximum climbing angle of over 60 degrees. The robot is equipped with a high-resolution image acquisition device and a supplementary light source, combined with image processing and machine learning algorithms, to automatically identify and classify defects and anomalies during inspection. The experimental results show that the robot does not derail or deviate when climbing a 60 degrees slope, with a minimum turning radius of 1.5 meters, image resolution of 1080p, battery life of 8 hours, stable data transmission, automatic recognition accuracy of over 95%, and maintains stable performance after continuous operation for 1000 hours.

【文献 243】
作者: Halder, S
来源: JOURNAL OF BUILDING ENGINEERING
题目: Construction inspection & monitoring with quadruped robots in future
摘要: Construction inspection and monitoring are key activities in construction projects. Automation of inspection tasks can address existing limitations and inefficiencies of the manual process to en-able systematic and consistent construction inspection. However, there is a lack of an in-depth understanding of the process of construction inspection and monitoring and the tasks and se-quences involved to provide the basis for task delegation in a human-technology partnership. The purpose of this research is to study the conventional process of inspection and monitoring of con-struction work currently implemented in construction projects and to develop an alternative process using a quadruped robot as an inspector assistant to overcome the limitations of the con-ventional process. This paper explores the use of quadruped robots for construction inspection and monitoring with an emphasis on a human-robot teaming approach. Technical development and testing of the robotic technology are not in the scope of this study. The results indicate how inspector assistant quadruped robots can enable a human-technology partnership in future con-struction inspection and monitoring tasks. The research was conducted through on-site experi-ments and observations of inspectors during construction inspection and monitoring followed by a semi-structured interview to develop a process map of the conventional construction inspection and monitoring process. The study also includes on-site robot training and experiments with the inspectors to develop an alternative process map to depict future construction inspection and monitoring work with the use of an inspector assistant quadruped robot. Both the conventional and alternative process maps were validated through interview surveys with industry experts against four criteria including, completeness, accuracy, generalizability, and comprehensibility. The findings suggest that the developed process maps reflect existing and future construction in-spection and monitoring work.

【文献 244】
作者: Chen, ZY
来源: AUTOMATION IN CONSTRUCTION
题目: Automated reality capture for indoor inspection using BIM and a
摘要: This paper presents a real-time, cost-effective navigation and localization framework tailored for quadruped robot-based indoor inspections. A 4D Building Information Model is utilized to generate a navigation map, supporting robotic pose initialization and path planning. The framework integrates a cost-effective, multi-sensor SLAM system that combines inertial-corrected 2D laser scans with fused laser and visual-inertial SLAM. Additionally, a deep-learning-based object recognition model is trained for multi-dimensional reality capture, enhancing comprehensive indoor element inspection. Validated on a quadruped robot equipped with an RGB-D camera, IMU, and 2D LiDAR in an academic setting, the framework achieved collision-free navigation, reduced localization drift by 71.77 % compared to traditional SLAM methods, and provided accurate large-scale point cloud reconstruction with 0.119-m precision. Furthermore, the object detection model attained mean average precision scores of 73.7 % for 2D detection and 62.9 % for 3D detection.

【文献 245】
作者: Foster, AJI
来源: DRONES
题目: Multi-Robot Coverage Path Planning for the Inspection of Offshore Wind
摘要: Offshore wind turbine (OWT) inspection research is receiving increasing interest as the sector grows worldwide. Wind farms are far from emergency services and experience extreme weather and winds. This hazardous environment lends itself to unmanned approaches, reducing human exposure to risk. Increasing automation in inspections can reduce human effort and financial costs. Despite the benefits, research on automating inspection is sparse. This work proposes that OWT inspection can be described as a multi-robot coverage path planning problem. Reviews of multi-robot coverage exist, but to the best of our knowledge, none captures the domain-specific aspects of an OWT inspection. In this paper, we present a review on the current state of the art of multi-robot coverage to identify gaps in research relating to coverage for OWT inspection. To perform a qualitative study, the PICo (population, intervention, and context) framework was used. The retrieved works are analysed according to three aspects of coverage approaches: environmental modelling, decision making, and coordination. Based on the reviewed studies and the conducted analysis, candidate approaches are proposed for the structural coverage of an OWT. Future research should involve the adaptation of voxel-based ray-tracing pose generation to UAVs and exploration, applying semantic labels to tasks to facilitate heterogeneous coverage and semantic online task decomposition to identify the coverage target during the run time.

【文献 246】
作者: Yang, AH
来源: PROCEEDINGS OF 2024 3RD INTERNATIONAL CONFERENCE ON FRONTIERS OF
题目: Inspection Robot Fault Detection Based on Electric Power Big Data and
摘要: The growing scale and complexity of the power system make the traditional inspection methods unable to meet the demand for timely detection and treatment of power equipment faults. To improve the efficiency of power equipment fault detection, this study designs an inspection robot fault detection model based on power big data and target detection algorithms. The model is based on the prediction frame to accurately locate the detection object and feature extraction, and the study also introduces the K-mean algorithm to scale the size of the prediction frame, so as to extract the target features more accurately to achieve fault detection. Experimentally, the loss rate of the proposed model is only 0.8% on the open-source dataset, and the precision and recall curves are also excellent. The model also achieves more accurate clustering of the prediction frame parameters in the clustering effect prediction. The experimental results verified that the model research has achieved preliminary results.

【文献 247】
作者: Miao, TY
来源: 2024 INTERNATIONAL CONFERENCE ON ADVANCED ROBOTICS AND MECHATRONICS,
题目: Design and Development of a Ventilation Pipe Inspection Robot Operating
摘要: Ventilation pipes are an important way of supplying fresh air to a room. However, there are cases where corrosion and dirt accumulation require inspection of the inside of the pipes. The development of pipeline inspection robots is an effective solution, but for the inspection task of narrow metal ventilation pipes, the existing pipeline robots are limited in size, kinematic capability and perception. In this study, a miniaturised pipeline robot is proposed and developed with a dual articulated arm crawler as the basic kinematic mechanism, which has the ability to cross obstacles, climb slopes and adapt to diameter changes in narrow pipelines. The pipeline robot sensing system is designed in a modular way, using the vanishing point and camera projection model to estimate the position of the robot in a straight metal pipe environment, and analysing the characteristics of the ventilation pipe system to construct a scalable pipeline topology map based on a weighted undirected graph. Finally, a ventilation pipe scene is constructed to conduct experiments on the pipeline robot. The results show that the designed pipeline robot can move flexibly in a narrow metal pipe environment, complete localization within the pipe, and construct pipe maps.

【文献 248】
作者: Degirmenci, E
来源: TOWARDS AUTONOMOUS ROBOTIC SYSTEMS, TAROS 2023
题目: Developing an Integrated Runtime Verification for Safety and Security of
摘要: Robotic systems are increasingly integrated into various industries, including manufacturing, transportation, and healthcare. So, it is essential to identify the vulnerabilities of these systems and take precautions. These systems are vulnerable to cyber-attacks compromising their safety and operations. In this study, we developed an integrated runtime verification for the safety and security of an industrial robot inspection system. Runtime verification is a lightweight technique that involves evaluating the behaviour of a system at runtime. The developed runtime verification system is named MARVer. In the experiments, firstly, the runtime verification is independently for safety and security using MARVer-R. Then, integrated runtime verification is realized to monitor the effects of security attacks on safety. The experiments are evaluated in a TRL5 laboratory environment designed for quality inspection of automotive-body-in-white. Our study highlights the importance of verifying safety and security at runtime.

【文献 249】
作者: Jafari-Tabrizi, A
来源: INTERNATIONAL JOURNAL OF ADVANCED MANUFACTURING TECHNOLOGY
题目: Exploiting image quality measure for automatic trajectory generation in
摘要: Currently, the standard method of programming industrial robots is to perform it manually, which is cumbersome and time-consuming. Thus, it can be a burden for the flexibility of inspection systems when a new component with a different design needs to be inspected. Therefore, developing a way to automate the task of generating a robotic trajectory offers a substantial improvement in the field of automated manufacturing and quality inspection. This paper proposes and evaluates a methodology for automatizing the process of scanning a 3D surface for the purpose of quality inspection using only visual feedback. The paper is divided into three sub-tasks in the same general setting: (1) autonomously finding the optimal distance of the camera on the robot's end-effector from the surface, (2) autonomously generating a trajectory to scan an unknown surface, and (3) autonomous localization and scan of a surface with a known shape, but with an unknown position. The novelty of this work lies in the application that only uses visual feedback, through the image focus measure, for determination and optimization of the motion. This reduces the complexity and the cost of such a setup. The methods developed have been tested in simulation and in real-world experiments and it was possible to obtain a precision in the optimal pose of the robot under 1 mm in translational, and 0.1 degrees \documentclass[12pt]{minimal} \usepackage{amsmath} \usepackage{wasysym} \usepackage{amsfonts} \usepackage{amssymb} \usepackage{amsbsy} \usepackage{mathrsfs} \usepackage{upgreek} \setlength{\oddsidemargin}{-69pt} \begin{document}$$<^>\circ $$\end{document} in angular directions. It took less than 50 iterations to generate a trajectory for scanning an unknown free-form surface. Finally, with less than 30 iterations during the experiments it was possible to localize the position of the surface. Overall, the results of the proposed methodologies show that they can bring substantial improvement to the task of automatic motion generation for visual quality inspection.

【文献 250】
作者: Magdy, M
来源: 2024 5TH INTERNATIONAL CONFERENCE ON ARTIFICIAL INTELLIGENCE, ROBOTICS
题目: Analysis of the Design Parameters of a Climbing Robot for Wind Turbine
摘要: Wind energy is becoming one of the rising sources of energy with the global shift towards renewable and clean energy. One of the challenges of wind energy is the periodic inspection of wind turbines. Given the high safety hazard of performing this process by technicians, this paper addresses the analysis of design parameters of a climbing robot purposed for inspection of wind turbines that can be controlled remotely to reduce the necessity of having personnel in critical safety conditions. The preliminary design analysis enables the robot to climb the wind turbine tower by means of four wheels and adhesion force provided through the tension of two ropes or screw, each driven by a stepper motor. A study of the acting forces and the required components was performed. The weight of the proposed prototype would weigh around 9 Kg, about 15% of which could be allocated for testing equipment; for example, ultra-sonic transducers as well as the controller that controls the system and be controlled remotely.

【文献 251】
作者: Yang, Q
来源: INTERNATIONAL JOURNAL OF ADVANCED ROBOTIC SYSTEMS
题目: Real-time marine target recognition method of underwater inspection
摘要: In the complex marine environment, target recognition is difficult, and the real-time detection has a slow speed. In this article, a target recognition method combining underwater generative adversarial network and improved YOLOv4 is proposed, which is named M-YOLOv4. Firstly, the images collected by the underwater inspection robot are enhanced using the underwater generative adversarial network algorithm to obtain the training datasets. Secondly, the YOLOv4 target detection algorithm combines the feature extraction network of MoblieNetv3 for lightweight processing, which reduces the network model size, and reduces the number of algorithm calculations and parameters. Then, change the size of the spatial pyramid pooling module pooling kernel, which can enlarge receptive field and integrate characteristics of different receptive fields. Finally, the processed datasets are transferred to the improved M-YOLOv4 algorithm for training, and the trained model is transplanted to the Jetson Nano hardware device for real-time detection. The results of experiments show that the mean average precision value of the improved M-YOLOv4 recognition is 90.77%, which is 2.02% higher than that of the unimproved one. The frame per second value of the lightweight YOLOv4 algorithm with MobileNetv3 is 27, an increase of 12 compared with YOLOv4. The improved M-YOLOv4 algorithm can perform accurate detection of marine multi-targets on embedded devices.

【文献 252】
作者: Chang, SW
来源: JOURNAL OF COMPUTING IN CIVIL ENGINEERING
题目: Development of a Fuzzy Logic Controller for Autonomous Navigation of
摘要: Robotic building inspection is gaining popularity as a way to increase the security, productivity, and cost-effectiveness of traditional inspection tasks. Despite the development of numerous building inspection robotic platforms, their motions still require manual control. To facilitate full automation, there is a need to explore autonomous navigation strategies for building inspection robots. Although different autonomous navigation strategies have been developed in the robotics field, few of them are suitable for building structural inspection behavior. In accordance with the responsibilities of professional inspectors, the robot is required to follow the structural components within a desired distance and dynamically avoid obstacles to conduct in-depth scanning. This navigation task becomes more difficult when providing a smooth following path in special building scenarios, such as narrow corners. Motivated by this need, the present study aimed to explore autonomous navigation for building inspection robots. To save the cost of map construction, local navigation strategies, which control the robots' travel in unknown environments, were targeted. Specifically, the objective is to develop a robust fuzzy logic controller (FLC) for wall-following behavior. The inputs are the distances within the designed interval ranges, which were measured with a 360 degrees laser. The membership functions and the decision-making rules were designed based on robot and camera configurations, building designs, and structural inspection criteria. The outputs are the real-time angular and linear velocities. Tested in both simulation and real-world environments, the proposed FLC is able to (1) find the wall, follow the wall, conduct self-turning, and avoid obstacles in unknown building scenarios, (2) prevent wavy motions, and (3) prevent path deviations for arbitrary surfaces. The results can be employed to perform daily building inspection featuring autonomous navigation. In conclusion, the limitations of FLC are given for future study.

【文献 253】
作者: Rad, SS
来源: 2023 IEEE TRANSPORTATION ELECTRIFICATION CONFERENCE & EXPO, ITEC
题目: Experimental Study of Magnetic Field Effect on Multi-Rotor Drones
摘要: This digest studies the effects of the magnetic field on multi-rotor drones (MR-drones) used for alternating current (AC) transmission line inspections. Although MR-drones are used for fast and accurate monitoring of the powerlines, there are many difficulties to deliver this task. In this digest, a DJI MAVIC Pro2 MR-drone is employed to study the effects of changing magnetic fields on the drone's built-in magnetometer sensors. The DJI MR-drone is tested under two different levels of magnetic field strength. For this purpose, two sets of experiments are conducted including a solenoid with 350 turns and a large coil with 49 turns. The amplitude of the sinusoidal current injected into the solenoid is about 30 A, where the corresponding magnetic field strength is 1800 mu T from a 10 cm distance. Also, the amplitude of the sinusoidal current that is injected into the larger coil is 63.34 A, with a magnetic field strength of 2500 mu T from a 10 cm distance. The DJI MR data that is recorded by magnetometer sensors are analyzed.

【文献 254】
作者: Lynch, A
来源: 2024 IEEE INTERNATIONAL CONFERENCE ON ROBOTICS AND AUTOMATION, ICRA 2024
题目: A Powerline Inspection UAV Equipped with Dexterous, Lockable Gripping
摘要: Inspection of powerlines is a hard problem that requires humans to operate in remote locations and dangerous conditions. This paper proposes a quadcopter unmanned aerial vehicle (UAV) equipped with rolling-capable perching mechanisms and a depth-vision system for the purpose of autonomous power line inspection. The perching mechanism grips onto the power line, allowing the UAV to withstand external forces such as wind disturbances. Once engaged and applying the desired gripping force, the perching mechanism requires no power through the use of a ratcheting serial elastic transmission, allowing the UAV to perch indefinitely. The depth-vision system automates the perching and unperching procedures by estimating the position and pose of the UAV relative to the powerline. These measurements are sent to a local position controller that guides the UAV to and from the power line. Once perched, rollers in the fingers of the perching mechanism drive the UAV along the powerline, providing a close-up platform for inspection equipment. The proposed system was tested in an outdoor testing environment and shown to autonomously perch and unperch from a steel cable. The grippers force application was analysed and the UAVs powerless robust perch is demonstrated by total disconnect of power while perched. These results suggest that such a system could be a valuable tool for the upkeep of electricity networks.

【文献 255】
作者: Li, Y
来源: COMPUTER-AIDED CIVIL AND INFRASTRUCTURE ENGINEERING
题目: A surface electromyography-based deep learning model for guiding
摘要: While semi-autonomous drones are increasingly used for road infrastructure inspection, their insufficient ability to independently handle complex scenarios beyond initial job planning hinders their full potential. To address this, the paper proposes a human-drone collaborative inspection approach leveraging flexible surface electromyography (sEMG) for conveying inspectors' speech guidance to intelligent drones. Specifically, this paper contributes a new data set, sEMG Commands for Piloting Drones (sCPD), and an sEMG-based Cross-subject Classification Network (sXCNet), for both command keyword recognition and inspector identification. sXCNet acquires the desired functions and performance through a synergetic effort of sEMG signal processing, spatial-temporal-frequency deep feature extraction, and multitasking-enabled cross-subject representation learning. The cross-subject design permits deploying one unified model across all authorized inspectors, eliminating the need for subject-dependent models tailored to individual users. sXCNet achieves notable classification accuracies of 98.1% on the sCPD data set and 86.1% on the public Ninapro db1 data set, demonstrating strong potential for advancing sEMG-enabled human-drone collaboration in road infrastructure inspection.

【文献 256】
作者: Kanazawa, K
来源: 13TH INTERNATIONAL WORKSHOP ON ROBOT MOTION AND CONTROL, ROMOCO 2024
题目: Usability Evaluation of Human-Robot Interaction in Manipulator
摘要: Currently, numerous studies have compared conventional interfaces in terms of operability, demonstrating that VR interfaces outperform traditional methods in several aspects. However, research on best practices in VR remains limited. In VR interfaces, some robots move synchronously in real-time with the input, while others respond only after the operator sets the target posture. Achieving perfect synchronization between the operator's movements and the robot's actions is challenging due to limitations in robot speed and communication delays. The timing of the robot's movements can significantly impact operability in VR interfaces. This study investigates the effect of the timing of robot movement initiation on usability. Validation was conducted using a VR teleoperation interface developed for rescue robots. This interface offers two modes of operation: Move-During-and-After and Move-After. The former synchronizes the robot movement with the operator's actions; in the latter, the robot remains stationary until the operator specifies the target posture, after which it moves to reach the specified posture. We focused on the manipulation of the position and posture of the manipulator. Moreover, we compared the usability differences of inspection tasks using these two methods via experiments involving 13 participants. Furthermore, we evaluated the usefulness of the VR interface by comparing it with gamepad- and display-based interface. The mean task completion time and standard deviation for the Move-During-and-After (MDA) operation were the smallest, followed by Move-After (MA), and then the gamepad (MDA:M = 228.0s, SD = 62.2, MA:M = 294.2s, SD = 143.6, Gamepad: M = 426.8s, SD = 227.9). The results of the one-way ANOVA and Bonferroni test confirmed that the time required for the MDA was significantly shorter than that for the gamepad (p = 0.013). The findings of this study will facilitate the design of interactions with improved operability.

【文献 257】
作者: Li, ZR
来源: IEEE SENSORS JOURNAL
题目: Small UAV Urban Overhead Transmission Line Autonomous Correction
摘要: As the scale of the power grid continues to expand, drone inspection operations are becoming increasingly popular. However, most of the existing inspection drones are for transmission line inspection in the open field environment, with the characteristics of large size and high quality, which is difficult to be directly applied to transmission line inspection around the city area. To address the above issues, in this article, a small unmanned aerial vehicle (UAV) inspection system is designed with the aim of achieving autonomous inspection of overhead ground wires in urban peripheral areas, combined with image processing technology, with a total weight of less than 400 g. Specifically, during the inspection of small UAV, Raspberry Pi uses traditional image processing methods, such as Hough transform, to obtain the position information and pixel error of ground wire from real-time video stream; the flight control system uses the results of image processing combined with data from millimeter-wave radar to achieve conversion from pixel error to actual distance error; finally, the ground wire is made to be in the center of the video as much as possible through the correction strategy, thus realizing the autonomous inspection task of the small UAV along the line. The experimental results show that the small UAV can stably identify the target transmission lines and achieve autonomous flight along the lines with horizontal deviation within plus or minus 0.3 m and height deviation within plus or minus 0.1 m, which is of great reference value for the application of small UAV in urban transmission line inspection.

【文献 258】
作者: Rayhana, R
来源: IEEE-ASME TRANSACTIONS ON MECHATRONICS
题目: Automated Defect-Detection System for Water Pipelines Based on CCTV
摘要: Water is an essential element for the survival of human beings and a nation. Nowadays, water utilities perform regular inspections of the internal conditions of the pipelines via autonomous robotic platforms. The human operator then analyzes the recording of the platforms to identify the defects inside the water pipelines. This manual assessment process is often time-consuming, exhaustive, and error-prone. Hence, this article proposes an automated defect-detection framework channel-spatial attention Mask-Canny-regional convolutional neural network (CSA-MaskC-RCNN) that automatically detects and classifies defects. An adaptive CSA mechanism algorithm is proposed to extract the detected features more efficiently. The features are then passed through a modified defect detector, MaskC-RCNN, to classify the defects on the videos obtained through the autonomous robotic platforms' closed-circuit television cameras. Then, the trained model is employed to develop a defect-detection unit interface tool to perform the defect assessment. The results from our study show that our model can outperform the state-of-the-art model with a mean average precision of 86.89%. Hence, integrating this automatic defect-detection system can save time and cost for the human operator and aid them in making timely decisions for pipe repair/rehabilitation.

【文献 259】
作者: Ding, L
来源: 2024 IEEE 18TH INTERNATIONAL CONFERENCE ON CONTROL & AUTOMATION, ICCA
题目: Improved Reinforcement Learning based on Angle Search for Route Planning
摘要: The inspection robot is applied to assist people in carrying out autonomous inspections and reduce the pressure on hospital staff. This article, based on Q-learning, uses a heuristic potential field function to guide the robot towards the target point and integrates an angle search strategy to further optimize the robot's action selection, so as to achieve the path planning of the inspection robot. This method incorporates a heuristic potential field function, calculated as the ratio of the distance between the current point and the target point to the map's diagonal length, as an additional reward into the Q-value update formula to enhance learning efficiency; Set up the angle search strategy, select the action within a fixed search angle range, avoid the robot from performing redundant exploration actions in the middle and late stages, and improve the convergence speed of the algorithm. Experiments show that the improved algorithm has better optimization performance than compared algorithms, enabling the hospital inspection robot to plan the route efficiently without collision.

【文献 260】
作者: Taccetti, S
来源: 2024 IEEE ULTRASONICS, FERROELECTRICS, AND FREQUENCY CONTROL JOINT
题目: Ultrasonic Wireless Power Transfer using Frequency Steerable Acoustic
摘要: This research work presents an Ultrasonic Wireless Power Transfer (UWPT) system based on a piezoelectric transducer called Frequency-Steerable Acoustic Transducer (FSAT). FSAT's directional properties are exploited to energize inaccessible sensor nodes and transmit the collected data in Structural Health Monitoring applications. To achieve this goal, the full monitoring system architecture is proposed, mostly focusing on the power conversion process. Experimental tests conducted on a 1mm thick aluminum plate with two FSATs bonded at a distance of 50 cm highlighted a maximum received power of 164 mu W with a 83 kHz, 23V peak-to-peak sinusoidal voltage in transmission. This proves the possibility to power a low-end Microcontroller unit (MCU) and micropower management circuits. Moreover, open circuit voltages of 65mV peak-to-peak were received by transmitting 3.3V amplitude square waves at the same frequency, laying the groundwork for a guided waves-based digital data communication to outsource results of structural and environmental inspections.

【文献 261】
作者: Yavasoglu, HA
来源: SUSTAINABILITY
题目: Long-Range Wireless Communication for In-Line Inspection Robot: 2.4 km
摘要: This paper presents a study of the feasibility of using in-line inspection (ILI) techniques with long-range communication-capable robotic systems deployed with advanced inspection sensors in natural gas distribution pipelines, which are rare in the literature. The study involved selecting appropriate antennas and determining the appropriate communication frequency for an ILI robot operating on Istanbul 12 '' and 16 '' steel pipelines. The paper identifies the frequency windows with low losses, presents received signal strength indicator (RSSI) and signal-to-noise ratio (SNR) information for various scenarios, and evaluates the impact of T-junctions, which are known to be the worst components in terms of communication. To utilize the pipeline as a waveguide, low-attenuation-frequency windows were determined, which improved communication by a factor of 500 compared to aerial communication. The results of laboratory tests on a 50 m pipeline and real-world tests on a 2.4 km pipeline indicate that long-distance communication and video transmission are possible at frequencies of around 917 MHz with low-gain antennas. The study also assessed the impact of the early diagnosis of anomalies without incidents on the environment, achievable with ILI robots using long-range wireless communication.

【文献 262】
作者: Li, J
来源: AUTOMATION IN CONSTRUCTION
题目: Adaptive climbing and automatic inspection robot for variable curvature
摘要: Regular inspections are essential to ensure the safe operation of petrochemical storage tank facilities, but traditional manual operations in high-altitude construction environments are inefficient and hazardous. To achieve automated inspection and maintenance of the tank walls, this paper proposes an adaptive climbing inspection robot for variable curvature walls. The inspection robot with magnetic adsorption wheels, curvatureadaptive mechanisms, and inclination adjustment structure was meticulously designed, featuring multifunctional inspection and maintenance capabilities. Robot climbing dynamics and posture adaptability on curved surfaces were analyzed and evaluated. The experimental results demonstrate the robot's ability to adaptively operate on curved tank walls and perform multifunctional tasks, including time-of-flight diffraction (TOFD) ultrasonic flaw detection, polishing, painting, and cleaning. The developed robot can significantly enhance the efficiency of automated inspection operation on curved tank facility walls. Future research should focus on enhancing the robot's adaptability to irregular welded surfaces and improving its defect recognition performance.

【文献 263】
作者: Tang, CQ
来源: IEEE TRANSACTIONS ON INSTRUMENTATION AND MEASUREMENT
题目: An Inspection Robot-Based Health Monitoring Method for Monorail Crane
摘要: Monorail crane track plays an important role in the auxiliary transportation system of coal mines. However, the current track defects mainly rely on manual inspection. It is difficult to carry out continuous and systematic global monitoring of the track. Therefore, this article proposes the health monitoring method for underground monorail crane tracks in coal mines based on an inspection robot, according to the health monitoring needs of monorail crane track conditions. First, we designed a track-dependent inspection robot with a modular and separable structure. Then, for the common health state problems of monorail crane tracks, we proposed a track misalignment detection method based on ultrasonic ranging and a track corner detection method based on machine vision, and investigated a defect localization method based on radio frequency identification (RFID). Finally, we carried out an experimental verification of the inspection robot system. The results show that the inspection robot can smoothly pass through ramps, turnouts, and curves with a maximum angle of 20 degrees, the accuracy of misalignment detection is about 82%, the relative error of the track bending angle detection method is less than 20%, and the accuracy of defective track localization reaches more than 98%. The developed inspection robot system can complete the inspection and monitoring tasks of the monorail crane track condition, and provide a reliable means for the healthy operation and maintenance of the monorail crane track in the coal mine.

【文献 264】
作者: Mthimkhulu, Z
来源: 2023 CONFERENCE ON INFORMATION COMMUNICATIONS TECHNOLOGY AND SOCIETY,
题目: Designing A Frugal Inspection Robot for Detecting Inpipe Leaks in The
摘要: Pipe inspection, according to the literature, is not the cleanest or safest job in the world, as inspectors must perform this critical activity in hazardous and difficult situations. As time-consuming as it is, they must be inspected because they are vulnerable to problems such as cracks and corrosion, which jeopardize their integrity. They must also be inspected regularly to ensure reliable operation and the safety of workers, equipment, and the environment. Inspection is essential because it monitors and maintains the integrity of aging infrastructure while also ensuring that it operates safely and without endangering the health of plant operators. Many corporations, mostly in developing countries, continue to use traditional oil and gas industry procedures to detect and repair leaks in pipeline networks, even though such approaches are time-consuming and dangerous to humans. The goal of this research is to create a frugal inspection robot for detecting in-pipe leaks in the oil and gas industry. In addition, using the robot's acquired images, an image processing technique will be used to detect in-pipe leaks. The frugal robot will be compared to industrial in-pipe inspection robots such as Pig, wheeled, walker/legged, and wall-pressed in terms of cost, flexibility, size, stability, and vertical mobility. According to the study's findings, the frugal inspection robot can detect in-pipe leaks.

【文献 265】
作者: Sepulveda-Valdez, C
来源: MATHEMATICS
题目: Mathematical Modeling for Robot 3D Laser Scanning in Complete Darkness
摘要: This paper introduces an autonomous robot designed for in-pipe structural health monitoring of oil/gas pipelines. This system employs a 3D Optical Laser Scanning Technical Vision System (TVS) to continuously scan the internal surface of the pipeline. This paper elaborates on the mathematical methodology of 3D laser surface scanning based on dynamic triangulation. This paper presents the mathematical framework governing the combined kinematics of the Mobile Robot (MR) and TVS. It discusses the custom design of the MR, adjusting it to use of robustized mathematics, and incorporating a laser scanner produced using a 3D printer. Both experimental and theoretical approaches are utilized to illustrate the formation of point clouds during surface scanning. This paper details the application of the simple and robust mathematical algorithm RANSAC for the preliminary processing of the measured point clouds. Furthermore, it contributes two distinct and simplified criteria for detecting defects in pipelines, specifically tailored for computer processing. In conclusion, this paper assesses the effectiveness of the proposed mathematical and physical method through experimental tests conducted under varying light conditions.

【文献 266】
作者: Chen, JF
来源: 2024 IEEE INTERNATIONAL CONFERENCE ON ROBOTICS AND AUTOMATION, ICRA 2024
题目: Meta-Reinforcement Learning Based Cooperative Surface Inspection of 3D
摘要: This paper presents a decentralized cooperative motion planning approach for surface inspection of 3D structures which includes uncertainties like size, number, shape, position, using multi-robot systems (MRS). Given that most of existing works mainly focus on surface inspection of single and fully known 3D structures, our motivation is two-fold: first, 3D structures separately distributed in 3D environments are complex, therefore the use of MRS intuitively can facilitate an inspection by fully taking advantage of sensors with different capabilities. Second, performing the aforementioned tasks when considering uncertainties is a complicated and time-consuming process because we need to explore, figure out the size and shape of 3D structures and then plan surface-inspection path. To overcome these challenges, we present a meta-learning approach that provides a decentralized planner for each robot to improve the exploration and surface inspection capabilities. The experimental results demonstrate our method can outperform other methods by approximately 10.5%-27% on success rate and 70%-75% on inspection speed.

【文献 267】
作者: Besharatifard, H
来源: IET SCIENCE MEASUREMENT & TECHNOLOGY
题目: In situ monitoring of defects in steel structures using a robot-assisted
摘要: Incidents in extreme environments can bring unprecedented consequences that would endanger the lives of many humans. Real-time and early detection of cracks and defects in the steel used for the construction of industrial sites can bring many benefits to remote operators and site managers. Although different methods and systems have been proposed in the past, they are not suitable for real-time fault detection or long-term operation in harsh environments. Therefore, this paper proposes a low-power robotic platform with a magnetic actuator mechanism which is capable of wireless inspection of steel structures using the ultrasonic method. The proposed magnetic motion uses the inertia force generated by the piston vibration and the difference in frictional force during movement. The paper evaluates the performance of the steel structure defect detection system from four different perspectives: sensing principle, wireless communication network, driving part, and power supply.

【文献 268】
作者: Xu, XJ
来源: JOURNAL OF MAGNETICS
题目: Research on Magnetic Actuation Mechanism and Kinematics of Soft
摘要: Currently, most of the inspection robots for high-voltage transmission lines, both at home and abroad, utilize a multi-cantilever rigid structure. However, the inefficiency and poor safety of these robots when it comes to crossing obstacles make them impractical. To address this issue, a magnetically actuated soft inspection robot has been developed. This robot uses the amperage force applied to the current-carrying coil in a HVDC toroidal magnetic field to efficiently and flexibly cross multiple obstacles in an inchworm-like motion. The focus of this paper is on the design and theoretical calculation of the magnetically actuated model, specifically the magnetic linear traction force and magnetic adsorption force (diastolic force), required to enable the soft robot to crawl. Through simulation and kinematic analysis, the results show that the magnetically actuated soft robot design proposed in this paper is theoretically feasible, providing a foundation for future developments in magnetically actuated soft robots.

【文献 269】
作者: Qian, P
来源: 2024 IEEE 7TH INTERNATIONAL CONFERENCE ON AUTOMATION, ELECTRONICS AND
题目: Self-supervised Learning-based Algorithm for Chinese Image Caption
摘要: Electric robot will obtain a large amount of image information during inspection, and if these images are checked whether there are faults in power inspection is time-consuming and labor-intensive. There is an urgent need for power image Chinese title generation technology to solve it. However, existing image Chinese title generation methods face the problems of small training data sets, differences in specific applications, and few methods for generating Chinese titles for power images. To this end, this paper proposes a self-supervised learning-based image Chinese title generation algorithm for fault detection in electric robot inspection. Specifically, a contrastive learning-based model to automatically capture the semantic relationship between images and text. Then, we propose an end-to-end encoding-decoding model combined with an attention mechanism to obtain Chinese title generation for inspection images. The effectiveness of the proposed algorithm is experimentally verified on two real datasets.

【文献 270】
作者: Li, YL
来源: JOURNAL OF SUPERCOMPUTING
题目: Constructing the intelligent expressway traffic monitoring system using
摘要: With the continuous acceleration of urbanization, traffic congestion, traffic accidents, and urban environmental problems are becoming increasingly serious, which negatively impacts the lives of city residents. With today's urbanization trend, traffic management is a pressing issue, and the safety and smoothness of highways profoundly affect a city's economy and quality of life. As a result, the intelligent inspection robot has entered the public view. It has the advantages of stability and efficiency, can continue to work in a high-intensity state, and helps reduce a lot of human workloads. Firstly, an intelligent transport monitoring system based on the Internet of Things (IoT) is proposed. This system integrates deep learning and artificial intelligence technology, which can quickly query traffic parameters, environmental parameters, and violations that may cause traffic accidents. Secondly, an intelligent inspection robot is introduced to monitor road traffic flow and violation records in real-time, which provides technical support for further scientific management of road traffic. Finally, the intelligent monitoring system's sensitivity and improvement measures are analyzed using the Simultaneous Localization and Mapping (SLAM) algorithm results, making intelligent traffic monitoring more popular. A section of closed safety road is selected for the inspection robot test. The results reveal that (1) the urban transportation model based on the IoT can meet the architecture of intelligent urban transportation. (2) Considering the performance of the inspection robot, the SLAM algorithm is more suitable for road intelligent traffic monitoring. (3) When the number of particles in the improved SLAM algorithm is small, the accuracy and real-time performance of the algorithm can also be guaranteed. The calculation efficiency is improved to 80%, and the modeling accuracy is improved by 23.3%. Traditional traffic monitoring methods typically rely on static sensors and limited data sources. However, the proposed system leverages IoT technology's and inspection robots' real-time data collection capabilities, achieving a more comprehensive, accurate, and flexible acquisition of traffic data. Through this exploration, the overall ideas and objectives of the construction of intelligent highways are clarified, which will lay a solid foundation for the follow-up construction of intelligent highways and provide comprehensive design and practical ideas. The improved SLAM algorithm can more stably complete the positioning and mapping of the tunnel inspection robot in the road environment. In the SLAM algorithm, an Extended Kalman Filter is introduced to ensure the accuracy and real-time of the improved algorithm, which can be applied to the modeling and positioning of unknown environments. Consequently, using the SLAM algorithm in the road detection robot system can stably realize environment awareness and autonomous path planning.

【文献 271】
作者: Zhao, B
来源: ENERGY REPORTS
题目: Research on hybrid navigation algorithm and multi-objective cooperative
摘要: For mobile robots working in the power inspection environment, navigation algorithms and collaborative path planning are the keys to understanding and reproducing human power inspection's thinking mode. Firstly, the paper proposed a hybrid navigation algorithm of reflector and laser based on the generalized inverse of the least squares solution. Secondly, to solve the problem of multi-robot inspection cooperative path planning, the traffic rule reservation table generated a dynamic weighted map representing traffic congestion in power inspection to prevent multi-robot collision. Thirdly, through the distributed control method, the robot used the dynamic weighted map to improve the RRT (Rapidly-Exploring Random Trees)algorithm to achieve the purpose of active cooperative path planning, reducing the trajectory's critical path points and solving the cooperative congestion. Finally, the positioning and calibration compensation can be carried out through the experiment by the reflector navigation algorithm in a large-scale complex environment. Comparing the path planning methods of the A* algorithm and the dynamic weighted RRT algorithm, the dynamic weighted RRT algorithm reduces the number of extended nodes. It solves the multi-robot planning collision problem while ensuring the search path's completeness. Meanwhile, the dynamic weighted RRT algorithm can save the running time of the multi-robot system and effectively improve the efficiency of the power inspection system. (c) 2023 The Author(s). Published by Elsevier Ltd. This is an open access article under theCCBY-NC-ND license (http://creativecommons.org/licenses/by-nc-nd/4.0/).

【文献 272】
作者: Cortes, CAP
来源: ADVANCES IN AUTOMATION AND ROBOTICS RESEARCH, LACAR 2023
题目: Advances in Automatic Feature Inspection with a Robot UR5e Programmed
摘要: A complete integration between advanced manufacturing systems and automated robotic systems is one of the pillars that supports the future of intelligent manufacturing. For this reason, this article proposes using a UR5e collaborative robot as part of the feature inspection system in manufactured parts. The objective is to enable, as a measurement tool using the force and position sensors incorporated in the robotic system, to capture the dimensional data of a piece in a studio case. The precision, the control system, and the robotic system's speed are evaluated in manufacturing features' measurement tasks, enabling a digital measurement with which a closed-loop inspection can be applied. This article presents the results and experimental advances obtained in estimating the dimensional values of a piece manufactured through additive manufacturing.

【文献 273】
作者: Zhao, B
来源: MEASUREMENT SCIENCE AND TECHNOLOGY
题目: Research on abnormal object detection network of computer room
摘要: This paper investigates a deep learning-based anomaly object detection network for identifying and alerting on abnormal items within computer room. First, the framework of the data center inspection robot system is outlined, and the anomaly detection task is decomposed. Next, a dataset of abnormal objects based on data center environmental information is established, and augmentation operations are performed on the created dataset. Subsequently, a SqueezeNet network model based on Residual Squeeze Excitation and Atrous Spatial Pyramid Pooling (RSE-ASPP) is proposed to optimize and improve the SqueezeNet network model. Finally, this paper employs transfer learning to address the issue of insufficient data volume. By pre-training on a large-scale dataset and fine-tuning on the constructed dataset, the accuracy and stability of abnormal object recognition can be significantly enhanced. Ultimately, the proposed RSE-ASPP-SqueezeNet network achieves high-precision detection of abnormal items in the data center inspection robot's anomaly detection task.

【文献 274】
作者: Jiang, Y
来源: ENERGY REPORTS
题目: Research on dynamic path planning method of electric inspection robot
摘要: In order to better address the issue of the coverage rate about power inspection robot to the working area, solving the difficulties in the navigation modeling and path planning of the intelligent control program is the focus of this paper. This paper first analyzes a multi-information fusion SLAM robot positioning and navigation algorithm, then uses the Kalman filter technology to locate through Voronoi mapping. Second, this paper proposes a five-layer neural network approach to how robots can plan and control routes in high-density scenarios. Third, the robot takes the surrounding obstacle distance, the target angle and the target distance as input parameters then the output parameters of the fuzzy neural network are the wheel speed and the steering angle. The inspection robot uses the sensors on the body to detect the surrounding environment's parameter information. The dynamic path planning algorithm changes the path accordingly, prevents collision, and finally reaches the preset target. Finally, the paper collects the features obtained by Voronoi mapping in a large-scale simulation environment. The fuzzy neural network algorithm for detecting robots is validated in a complex environment, proving the effectiveness and accuracy of the algorithm. (c) 2023 The Author(s). Published by Elsevier Ltd. This is an open access article under the CC BY-NC-ND license (http://creativecommons.org/licenses/by-nc-nd/4.0/).

【文献 275】
作者: Meng, QL
来源: JOURNAL OF PERFORMANCE OF CONSTRUCTED FACILITIES
题目: A Robot System for Rapid and Intelligent Bridge Damage Inspection Based
摘要: Large numbers of bridges have already suffered various types of damage but still operate all year round without proper treatment. Conducted primarily manually, the routine bridge inspections are ineffective in detecting potential damage in time due to a lack of relevant instruments and equipment, particularly modern measures. In this study, a rapid and intelligent bridge inspection system that integrates multiple modules and deep learning algorithms was established. First, the robot inspection equipment is established. Then, the You Only Look Once version 3 (YOLOv3) object detection algorithm is employed to classify four types of defects from the acquired data. Finally, an image segmentation algorithm is used to identify crack defects at a pixel level. Experimental results reveal that the proposed system can be effectively applied to accurately locate defects (e.g., cracks, spalls, exposed tendons, and free lime) and identify cracks at a pixel level on various types of bridges without affecting traffic.

【文献 276】
作者: Zeng, YJ
来源: APPLIED SCIENCES-BASEL
题目: A Novel Autonomous Landing Method for Flying-Walking Power Line
摘要: Hybrid inspection robots have been attracting increasing interest in recent years, and are suitable for inspecting long-distance overhead power transmission lines (OPTLs), combining the advantages of flying robots (e.g., UAVs) and climbing robots (e.g., multiple-arm robots). Due to the complex work conditions (e.g., power line slopes, complex backgrounds, wind interference), landing on OPTL is one of the most difficult challenges faced by hybrid inspection robots. To address this problem, this study proposes a novel autonomous landing method for a developed flying-walking power line inspection robot (FPLIR) based on prior structure data. The proposed method includes three main steps: (1) A color image of the target power line is segmented using a real-time semantic segmentation network, fusing the depth image to estimate the position of the power line. (2) The safe landing area (SLA) is determined using prior structure data, applying the trajectory planning method with geometric constraints to generate the dynamic landing trajectory. (3) The landing trajectory is tracked using real-time model predictive control (MPC), controlling FPLIR to land on the OPTL. The feasibility of the proposed method was verified in the ROS Gazebo environment. The RMSE values of the position along three axes were 0.1205,0.0976 and 0.0953, respectively, while the RMSE values of the velocity along these axes were 0.0426, 0.0345 and 0.0781. Additionally, experiments in a real environment using FPLIR were performed to verify the validity of the proposed method. The experimental results showed that the errors of position and velocity for the FPLIR landing on the lines were 6.18x10-2 m and 2.16x10-2 m/s. The simulation results as well as the experimental findings both satisfy the practical requirements. The proposed method provides a foundation for the intelligent inspection of OPTL in the future.

【文献 277】
作者: Sanchez-Cubillo, J
来源: SENSORS
题目: Toward Fully Automated Inspection of Critical Assets Supported by
摘要: Robotic inspection is advancing in performance capabilities and is now being considered for industrial applications beyond laboratory experiments. As industries increasingly rely on complex machinery, pipelines, and structures, the need for precise and reliable inspection methods becomes paramount to ensure operational integrity and mitigate risks. AI-assisted autonomous mobile robots offer the potential to automate inspection processes, reduce human error, and provide real-time insights into asset conditions. A primary concern is the necessity to validate the performance of these systems under real-world conditions. While laboratory tests and simulations can provide valuable insights, the true efficacy of AI algorithms and robotic platforms can only be determined through rigorous field testing and validation. This paper aligns with this need by evaluating the performance of one-stage models for object detection in tasks that support and enhance the perception capabilities of autonomous mobile robots. The evaluation addresses both the execution of assigned tasks and the robot's own navigation. Our benchmark of classification models for robotic inspection considers three real-world transportation and logistics use cases, as well as several generations of the well-known YOLO architecture. The performance results from field tests using real robotic devices equipped with such object detection capabilities are promising, and expose the enormous potential and actionability of autonomous robotic systems for fully automated inspection and maintenance in open-world settings.

【文献 278】
作者: Aldao, E
来源: CONSTRUCTION AND BUILDING MATERIALS
题目: Comparison of deep learning and analytic image processing methods for
摘要: In this work, different methods are proposed and compared for autonomous inspection of railway bolts and clips. A prototype of an autonomous data acquisition system was developed to automatically obtain information of the state of the railway track using LiDAR and camera sensors. This system was employed in a testing railway track installed in the facilities of the University of Vigo to obtain the images used in this work. Then, the images were further processed using analytic image segmentation algorithms as well as a neural network to detect the bolts and clips. Once these elements are detected, their relative position is computed to evaluate if there is any missing component. Finally, the orientation of the clips is computed to ensure that all the bolts are correctly placed. Four different methods were implemented, and their performance was evaluated using the segmentations provided by the analytical methods and the neural network.

【文献 279】
作者: Rodriguez, AA
来源: DRONES
题目: Deep Learning for Indoor Pedestal Fan Blade Inspection: Utilizing
摘要: This paper introduces a drone-based surrogate project aimed at serving as a preliminary educational platform for undergraduate students in the Electrical and Computer Engineering (ECE) fields. Utilizing small Unmanned Aerial Vehicles (sUAVs), this project serves as a surrogate for the inspection of wind turbines using scaled-down pedestal fans to replace actual turbines. This approach significantly reduces the costs, risks, and logistical complexities, enabling feasible and safe on-campus experiments. Through this project, students engage in hands-on applications of Python programming, computer vision, and machine learning algorithms to detect and classify simulated defects in pedestal fan blade (PFB) images. The primary educational objectives are to equip students with foundational skills in autonomous systems and data analysis, critical for their progression to larger scale projects involving professional drones and actual wind turbines in wind farm settings. This surrogate setup not only provides practical experience in a controlled learning environment, but also prepares students for real-world challenges in renewable energy technologies, emphasizing the transition from theoretical knowledge to practical skills.

【文献 280】
作者: Chen, MH
来源: PROCEEDINGS OF ASME 2024 PRESSURE VESSELS & PIPING CONFERENCE, PVP2024,
题目: LIFT-OFF SIMULATION AND REVISION OF THE FLUID-DRIVEN MFL PIPELINE
摘要: The use of fluid-driven Magnetic Flux Leakage (MFL) pipeline inspection robot to maintain oil and gas pipelines is an essential method of ensuring safe operation. The internal excitations of the pipe, such as girth weld and depressions, cause the vibration response of the robot and the variation of the Liftoff value of the sensors. In this study, the Lift-off value of the detection probe and the vibration response of a multi-sealing disc pipeline inspection robot passing through a girth weld under different design parameters are investigated by co-simulation. Considering the coupling between the fluid and robot, the fluidstructure interactions simulation model of MSC/ADAMS and MATLAB/SIMULINK is established. Simplify the modeling of probe detection arm and study the change trend of Lift-off value under excitation. The results show that the change of sealing disc interval and running speed has a significant influence on the dynamic response. The extreme value of vertical acceleration increases with the increase of the interval, but the increase of sealing disc interval has little influence on the change of tendency and the extreme value of axial acceleration. As the running speed increases, the extreme value of the vertical acceleration decreases significantly, and the degree of reciprocating oscillation in the time history curve decreases significantly. The extreme value and the range of variation of the Lift-off value at different locations are determined through result analysis and show a weakening trend at different speeds. The presented analysis is beneficial for structural fatigue analysis and Lift-off correction.

【文献 281】
作者: Zhang, HB
来源: IEEE TRANSACTIONS ON INSTRUMENTATION AND MEASUREMENT
题目: LICFM3-SLAM: LiDAR-Inertial-Camera Fusion and Multimodal Multilevel
摘要: In comparison to wheeled robots, the locomotion of bionic quadruped robots is more vigorous. Mapping systems should maintain satisfactory robustness and accuracy in various complex real-world scenarios, even when the robot's body experiences intense shaking. To address these challenges, this study proposes a simultaneous localization and mapping (SLAM) system based on LiDAR-inertial-camera fusion and a multimodal multilayer matching algorithm (LICFM3-SLAM). First, a tightly coupled strategy is utilized to fuse LiDAR, inertial, and camera information, introducing a visual-inertial odometry (VIO) subsystem based on adaptive graph inference; thus, high-precision and robust robot state estimation is achieved. Second, inspired by human spatial cognition, the study proposes a multimodal multilayer matching algorithm and utilizes observation data obtained from the camera and LiDAR, thereby achieving accurate and robust data association. Finally, incremental poses are optimized using factor graph optimization methods; thus, a globally consistent 3-D point cloud map is constructed. The proposed system is tested on a public benchmark dataset and applied to a bionic quadruped inspection robot (BQIR), and experiments are conducted in various challenging indoor and outdoor large-scale scenarios. The results reveal that LICFM3-SLAM exhibits high robustness and mapping accuracy while meeting real-time requirements.

【文献 282】
作者: Zhou, J
来源: COMPUTERS AND ELECTRONICS IN AGRICULTURE
题目: A Novel Behavior Detection Method for Sows and Piglets during Lactation
摘要: Accurately identifying behaviors exhibited by lactating sows and piglets is crucial for maintaining swine health and preventing farming crises. In the absence of dedicated swine behavior monitoring systems and the challenges of implementing cloud-based automated monitoring in large-scale farming, this study proposes a method utilizing inspection robots to detect behaviors of lactating sows and piglets. The inspection robot initially serves as a data acquisition and storage tool, collecting behavioral data such as sows postures (standing, sitting, lateral recumbency, and sternal recumbency) and activities of piglet groups (resting, suckling, and active behavior) within confined pens. The YOLOv8 series algorithms are then employed to identify static postures of sows, while the Temporal Shift Module (TSM) is used to recognize dynamic behaviors within piglet groups. These models are fine-tuned and deployed on the Jetson Nano edge computing platform. Experimental results show that YOLOv8n accurately identifies sow postures with a mean Average Precision (mAP) @0.5 of 97.08% and a frame rate of 36.4 FPS at an image resolution of 480 x 288, following TensorRT acceleration. For piglet behavior recognition, the TSM model, using ResNet50 as the backbone network, achieves a Top-1 accuracy of 93.63% in recognizing piglet behaviors. Replacing ResNet50 with MobileNetv2 slightly reduces the Top-1 accuracy to 90.81%; however, there is a significant improvement in inference speed on Jetson Nano for a single video clip with a processing duration of 542.51 ms, representing more than a 20-fold enhancement compared to TSM_ResNet50. The Kappa consistency analysis reveals moderate behavioral coherence among sows in different pens and piglet groups. The study offers insights into automated detection of behaviors lactating sows and piglets within large-scale intensive farming systems.

【文献 283】
作者: Chu, HH
来源: PROCEEDINGS OF THE ITA-AITES WORLD TUNNEL CONGRESS 2023, WTC 2023:
题目: A collaborative inspection system composed of quadruped and flying robot
摘要: Advanced robotic systems involving unmanned aerial vehicles (UAVs) and unmanned ground vehicles (UGVs) have shown advantages in detecting damaged infrastructures, such as bridges, buildings, and nuclear power plants. Due to their ability to leverage varied vantage points, a robot team of heterogeneous UAVs and UGVs could collect full space information of the structure with wider coverages and higher robustness compared with that from each robotic platform alone. However, critical issues still exist when applying the UAV-UGV inspection system in the tunnel environment. One major problem is that the lack of light and GPS signals may lead to a higher probability of UAV collisions. Another problem is that the road surface in the tunnel is generally uneven with many obstacles and puddles, which could hinder the travel of traditional wheeled or tracked UGVs. To address the above issues, a novel collaborative robotic system was proposed that consists of a quadruped robot with strong obstacle-striding abilities and a collision-resilient UAV. In the proposed inspection framework, the quadruped robot follows a pre-planned path and scans the tunnel lining with the vision sensor. Based on the scanning results, the UAV equipped with a protective cage would be launched from the helipad of the quadruped robot to perform a closer inspection. To correct its flight trajectory and protect the UAV, a GPS-free self-localization algorithm is constructed based on the data from the onboard computer, camera, and inertial measurement unit (IMU). Finally, to ensure that the tiny defects can be detected efficiently, a multi-scale feature fusion segmentation network with the attention mechanism was applied to images taken by the UAV. The performance of the system is validated against a field test, which demonstrates the feasibility of developing and deploying a collaborative inspection system using quadruped and flying robots for tunnel inspection.

【文献 284】
作者: Ehrbar, J
来源: FLEXIBLE AUTOMATION AND INTELLIGENT MANUFACTURING: ESTABLISHING BRIDGES
题目: Robot-Based Inspection of Freeform Components: Process Analysis and
摘要: Vertical scanning white light interferometry is nowadays successfully used for automated inspection of combustion chambers in aircraft engines. Although they provide high-accuracy data, white light interferometers have the disadvantage of being comparatively slow sensors due to their small field of view and vibrational sensitivity. Using a lateral scanning white light interferometer instead of a vertical scanning one offers a reduction in inspection times by scanning larger areas at once. At the same time using the lateral scanning setup creates new challenges for the inspection process. In this paper, considering a fan blade of an aircraft engine as an example, a possible inspection setup is shown for reference and the challenges regarding the inspection process are investigated. Existing solutions for similar problems are analysed for their applicability. The biggest remaining challenge is seen in the path planning for the inspection, since generating a minimal number of viewpoints is critical for efficiently using a lateral scanning white light interferometer.

【文献 285】
作者: Xie, YQ
来源: IEEE TRANSACTIONS ON INSTRUMENTATION AND MEASUREMENT
题目: Relative Positioning of the Inspection Robot Based on RFID Tag Array in
摘要: High-precision location information is the key to efficient, safe, and automated inspection. A novel relative positioning method based on radio frequency identification (RFID) tag array is proposed and demonstrated, and the high-precision relative position of the inspection robot is realized by configuring only a small tag array in the inspection area. First, an array of reference tags is deployed on the walls around the inspection area. Second, in the offline stage, the inspection robot patrols along the designated route, and uses a system that integrates a mobile robot with an RFID reader and antenna to obtain the phase measurement value of the reference tag array (RTA) and build a fingerprint database. Finally, in the online stage, the inspection robot continuously collects the phase information of the RTA during the inspection process, compensates the error of the online data based on the offline fingerprint database, and selects the appropriate reference tag to participate in the positioning based on the geometric dilution of precision (GDOP) theory. We built our test platform using the commercial RFID reader Impinj Speedway R420, and deployed a set of smaller RFID tag arrays with a number of 3 x 6 and a spacing of 40 cm between tags in the conference room of our university. The results showed that the new design was less affected by the inspection trajectory and obstacles, and could assist the inspection robot to achieve high-precision relative positioning, with a positioning error of no more than 6 cm in complex scenarios.

【文献 286】
作者: Elankavi, RS
来源: JOURNAL OF AMBIENT INTELLIGENCE AND SMART ENVIRONMENTS
题目: Design of a wheeled-type In-Pipe Inspection Robot to overcome motion
摘要: This paper discusses the development and design of two wheeled-type In-Pipe Inspection Robots (IPIRs), Kuzhali I and Kuzhali II, which were created to address the limitations of traditional human inspection methods and earlier robot designs. Specifically, the robots aim to overcome the motion singularity experienced by IPIRs when navigating through curved pipes. Kuzhali I was developed with wheels mounted at an asymmetric angle, which enables the wheels to maintain contact with the pipe's surface, preventing motion singularity. However, Kuzhali I had limitations due to its prismatic mechanism, and thus Kuzhali II was developed with a telescopic mechanism to allow it to pass through vertical pipes with obstacles. Motion analysis was conducted on both robots to demonstrate how they overcome motion singularity and navigate through straight and curved pipelines. Simulation results showed that the forces acting on the robots' wheels fell within 5 N to 12 N, demonstrating stability while navigating pipeline junctions. Experimental tests were conducted on Kuzhali II, and the results were compared to simulation results, showing an error of less than 5%. The results of the experiments indicate that Kuzhali II is safe to use for pipeline inspection, can navigate through vertical pipelines with ease and can overcome motion singularity in curved pipes. These robots offer a faster, more accurate, and safer alternative to human inspection, which can reduce the risk of pipeline failures and associated environmental and safety hazards.

【文献 287】
作者: Jiang, Z
来源: JOURNAL OF FIELD ROBOTICS
题目: Design and analysis of a wall-climbing robot for water wall inspection
摘要: Boiler water wall in thermal power plants is characterized by high-altitude detection requirements. Moreover, the existing water wall-climbing robots are characterized by low obstacle-crossing performance, deviations, and a lack of autonomous crossing pipeline function. In view of this feature, a wall-climbing robot with permanent magnet and electromagnetic hybrid adsorption wheels is proposed. The robot has the function of independent traverse according to its own structural characteristics. Furthermore, transverse movement is proposed by comparing different adsorption modes, moving and driving modes. Robot statics in upward, downward, and transverse crawling are carried out, and nonsliding mechanical and nonoverturning mechanical models are obtained. Robot's dynamics are analyzed by considering the wall movement. The finite element simulation analysis of its main stressed parts is carried out by employing ANSYS, and an optimal structural model is obtained. The gap adsorption permanent magnet model is constructed, and its parametric simulation analysis is carried out using the Ansoft Maxwell module. The influence curve of the gap on the magnetic force is then obtained. Finally, the prototype is developed according to the design model and calculation analysis, and the experimental test is carried out. The experimental results show that the robot meets the expected functions and indexes, providing a basis for the intelligent development of thermal power plants.

【文献 288】
作者: Park, J
来源: BIOMIMETICS
题目: Development of a Wheel-Type In-Pipe Robot Using Continuously Variable
摘要: Pipelines are embedded in industrial sites and residential environments, and maintaining these pipes is crucial to prevent leakage. Given that most pipelines are buried, the development of robots capable of exploring their interiors is essential. In this work, we introduce a novel in-pipe robot utilizing Continuously Variable Transmission (CVT) mechanisms for navigating various pipes, including vertical and curved pipes. The robot comprises one air motor, three CVT mechanisms, and six wheels at the end of six slider-crank mechanisms, including three active and three idler ones. The slider crank and spring mechanism generate a wall press force through the wheel to prevent slipping inside the pipe. This capability allows the robot to climb vertical pipes and adapt to various pipe diameters. Moreover, by combining CVT mechanisms, whose speed ratios between the driver and driven pulleys are passively adjusted by the position of the slider, the robot achieves independent and continuous speed control for each wheel. This enables it to navigate pipes with various geometries, such as straight-curved-straight pipes, using only one motor. Since active control of each wheel is not needed, the complexities of the robot controller can be significantly reduced. To validate the proposed mechanism, MATLAB simulations were conducted, and in-pipe driving experiments were executed. Both simulation and experimental results have shown that the robot can effectively navigate curved pipes with a maximum speed of 17.5 mm/s and a maximum traction force of 56.84 N.

【文献 289】
作者: Jakubiak, J
来源: APPLIED SCIENCES-BASEL
题目: Fast Detection of Idler Supports Using Density Histograms in Belt
摘要: The automatic inspection of belt conveyors gathers increasing attention in the mining industry. The utilization of mobile robots to perform the inspection allows increasing the frequency and precision of inspection data collection. One of the issues that needs to be solved is the location of inspected objects, such as, for example, conveyor idlers in the vicinity of the robot. This paper presents a novel approach to analyze the 3D LIDAR data to detect idler frames in real time with high accuracy. Our method processes a point cloud image to determine positions of the frames relative to the robot. The detection algorithm utilizes density histograms, Euclidean clustering, and a dimension-based classifier. The proposed data flow focuses on separate processing of single scans independently, to minimize the computational load, necessary for real-time performance. The algorithm is verified with data recorded in a raw material processing plant by comparing the results with human-labeled objects. The proposed process is capable of detecting idler frames in a single 3D scan with accuracy above 83%. The average processing time of a single scan is under 22 ms, with a maximum of 75 ms, ensuring that idler frames are detected within the scan acquisition period, allowing continuous operation without delays. These results demonstrate that the algorithm enables the fast and accurate detection and localization of idler frames in real-world scenarios.

【文献 290】
作者: Yu, YC
来源: APPLIED ACOUSTICS
题目: Sparse representation for artefact/defect localization with an acoustic
摘要: Autonomous robots equipped with long-range acoustic sensing are revolutionizing the inspection of extensive sewer networks by accurately detecting and mapping artefacts and defects that could cause a pipe operational failure. This study introduces a new frequency domain Sparse Representation (SR) method for identifying artefacts and defects using a circular microphone array mounted on a continuously moving inspection robot. The circular array enhances the precision of localization by extracting plane wave modes at frequencies above the pipe's first cut-off frequency. The proposed mobile compensation algorithm can separate overlapping echoes with less than 0.5% error of the sensing distance, given sufficient overlap in acoustic wavelengths. It also compensates for phase shifts and reduces noise from a moving robot, enabling accurate estimation of defect direction and distance amid overlapping acoustic echoes. The novelty of this method is in the ability to work on a continuously moving robot to achieve a significant enhancement in terms of operational speed, direction estimation and defect resolution quality.

【文献 291】
作者: Liu, KQ
来源: SCIENTIFIC REPORTS
题目: Motion control and positioning system of multi-sensor tunnel defect
摘要: As the mileage of subway is increasing rapidly, there is an urgent need for automatic subway tunnel inspection equipment to ensure the efficiency and frequency of daily tunnel inspection. The subway tunnel environment is complex, it cannot receive GPS and other satellite signals, a variety of positioning sensors cannot be used. Besides, there are random interference, wheel and rail idling and creep. All the above results in poor performance of conventional speed tracking and positioning methods. In this paper, a multi-sensor motion control system is proposed for the subway tunnel inspection robot. At the same time, a trapezoidal speed planning and a speed tracking algorithm based on MPC (Model Predictive Control) are proposed, which simplify longitudinal dynamics model to overcome the complex and variable nonlinear problems in the operation of the maintenance robot. The optimal function of speed, acceleration and jerk constraint is designed to make the tunnel inspection robot achieve efficient and stable speed control in the subway tunnel environment. In this paper, the "INS (inertial navigation system)+Odometer" positioning method is proposed. The difference between the displacement measured by the inertial navigation system and the displacement calculated by the odometer is taken as the measurement value, which reduces the dimension of the conventional algorithm. The closed-loop Kalman filter is used to establish the combined positioning model, and the system error can be corrected in real time with higher accuracy. The algorithms were verified on the test line. The displacement target was set to be 1 km and the limit speed was 60 km/h. The overshooting error of the speed tracking algorithm based on trapezoidal velocity planning and MPC was 0.89%, and the stability error was 0.32%. It improved the accuracy and stability of the speed following, and was much better than the PID speed tracking algorithm. At the speed of 40 km/h, the maximum positioning error of the robot within 2 km is 0.15%, and the average error is 0.08%. It is verified that the multi-sensor fusion positioning algorithm has significantly improved the accuracy compared with the single-odometer positioning algorithm, and can effectively make up for the position error caused by wheel-rail creep and sensor error.

【文献 292】
作者: Qu, JW
来源: AGRONOMY-BASEL
题目: Map Construction and Positioning Method for LiDAR SLAM-Based Navigation
摘要: In agricultural field inspection robots, constructing accurate environmental maps and achieving precise localization are essential for effective Light Detection And Ranging (LiDAR) Simultaneous Localization And Mapping (SLAM) navigation. However, navigating in occluded environments, such as mapping distortion and substantial cumulative errors, presents challenges. Although current filter-based algorithms and graph optimization-based algorithms are exceptionally outstanding, they exhibit a high degree of complexity. This paper aims to investigate precise mapping and localization methods for robots, facilitating accurate LiDAR SLAM navigation in agricultural environments characterized by occlusions. Initially, a LiDAR SLAM point cloud mapping scheme is proposed based on the LiDAR Odometry And Mapping (LOAM) framework, tailored to the operational requirements of the robot. Then, the GNU Image Manipulation Program (GIMP) is employed for map optimization. This approach simplifies the map optimization process for autonomous navigation systems and aids in converting the Costmap. Finally, the Adaptive Monte Carlo Localization (AMCL) method is implemented for the robot's positioning, using sensor data from the robot. Experimental results highlight that during outdoor navigation tests, when the robot operates at a speed of 1.6 m/s, the average error between the mapped values and actual measurements is 0.205 m. The results demonstrate that our method effectively prevents navigation mapping distortion and facilitates reliable robot positioning in experimental settings.

【文献 293】
作者: Ji, HX
来源: SENSORS
题目: Spatial Localization of Transformer Inspection Robot Based on Adaptive
摘要: In the detection process of the internal defects of large oil-immersed transformers, due to the huge size of large transformers and metal-enclosed structures, the positional localization of miniature inspection robots inside the transformer faces great difficulties. To address this problem, this paper proposes a three-dimensional positional localization method based on adaptive denoising and the SCOT weighting function with the addition of the exponent beta (SCOT-beta) generalized cross-correlation for L-type ultrasonic arrays of transformer internal inspection robots. Aiming at the strong noise interference in the field, the original signal is decomposed by an improved Empirical Mode Decomposition (EMD) method, and the optimal center frequency and bandwidth of each mode are adaptively searched. By extracting the modes in the frequency band of the positional localization signal, suppressing the modes in the noise frequency band, and reconstructing the Intrinsic Mode Function (IMF) of the independently selected superior modal components, a signal with a high signal-to-noise ratio is obtained. In addition, for the traditional mutual correlation algorithm with a large delay estimation error at a low signal-to-noise ratio, this paper adopts an improved generalized joint weighting function, SCOT-beta, which improves the anti-jamming ability of the generalized mutual correlation method at a low signal-to-noise ratio by adding an exponential function to the denominator term of the SCOT weighting function's generalized cross-correlation. Finally, the accurate positional localization of the transformer internal inspection robot is realized based on the quadratic L-array and search-based maximum likelihood estimation method. Simulation and experimental results show the following: the improved EMD denoising method better improves the signal-to-noise ratio of the positional localization signal with a lower distortion rate; in the transformer test tank, which is 120 cm in length, 100 cm in width, and 100 cm in height, based on the positional localization method in this paper, the average relative positional localization error of the transformer internal inspection robot in three-dimensional space is 2.27%, and the maximum positional localization error is less than 2 cm, which meets the requirements of engineering positional localization.

【文献 294】
作者: Yu, SY
来源: TRANSACTIONS OF THE CANADIAN SOCIETY FOR MECHANICAL ENGINEERING
题目: Novel design and motion analysis of an omni-tread snake-like robot for
摘要: Snake-like robots have a slender body and strong environmental adaptability. This paper aims to propose an omni-tread snake-like robot that can adapt to the needs of specific narrow space exploration missions and provide a basis for design work through necessary motion analysis. The robot adopts the form of three modules in series, and the modules are connected through differential driving joints, which increases the joint torque of the robot. The nested omni-tread structure improves the robot's motion efficiency and environmental adaptability. This paper conducts configuration and motion analysis of the robot. The simulation is performed using the optimization solution method to obtain the joint torque and walking torque of the robot, which guides the design of the robot. The experiment is also conducted with the developed prototype to verify the robot's performance. From the simulation results, the joint torque and walking torque are obtained, and the motors are selected. The motion performance and field applicability of the robot are verified through experiment tests. The experiment results further verify the robot design and analysis work. The structural design of robots and optimization solution method in this paper has certain reference values for other researchers.

【文献 295】
作者: Li, YH
来源: AGRICULTURE-BASEL
题目: Design of a Closed Piggery Environmental Monitoring and Control System
摘要: To improve environmental quality in enclosed piggeries, a monitoring and control system was designed based on a track inspection robot. The system includes a track mobile monitoring platform, an environmental control system, and a monitor terminal. The track mobile monitoring platform consists of three main components: a single-track motion device, a main box containing electronic components, and an environmental sampling device. It is capable of detecting various environmental parameters such as temperature, humidity, NH3 concentration, CO2 concentration, light intensity, H2S concentration, dust concentration, and wind speed at different heights below the track. Additionally, it can control on-site environmental control equipment such as lighting systems, ventilation systems, temperature control systems, and manure cleaning systems. The networked terminal devices enable real-time monitoring of field equipment operating status. An adaptive fuzzy PID control algorithm is embedded in the system to regulate the temperature of the piggery. Field tests conducted on a closed nursery piggery revealed that the system effectively controlled the maximum temperature range within 2 degrees C. The concentrations of CO2, NH3, and PM2.5 were maintained at a maximum of 1092 mg center dot m(-3), 16.8 mg center dot m(-3), and 35 mu g center dot m(-3), respectively. The light intensity ranged from 51 to 57 Lux, while the wind speed remained stable at approximately 0.35 m center dot s(-1). The H2S concentration was significantly lower than the standard value, and the lowest relative humidity recorded was 18% RH at high temperatures. Regular humidification is required in closed piggeries and other breeding places when the system does not trigger the wet curtain humidification and cooling function, as the relative humidity is lower than the standard value. By controlling the temperature, the system combined with a humidification device can meet environmental requirements. The control method is simple and effective, with a wide range of applications, and holds great potential in the field of agricultural environmental control.

【文献 296】
作者: Yang, S
来源: SAFETY SCIENCE
题目: Risk-based performance assessment from fully manual to human-robot
摘要: The use of robotics in the process industry has shown a stable, increasing trend, according to survey reports, reducing the exposure of operators to critical operations, mainly during maintenance. Moving from full manual (FM) to human-robot teaming (HRT) operations is expected to reduce the risks for operators and increase operational efficiency. The methods used to assess system performances require an adaptation. Traditional risk assessment techniques are no longer adapted to analyze the new way of working; the previous methods proposed for HRT systems have been criticized for their fragmentation, complexity, and lack of validation. This study proposed an integrated framework, including the qualitative and quantitative stages, to investigate the risk-based system performance and compare the full manual and human-robot teaming scenarios in pressurized tank inspection operations. The outputs demonstrated that the integrated framework worked for FM and HRT system performance assessment, considering multiple element types and their interdependencies, generating knowledge that can be exploited to reduce the system's risk and choose among different operational alternatives.

【文献 297】
作者: Tunt, TT
来源: PROCEEDINGS OF ASME 2024 43RD INTERNATIONAL CONFERENCE ON OCEAN,
题目: HIGH-FIDELITY SIMULATION PLATFORM FOR AUTONOMOUS FISH NET-PEN VISUAL
摘要: The importance of high-fidelity simulation in the deployment of Unmanned Underwater Vehicles (UUVs) in offshore aquaculture is evident, driven by a range of crucial factors such as safe deployment in challenging working environments. Such simulations enable thorough testing and refinement of UUV behavior, thereby mitigating safety concerns associated with human workforce exposure to risk. Additionally, the intricate preparations for real-world field trials involving multiple stakeholders require thorough planning. High-fidelity simulations serve a pivotal role as an informed decision-making tool in a cost-efficient way in aligning these diverse stakeholders and in pre-validating the advantages and challenges associated with UUV deployment in uncertain and demanding offshore aquaculture environments. In this paper, a number of existing simulation platforms are reported and using one of them which is built on the Robot Operating System (ROS) in Python, namely UUV simulator, an autonomous fish net-pen visual inspection with the BlueROV2 Heavy Configuration for the Blue Endeavour project (an upcoming first offshore aquaculture firm in New Zealand) of the New Zealand King Salmon company was demonstrated as a use-case study.

【文献 298】
作者: Aubard, M
来源: OCEANS 2024 - SINGAPORE
题目: Mission Planning and Safety Assessment for Pipeline Inspection Using
摘要: The recent advance in autonomous underwater robotics facilitates autonomous inspection tasks of offshore infrastructure. However, current inspection missions rely on predefined plans created offline, hampering the flexibility and autonomy of the inspection vehicle and the mission's success in case of unexpected events. In this work, we address these challenges by proposing a framework encompassing the modeling and verification of mission plans through Behavior Trees (BTs). This framework leverages the modularity of BTs to model onboard reactive behaviors, thus enabling autonomous plan executions, and uses BehaVerify to verify the mission's safety. Moreover, as a use case of this framework, we present a novel AI-enabled algorithm that aims for efficient, autonomous pipeline camera data collection. In a simulated environment, we demonstrate the framework's application to our proposed pipeline inspection algorithm. Our framework marks a significant step forward in the field of autonomous underwater robotics, promising to enhance the safety and success of underwater missions in practical, real-world applications. https://github.com/remaro-network/pipe_inspection_mission

【文献 299】
作者: Santos, P
来源: OCEANS 2024 - SINGAPORE
题目: Path Planning and Control for Remotely Operated Vehicles Focused on
摘要: This paper introduces an innovative approach to address the challenges of inspecting offshore wind turbines by presenting a real-time dynamic path-planning system for remotely operated vehicles (ROVs). Focused on floating wind turbine structures, the proposed methodology integrates an Inertial Measurement Unit (IMU) into the structure, allowing dynamic adjustment of the inspection path based on real-time pose data. Utilizing a fault-tolerant control solution, the system enables autonomous flight control of ROVs, maintaining consistent orientation towards the structure and desired inspection distance. The applicability of the approach is validated through simulations in the OceanRINGS+ control suite, emphasizing the importance of dynamic path planning in enhancing efficiency, safety, and cost-effectiveness in offshore wind turbine inspections.

【文献 300】
作者: Wibisono, A
来源: IEEE ACCESS
题目: An Autonomous Underwater Vehicle Navigation Technique for Inspection and
摘要: In the above article [1], reference 67 was retracted. As the work in this reference is no longer reliable, we are removing it from the reference list and replacing it with [2].

【文献 301】
作者: Zhao, WZ
来源: INTERNATIONAL JOURNAL OF ADVANCED MANUFACTURING TECHNOLOGY
题目: Multi-robot coverage path planning for dimensional inspection of large
摘要: The multi-robot coverage path planning problem (MRCPP), incorporating with the sub-problems of viewpoint sampling, viewpoint task allocation and robotic sequential planning generally, is a highly coupled problem with complex engineering constraints and is difficult to achieve the optimal result. Most coverage path planning studies in the field of industrial inspection focus on viewpoint sampling and sequential path planning for the individual robot. At the same time, the measurement uncertainty requirements of the inspected surface features for the candidate viewpoints are rarely considered in the process of CPP. To address this problem, a systematic MRCPP framework for free-form surface inspection is developed considering the measurement uncertainty requirements and full coverage of the surfaces to ensure the inspection efficiency and the scanning accuracy of to-be-inspect features. Specifically, a stepwise method for multi-robot optical coverage path planning is proposed. Firstly, a redundant viewpoint set is generated based on the geometric model of the product; additionally, considering the constraints of measurement uncertainty and full coverage constraint, an enhanced Random Inspection Tree Algorithm (RITA) is used to obtain the optimal viewpoint set. Secondly, an improved group Ant Colony optimization (IACO) algorithm is proposed to realize multi-robot viewpoint allocation and sequential planning on the optimal viewpoint set. Finally, in order to evaluate the effectiveness of the proposed method, a 4-robot optical inspection case of an auto body was used. Compared to the benchmark method, the number of viewpoints is reduced by 56.38%, and the bottleneck time in the station is reduced by 7.651%.

【文献 302】
作者: Hu, F
来源: AUTOMATION IN CONSTRUCTION
题目: Digital twin-based fatigue life assessment of orthotropic steel bridge
摘要: Fatigue cracks are a major issue affecting the lifespan and operation and maintenance (O&M) costs of bridges with orthotropic steel decks (OSDs), while current practices for detecting fatigue cracks often rely on manual inspection with time inefficiency. This paper presents a digital twin framework that employs robots equipped with nondestructive testing devices for data collection and deep learning algorithms for data analytics, aiming to enable automatic detection of cracks and assessment of fatigue life. Inspected crack are fed into a finite element model constructed via ABAQUS-FRANC3D co-simulation to conduct fatigue life analysis, and an MLE-PCEKriging surrogate modeling technique is developed to facilitate rapid assessment of fatigue life. The deep learning-based crack detection achieves accuracy and recall of 95.6 % and 92.2 %, respectively, while the MLRPCE-Kriging model exhibits an MPAE of 2 %, demonstrating high accuracy. The proposed digital twin framework can guide automated bridge inspection, thereby promoting intelligent O&M management for bridges.

【文献 303】
作者: Okuma, R
来源: 2024 IEEE/RSJ INTERNATIONAL CONFERENCE ON INTELLIGENT ROBOTS AND
题目: Peristaltic Soft Robot for Long-distance Pipe Inspection with an
摘要: EMIRATES

【文献 304】
作者: Rea, P
来源: IFAC PAPERSONLINE
题目: Cyber Physical Systems: A Brief Survey and an Application of a MIR
摘要: This work refers to the realization of a Cyber Physical System (CPS), i.e. modeling, analysis, and integration of a MIR (Mobile Industrial Robot) platform with an industrial robot, in order to test its operating conditions. MIRs, although usually employed in industrial environment, can be used in sectors such as inspection, assistance, defense, production, remote exploration as well as search and rescue. In addition of being programmable, thanks to the provision of sensors and navigation systems, they can adapt in real time to various environmental contexts and purposes of use. The objective of the paper is to create a CPS of a MIR that can basically perform two fundamental actions. The first one is to carry out movements in predominantly external environments and therefore with various types of terrain. The second fundamental action is the pick and place operations, i.e. the robot must be able to carry out the manipulation of objects. Copyright (c) 2024 The Authors. This is an open access article under the CC BY-NC-ND license (https://creativecommons.org/licenses/by-nc-nd/4.0/)

【文献 305】
作者: Yin, JH
来源: ROBOTICA
题目: Design and motion mechanism analysis of screw-driven in-pipe inspection
摘要: In the pipeline industry, it is often necessary to monitor cracks and damage in pipelines, or need to clean the inside of the pipeline regularly, or collect adhesive on the inner wall of the pipe, but the pipe is too narrow and difficult for humans to enter, it is necessary to use a pipe machine to complete the work. In this paper, a newly designed screw-driven in-pipe inspection robot (IPIR) is proposed. Compared with common robots, this robot innovatively designs adapting mechanism. The robot can not only adapt to the change of the inner diameter size of the pipeline by using the bionic principle and the deformation characteristics of flexible components but also can pass smoothly in the horizontal/oblique/vertical pipelines and has a certain ability to cross obstacles. In addition, it can transmit images of the inner wall of the pipeline wirelessly for data analysis. Finally, through theoretical analysis and prototype construction, the performance of the robot is verified. The results show that the prototype robot can not only smoothly pass through the acrylic pipe with inner diameter of 120-138 mm but also pass through boss with a height of 3 mm.

【文献 306】
作者: Bruzzone, L
来源: JOURNAL OF FIELD ROBOTICS
题目: WheTLHLoc 4W: Small-scale inspection ground mobile robot with two
摘要: WheTLHLoc 4W is the second version of a hybrid leg-wheel-track small-scale ground mobile robot, developed for surveillance and inspection applications. It differs from its predecessor, WheTLHLoc 2W, in two main aspects: the number of wheels (four instead of two), and the new leg design. The overall size of the robot varies depending on the configuration: 500 x 420 x 140 mm (length x width x height) in tracked mode with lowered legs, 315 x 420 x 310 mm in tracked mode with raised legs, and 430 x 420 x 260 mm in wheeled mode. The robot is sufficiently small to explore narrow spaces, both indoors and outdoors, switching between wheeled locomotion on compact surfaces and tracked locomotion on irregular and yielding terrain. Due to its small length, the robot can stand on one tread of a stair. Nevertheless, it is capable of climbing stairs, and the step height limit has been augmented from 165 mm to 180 mm (****%) with respect to the first version, keeping constant the robot overall size. This enhancement has been achieved through a mechanical redesign of the two rotating legs: each leg now features a pair of smaller wheels, actuated by the same actuator through a gear train, instead of a single larger wheel. The significant increase in the maximum step height expands the robot's capabilities for indoor environments, as stairs with a rise higher than 180 mm are extremely uncommon. The paper discusses the mechanical design of WheTLHLoc 4W, the trajectory planning for step/stair climbing, and provides an analytical comparison between the novel architecture and the first version in terms of stability against overturn and slippage. These analytical results are then experimentally verified. The findings demonstrate that the new leg design not only increases the maximum step height (****%), but also reduces the minimum friction coefficient required for successful stair climbing (-19.1% for the maximum step).

【文献 307】
作者: Chaurasiya, KL
来源: HEALTH MONITORING OF STRUCTURAL AND BIOLOGICAL SYSTEMS XVII
题目: An innovative method and apparatus for speed control of pipe health
摘要: Periodic pigging of pipelines is essential for the inspection and maintenance of the gas pipeline network. Undetected cracks can be detrimental to pipelines and can often compromise the integrity of the pipeline. Pigging operation requires the pipeline inspection gauges to move at a moderately low yet uniform speed to inspect the defects, including corrosion, cracks, and deposits, developed in the pipeline after prolonged service. The speed of the pipe health monitoring robot (PHMR) can attain an undesirable high magnitude due to fluctuations in pressurized gas flow conditions prevailing in the pipelines. The high travel speed results in aliasing, leading to a consistent sampling of error-prone inspection data. The present study explores and expands on the previous speed control units by developing an innovative method of a novel speed control system based on the combination of deflector bypass flow and hydraulic brake mechanisms and experimentally validating it for PHMR. The speed control system developed is highly responsive to the changes in the speed of the PHMR since the incompressible nature of the brake fluid makes instantaneous transmission of pressure changes for the braking action possible. The modular nature of the developed speed control system enables it to be attached to any wheel suspension assembly-based PHMR and has been reported to passively regulate any undesirable high-speed spikes maximum by 51% within the acceptable range. The system is operated without a power supply, making it highly safe while operating in inflammable gas pipelines and a cost-effective and reliable solution that can help in accurate, effective, and seamless inspection of the gas pipelines spread over a large area of the pipeline network.

【文献 308】
作者: Zholtayev, D
来源: IEEE ACCESS
题目: Smart Pipe Inspection Robot With In-Chassis Motor Actuation Design and
摘要: In the contemporary world, inspection operations have become a critical component of infrastructure maintenance. Over the years, the demand for comprehensive inspection of pipes, both internally and externally, has grown increasingly complex and challenging. Consequently, there is a pressing need for significant advancements in in-pipe robots, particularly in the areas of inspection speed, defect detection precision, and overall reliability. Recent developments in new devices and sensors have markedly improved our capability to inspect and diagnose defects within pipes with greater accuracy. Furthermore, the application of machine learning tools has optimized the inspection process, enhancing the detection and recognition of potential pipe defects, such as rust, blockages, and welding anomalies. This research introduces a novel mobile robot platform specifically designed for pipe inspection. It integrates an advanced machine learning model that effectively detects and identifies key pipe defects, including rust, compromised welding quality, and pipe deformation. Additionally, this platform offers enhancements in inspection speed. The integration of these technologies represents a significant stride in the field of infrastructure maintenance, setting a new standard for efficiency and precision in pipe inspection.

【文献 309】
作者: Sun, HQ
来源: 2024 5TH INTERNATIONAL CONFERENCE ON MACHINE LEARNING AND HUMAN-COMPUTER
题目: Design of Pig Inventory and Abnormality Monitoring System Based on
摘要: Pig inventory is an important task in intensive asset management of pig farms. Accurate quantity can realize precise feeding, help reduce farming cost and improve farming efficiency. The realization of pig inventory through computer vision algorithms has become the main development trend of intelligent pig inventory methods [1]. At present, pig inventory mostly adopts manual inventory, which is costly, inefficient and prone to errors. Some of the existing large pig farms use camera-based pig inventory, but they are based on fixed cameras, and cannot complete the inventory of the entire pig farm. Meanwhile, sometimes pigs will jump out of the pen and can't eat, which is very dangerous especially for piglets. Design a track inspection robot carrying a camera to complete the inspection of pig farms, according to the pen information on the pig data collection, this project collected 2000 valid pictures to form a dataset, through the YOLOv5 target detection algorithm to train it, learn a training model, in order to improve the accuracy of inventory, enhancement processing of the photos, through the deep learning model training to obtain the optimal model and deployed, and finally Data correction is performed by Kalman filtering method to improve the inventory accuracy, and this project is carried out on-site experiments in pig farms of Zhengzhou Xinrong Agricultural and Animal Husbandry Information Co. The experimental results show that the overall accuracy of pig inventory is 95.6%. Compared with manual inventory, this method has low cost and good application prospect. When pigs are found in the channel or non-pen labeling, it is an abnormal situation, and the relevant personnel will be notified in time to deal with it, so as to ensure the safety of farming.

【文献 310】
作者: Xu, RY
来源: INTERNATIONAL JOURNAL OF CONTROL AUTOMATION AND SYSTEMS
题目: Trajectory Planning of Rail Inspection Robot Based on an Improved
摘要: To ensure the smooth operation of each joint and shorten the joint movement time of a rail inspection robot, a trajectory planning method based on time optimization with a penalty function is proposed. According to the Denavit-Hartenberg (D-H) model of the inspection robot, a kinematic solution is found, and the trajectory of each joint is generated using a mixed polynomial interpolation algorithm. Taking time optimization as the standard, the traditional particle swarm algorithm cannot handle complex constraints, easily falls to local optimum solutions, and has a slow convergence speed. An improved simulated annealing particle swarm algorithm with a penalty function (IPF-SA-PSO) is proposed to optimize the trajectory generated by the mixed polynomial interpolation algorithm. The simulation results show that the proposed algorithm, compared with the mixed polynomial interpolation method, can limit the angular velocity and reduce the running time of each manipulator joint. The two algorithms are experimentally verified based on a rail inspection robot, and the results show that after adopting the optimization algorithm, the angular velocity of each joint is within the angular velocity limit, the run time is shorter, and the operation is smoother, which indicates the effectiveness of the proposed algorithm. The proposed algorithm can optimize the robot running time, improve the smoothness, and be applied to the fields of the automatic tracking of abnormal targets and video acquisition.

【文献 311】
作者: Wang, G
来源: FRONTIERS IN NEUROROBOTICS
题目: Neural network aided flexible joint optimization with design of
摘要: IntroductionThe flexible joint is a crucial component for the inspection robot to flexible interaction with nuclear power facilities. This paper proposed a neural network aided flexible joint structure optimization method with the Design of Experiment (DOE) method for the nuclear power plant inspection robot. MethodsWith this method, the joint's dual-spiral flexible coupler was optimized regarding the minimum mean square error of the stiffness. The optimal flexible coupler was demonstrated and tested. The neural network method can be used for the modeling of the parameterized flexible coupler with regard to the geometrical parameters as well as the load on the base of the DOE result. ResultsWith the aid of the neural network model of the stiffness, the dual-spiral flexible coupler structure can be fully optimized to a target stiffness, 450 Nm/rad in this case, and a given error level, 0.3% in the current case, with regard to the different loads. The optimal coupler is fabricated with wire electrical discharge machining (EDM) and tested. DiscussionThe experimental results demonstrate that the load and angular displacement keep a good linear relationship in the given load range and this optimization method can be used as an effective method and tool in the joint design process.

【文献 312】
作者: Guo, ZS
来源: MEASUREMENT SCIENCE AND TECHNOLOGY
题目: Design of hexapod robot equipped with omnidirectional vision sensor for
摘要: Defect detection of inner surface of precision pipes is a crucial aspect of ensuring production safety. Currently, pipeline defect detection primarily relies on recording video for manual recognition, with urgent need to improve automation, quantification and accuracy. This paper presents a hexapod in-pipe robot with carrying capacity designed to transport the omnidirectional vision sensor to specified location within unreachable pipelines. The feasibility of the robot's mechanical design and sensor load-carrying module is analyzed using theory calculations, motion simulations and finite element method. To address the challenges of small pixel ratio and weak background changes in panoramic images, a tiny defect segmentor based on ResNet is proposed for detecting tiny defects on the inner surface of pipelines. The hardware and software systems are implemented, and the motion performance of the pipeline robot is validated through experiments. The results demonstrate that the robot achieves stable movement at a speed of over 0.1 m s-1 and can adapt to pipe diameter ranging from of 110 to 130 mm. The novelty of the robot lies in providing stable control of the loaded vision sensor, with control precision of the rotation angle and the displacement recorded at 1.84% and 0.87%, respectively. Furthermore, the proposed method achieves a detection accuracy of 95.67% for tiny defects with a diameter less than 3 mm and provides defect location information. This pipeline robot serves as an essential reference for development of in-pipe 3D vision inspection system.

【文献 313】
作者: Ji, ZC
来源: IEEE ROBOTICS AND AUTOMATION LETTERS
题目: Design and Control of a Snake Robot With a Gripper for Inspection and
摘要: This letter presents a snake robot with a gripper for inspection and maintenance in narrow spaces. The proposed robot has a gripper equipped with a camera and a laser distance sensor that can inspect the surroundings and grasp objects. The control methods of the proposed robot consist of three parts: 1) A double-layer controller based on central pattern generator (CPG) realizes the locomotion of the robot in narrow spaces. 2) A circular posture is proposed to avoid rollover and improve the payload capacity when the robot is grasping or manipulating an object on either side. 3) A three-stage grasping strategy is proposed to realize semi-automatic grasping operations. A prototype of the robot has been developed and the effectiveness of the proposed control methods have been verified in the payload capacity test, target grasping test, and pick-transport-place experiment.

【文献 314】
作者: Li, XP
来源: PROCEEDINGS OF THE INSTITUTION OF MECHANICAL ENGINEERS PART C-JOURNAL OF
题目: Dynamic performance analysis based on the mechatronic system of power
摘要: Dynamic performance of the power transmission line inspection robots (PTLIRs) is of great importance, which would heavily affect the stability of the robot during obstacle avoidance. In this paper, a detailed dynamic performance analysis method of PTLIR is presented by considering the effect on the mechatronic system caused by obstacle avoidance. First, the dynamic modeling of the robot and control system model of the revolute joint are given based on the mechatronic modeling. Then, dynamic characteristics of mechanical subsystem are analyzed by studying the change of the inertia matrix during obstacle avoidance. Next, to analyze the control subsystem performance, three kinds of pole assignment methods which used to tune the controller parameter are compared. Finally, a series of numerical simulations are performed, which prove the time-varying characteristic of load inertia during obstacle avoidance has a great influence on the dynamic performance of the robot, moreover, the changes of natural angular frequency and damping coefficient parameters could be used to evaluate dynamic performance. The main results of the paper can provide an effective tool to analysis dynamic performance of the PTLIR, which is meaningful to improve inspection stability.

