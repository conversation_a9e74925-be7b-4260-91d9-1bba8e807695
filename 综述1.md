## 机器人裂缝检测综述：从平台到部署算法的系统级视角

### 摘要：

土木基础设施的安全性与使用寿命在很大程度上依赖于结构健康监测（SHM）的有效性，而裂缝是最主要的退化指示因素之一。尽管机器人系统已成为替代危险人工检查的一种变革性方案，但在算法进展与其在实际可部署机器人平台中稳健集成之间，仍存在显著鸿沟。本文从系统层面出发，全面回顾了机器人裂缝检测技术，系统梳理了从硬件到部署智能的完整技术链。首先，文章对构成检查系统物理基础的多种机器人平台（如地面、空中、攀爬式）与多模态传感器组合进行分类与分析。研究核心则深入探讨感知算法的演变过程，涵盖从传统图像处理到最先进的深度学习模型的发展轨迹，重点分析用于设备端推理的架构创新与轻量化设计。更重要的是，本研究还桥接了理论与实践，评估了支持机器人自主行为的部署策略与系统集成架构。本文的主要贡献在于提出一种系统级协同设计方法，强调算法与硬件的协同优化是实际部署的基础，这一点是现有研究普遍忽视的。最后，本文还深入剖析了当前面临的挑战，并明确指出未来的研究方向，如基础模型、数据中心式AI与多机器人系统等，为构建下一代鲁棒、高效、可信的机器人检查系统提供战略性路线图。

**关键词：** 机器人检测、裂缝检测、结构健康监测（SHM）、无人机（UAV）、深度学习、系统集成、传感器融合

---

## 1 引言

### 1.1 研究背景与意义

桥梁、隧道、水坝与管道等土木基础设施构成现代社会的基石 \[1, 2]。然而，这些资产的结构完整性在不断老化、环境应力与运营载荷的作用下逐渐受损，常表现为表面裂缝。由于裂缝可能扩展并引发灾难性结构失效，及时的裂缝检测是结构健康监测（SHM）中不可或缺的一环，对基础设施运维与公共安全至关重要 \[3]。

然而，传统的人工检查方式存在主观性强、成本高昂、且对人员安全构成威胁等问题 \[1, 4–8]。这些挑战促使检查方式向自动化系统转型，依托机器人与人工智能技术的发展 \[9]。

过去十年，大量研究表明自动化系统具有巨大潜力。如今，高精度的深度学习模型已能高效检测裂缝 \[10, 11]，而多样化的机器人平台（包括无人机 \[12, 13]、地面移动机器人 \[14, 15] 以及专用攀爬机器人 \[16, 17]）则为复杂结构环境提供了前所未有的访问能力。然而，尽管各个组成部分取得了显著进展，一个关键挑战也日益凸显：即如何将实验室中高性能的感知算法可靠地部署到实际运行的自主机器人系统中。这一从学术原型到现场部署解决方案的成功转化，依赖于一种目前文献中尚不充分探讨的“系统级”整体方法。

---

### 1.2 系统的核心组成与子问题

本研究聚焦于**机器人裂缝检测**这一融合了机器人技术、计算机视觉与土木工程的交叉研究领域。该方向最初源于将机器人与无损检测（NDE）传感器集成的系统 \[18, 19]，其核心任务是：通过机器人自主导航结构表面，采集传感数据（通常为图像），并进行处理以识别裂缝、评估其属性并进行定位与建图。

本研究定义了几个关键术语与子问题：

* **机器人平台（Robotic Platform）**：指用于数据采集的移动机器人系统 \[1, 20]。其类型多样，包括可迅速抵达高空或偏远区域的**无人机（UAV）** \[21]、适合地面与桥面等区域的**地面机器人（UGV）**，以及适合钢结构表面作业的**攀爬机器人** \[17, 22]。平台性能通常通过其移动性、稳定性与载重能力衡量。
* **传感系统（Sensor System）**：指集成于机器人平台上的多种传感器 \[23, 24]。常用传感器包括**高分辨率RGB相机**（用于表面视觉检测）、**激光雷达（LiDAR）**与**RGB-D相机**（提供三维建图与定位信息 \[13, 15]）、**热成像相机**（检测表面下裂缝或湿度 \[25]）与**超声传感器**（量化裂缝深度 \[26]）。多模态数据的融合对于实现全面评估至关重要。
* **定位与建图（Localization and Mapping）**：为实现自主运行并使缺陷定位具有实际意义，机器人必须能够确定其相对结构的位置与姿态，并构建环境地图。常采用\*\*同步定位与建图（SLAM）\*\*技术 \[27, 28]，尤其适用于桥下或隧道等GPS信号受限的场景 \[2, 13]。
* **裂缝检测算法（Crack Detection Algorithm）**：用于处理传感器数据并识别裂缝的计算方法 \[4, 5]。算法已从**传统图像处理技术**（如边缘检测、阈值分割）发展至**机器学习**与最新的**深度学习方法** \[15]。现代算法目标不仅是检测，还包括语义分割（像素级定位）与裂缝量化（长度、宽度、方向等）。
* **路径规划（Path Planning）**：旨在为机器人规划最优运动路径，确保对目标结构表面的完整、有效覆盖 \[29, 30]。路径需具备可行性、避障能力，并确保传感器处于合适距离与视角，以获取高质量数据。

综上所述，仅有高精度算法远远不够，真正的挑战是构建一个能够在真实环境中自主、稳定运行的完整机器人系统。

---

### 1.3 本研究的贡献与结构安排

本文的主要贡献是提出一种系统级整合方法，明确将机器人平台与传感器（系统“躯体”）与感知算法（系统“智能”）连接起来。与以往以算法为中心的综述不同，本研究强调部署导向视角，关注在资源受限的硬件条件下如何实现算法的集成与控制自主。为此，文章系统梳理了裂缝检测算法的发展路径，并对当前挑战与未来趋势进行了战略性分析。

文章结构安排如下：第2章介绍各种机器人平台与传感系统，第3章深入探讨裂缝感知算法的发展，第4章分析算法部署与系统集成的实践问题，第5章讨论面临的挑战与研究趋势，第6章总结全文并展望未来发展方向。

---

## 2 机器人平台与传感系统

自动裂缝检测方案能否成功实施，很大程度上取决于其底层硬件的能力。一个完整的机器人检测系统由移动平台和传感器组成：前者提供移动与访问能力，后者负责数据采集。本章系统梳理了构成现代结构检测基础的多样化机器人平台与多模态传感器系统，并按其操作领域与传感器技术分类。

---

### 2.1 用于裂缝检测的机器人平台

机器人平台的选择是系统设计中的首要决策，直接影响能否有效导航目标环境并实现高质量感知。如表2.1所示，这些平台按主要作业环境可分为地面/攀爬、空中与水下/狭小空间三类，各具优劣。

地面与攀爬式平台是近距离高精度检测的关键。例如，**RABIT** 系统是一种用于桥面检测的UGV，强调高稳定性 [18]；四足机器人如 **ANYmal** 和 **X30** 则具备在变形地形如变电站、工地穿行的能力 [6, 10]。攀爬机器人种类繁多，包括磁轮机器人 [16, 17]、吸附式与类尺蠖机器人如 **HMICRobot**，适用于钢箱梁内部检测 [31]。这些平台具有较强稳定性与承载能力，但在垂直表面作业时速度较慢，且对地形较敏感。

相比之下，空中平台（特别是多旋翼无人机）在风电塔、高架桥等高空结构的快速覆盖方面表现优异 [8, 12, 32, 33]，但在强风中稳定性差，续航与载荷能力有限。

最后，水下与管道机器人（如AUV）是检测水下桥墩、管道、海洋平台等结构的关键设备，但需克服极端环境与GPS缺失等困难 [2, 21, 34]。值得注意的是，所有现代机器人平台普遍集成传感器（如LiDAR、相机、IMU），为后续的定位与自主导航提供基础 [13, 27, 28, 35]。

---

**表2.1.** 不同类别机器人平台在裂缝检测中的对比

| 平台类别          | 子类型与实例                                                                                                                             | 主要作业领域             | 优势                     | 局限与挑战                              |
| :------------ | :--------------------------------------------------------------------------------------------------------------------------------- | :----------------- | :--------------------- | :--------------------------------- |
| **地面/攀爬平台**   | 轮式/履带式UGV（如 RABIT [18]）、四足机器人（如 ANYmal [6]、X30 [10]）、磁附/吸附式爬壁机器人 [16, 36]、带机械臂的系统（如 ROBO-SPECT [26]）、尺蠖机器人（如 HMICRobot [31]） | 桥面、路面、建筑立面、钢结构、隧道等 | 稳定性高、承载力强，适合近距离或接触式NDE | 地形受限、垂直表面速度慢、控制系统复杂                |
| **空中平台**      | 多旋翼UAV [12, 13]                                                                                                                   | 高层建筑、桥梁上部结构、风电塔等   | 快速覆盖大范围、高空/远距访问能力强     | 容易受风干扰、续航与载荷有限、需维持适当飞行距离 [32, 33] |
| **水下与狭小空间平台** | USV/UUV/AUV [21]，管道机器人 [34]                                                                                                      | 桥墩、水下基础设施、管网、污水道   | 可进入浸水、危险或封闭空间          | GPS不可用、通信受限、环境条件恶劣                 |

---

### 2.2 多模态传感器系统

裂缝检测的有效性高度依赖于集成的多模态传感系统，以实现对结构表面与内部缺陷的全面感知。当前趋势是融合多种类型的传感器数据，以提升检测的全面性与鲁棒性。
以下是你提供的英文内容的中文翻译，保持了SCI综述的正式风格：

---

#### 2.2.1. 视觉与光学传感器

视觉与光学传感器是非接触式机器人检测的基石，但其有效性依赖于克服环境挑战并充分利用几何上下文信息。

* **高分辨率RGB相机：** 作为裂缝检测的主要传感器，RGB相机对环境条件极为敏感。**Jose等人 [32]** 定量研究了运动模糊对测量精度的负面影响，指出检测速度与数据质量之间的权衡。为应对低对比度和噪声等问题，尤其是在电缆隧道等复杂环境中，**Zhao等人 [37]** 构建了一套专门的图像增强流程，结合混合优化模块与特征融合机制，以恢复丢失的纹理细节并提升整体图像质量，为后续分析打下基础。

* **三维几何传感器（LiDAR、RGB-D、结构光）：** 这些传感器对于获取结构几何信息至关重要。例如，**Zhang等人 [9]** 在车底检测机器人中引入3D固态激光雷达，并将其集成进SLAM框架中，有效应对了重复特征场景下的定位问题。**Huang等人 [17]** 更进一步，采用分布式2D LiDAR系统，不仅实现了导航功能，还用于精确确定车辆停车姿态，从而指导检测机器人的部署。在精细结构分析方面，**Yu等人 [38]** 将语义分割与3D重建技术结合，恢复出结构的真实表面形貌，建立了完整的结构外观模型。

* **热成像（红外）相机：** 超越可见光谱范围，热成像相机可揭示如脱层等亚表面缺陷。其中最有效的方法多采用主动热成像技术。例如，**Rodríguez等人 [5, 6]** 开发的机器人系统结合热风鼓风机与红外相机，通过加热形成热对比并监测热扩散模式，成功实现了瓷砖空鼓的区域分割。这种方法体现了一种主动感知策略，机器人主动激发环境以揭示隐藏缺陷。

#### 2.2.2. 非视觉传感器集成

全面的结构评估不仅需要表层信息，还要求深入结构内部并确保鲁棒导航，因此非视觉传感器的集成至关重要。

* **导航与定位传感器：** 惯性测量单元（IMU）是状态估计的基础，特别是在与LiDAR紧耦合的SLAM框架中，IMU的数据可有效提升无GPS环境下的轨迹估计鲁棒性。这在如 **LIO-SAM [27]** 和 **FAST-LIO [28]** 等系统中已被广泛验证，是从无人机 [13] 到地面移动平台 [39] 的自主机器人系统中不可或缺的核心组件。

* **无损检测（NDE）传感器：** 为量化损伤，机器人配备了专用的无损检测传感器，且其选择高度依赖于检测材料的特性。例如，**Pfändler等人 [24]** 展示了在自主NDT中的重要进展：利用搭载传感探头的六旋翼无人机，实现了混凝土半电池电位与电阻率的接触式测量，从而可实现早期腐蚀的识别。针对高温金属结构检测，**Dalmedico等人 [40]** 研发了CRAS爬壁机器人，配备相控阵超声传感器，可对最高135°C环境下的工业容器焊缝进行精确检测，成功解决了高温与高精度检测的双重挑战。

#### 2.2.3. 数据获取与传感器集成实践

有效的多传感器系统集成需将各类数据融合为统一、信息丰富的模型，这要求精准的传感器布置、标定与同步。典型实例包括将二维裂缝特征投影到三维点云中，生成地理参考的损伤地图 [15, 38]，这一关键步骤为后续的缺陷时序跟踪提供支撑。然而，该融合过程依赖于高精度的时空对齐，这需通过**外参标定**（确定传感器间相对位姿）与**时间同步**实现，后者容忍度极低，毫秒级误差即可能导致紧耦合估计器严重失效 [27, 41]。

下表（表2.2）汇总了裂缝检测中常用的传感器类型及其关键特性：

<br>

**表2.2.** 用于机器人裂缝检测的传感器类型及其关键特征。

| 传感器类型                | 主要功能                 | 相关机器人任务            | 主要局限性                  |
| -------------------- | -------------------- | ------------------ | ---------------------- |
| **RGB相机**            | 获取高分辨率纹理和颜色信息        | 裂缝检测、视觉伺服          | 易受光照影响、存在运动模糊、仅能获取表层信息 |
| **激光雷达（LiDAR）**      | 获取精确的三维点云几何数据        | 地图构建、定位（SLAM）、导航避障 | 数据稀疏、成本较高、对高反射或透明表面不敏感 |
| **热成像相机**            | 测量表面温度分布，揭示亚表面缺陷     | 亚表面缺陷检测（如脱层、空洞）    | 需人工制造热对比、图像解释复杂、分辨率低   |
| **IMU**              | 测量线加速度与角速度，用于状态估计与定位 | 状态估计、SLAM定位        | 存在长期漂移、需与其他传感器融合提升精度   |
| **GPR / 涡流 / 声波传感器** | 检测材料内部结构或缺陷          | 亚表面/内部缺陷量化         | 通常需接触或近距离作业、对材料敏感、载荷较大 |
| **RGB-D / 结构光**      | 提供高密度、短距离的三维深度图像     | 局部建图、导航避障、三维重建     | 有效距离短、对环境光（尤其是结构光）敏感   |

<br>

随着多传感器机器人平台的普及，挑战已从数据采集转向**智能解析**。下一章节将探讨如何利用先进的学习方法，从上述丰富的多模态数据流中提取可操作的检测洞见。

综上所述，本章系统回顾了构成检测系统“身体”的硬件平台。下一章（第3章）将深入分析该系统的“脑”——即使检测机器人具备感知能力的核心算法演进过程。


