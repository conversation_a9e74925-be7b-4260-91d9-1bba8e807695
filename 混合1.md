【文献 1】
作者: Chen, XB
来源: SENSORS
题目: Autonomous Crack Detection for Mountainous Roads Using UAV Inspection
摘要: Road cracks significantly affect the serviceability and safety of roadways, especially in mountainous terrain. Traditional inspection methods, such as manual detection, are excessively time-consuming, labor-intensive, and inefficient. Additionally, multi-function detection vehicles equipped with diverse sensors are costly and unsuitable for mountainous roads, primarily because of the challenging terrain conditions characterized by frequent bends in the road. To address these challenges, this study proposes a customized Unmanned Aerial Vehicle (UAV) inspection system designed for automatic crack detection. This system focuses on enhancing autonomous capabilities in mountainous terrains by incorporating embedded algorithms for route planning, autonomous navigation, and automatic crack detection. The slide window method (SWM) is proposed to enhance the autonomous navigation of UAV flights by generating path planning on mountainous roads. This method compensates for GPS/IMU positioning errors, particularly in GPS-denied or GPS-drift scenarios. Moreover, the improved MRC-YOLOv8 algorithm is presented to conduct autonomous crack detection from UAV imagery in an on/offboard module. To validate the performance of our UAV inspection system, we conducted multiple experiments to evaluate its accuracy, robustness, and efficiency. The results of the experiments on automatic navigation demonstrate that our fusion method, in conjunction with SWM, effectively enables real-time route planning in GPS-denied mountainous terrains. The proposed system displays an average localization drift of 2.75% and a per-point local scanning error of 0.33 m over a distance of 1.5 km. Moreover, the experimental results on the road crack detection reveal that the MRC-YOLOv8 algorithm achieves an F1-Score of 87.4% and a mAP of 92.3%, thus surpassing other state-of-the-art models like YOLOv5s, YOLOv8n, and YOLOv9 by 1.2%, 1.3%, and 3.0% in terms of mAP, respectively. Furthermore, the parameters of the MRC-YOLOv8 algorithm indicate a volume reduction of 0.19(x106) compared to the original YOLOv8 model, thus enhancing its lightweight nature. The UAV inspection system proposed in this study serves as a valuable tool and technological guidance for the routine inspection of mountainous roads.

【文献 2】
作者: Chen, JZ
来源: IEEE TRANSACTIONS ON INTELLIGENT VEHICLES
题目: Refined Crack Detection via LECSFormer for Autonomous Road Inspection
摘要: Due to the rising cost of human resources in road maintenance and the pursuit of efficiency, autonomous road inspection vehicles are developed for intelligent detection of road disease to prevent severe traffic disasters in the early stages. Nevertheless, as a prevalent road disease, road cracks are diverse and susceptible to shadows, weather changes, and noise in data acquisition. Moreover, they usually appear with thin shapes that are hard to detect correctly by existing methods. To handle this problem, more details of the road cracks need to be better analyzed. In this article, we propose a refined road crack detection method named locally enhanced cross-shaped windows transformer (LECSFormer), which adopts a delicate design of the encoder-decoder structure. The encoder employs window-based transformer blocks to model long-range dependencies. Each transformer block ensembles the locally enhanced module to enrich the local contextual information, and token shuffle operation is applied to build cross-windows connections. The decoder uses dense connections to fuse multi-scale information, and the feature fusion module fuses hierarchical features and reweights them by the channel attention mechanism. The proposed method outperforms other state-of-the-art methods with ODS of 0.963, 0.917, 0.952, and 0.953 on four challenging datasets, CrackTree260, CrackLS315, Stone331, and CRKWH100. It can accurately detect cracks in road surfaces and support intelligent preventive maintenance of roads.

【文献 3】
作者: Chen, ZH
来源: SENSORS AND ACTUATORS A-PHYSICAL
题目: Inchworm-inspired micro-inspection robot: Autonomous design for
摘要: The inspection of aero-engines presents significant challenges due to the complexity of manual maintenance and the limitations of traditional cable-driven robots, which struggle to follow engine rotations and offer limited degrees of freedom and restricted access. To address these issues, this study introduces a five-degree-of-freedom (5DOF) inchworm-inspired cableless robot, equipped with deep learning-based autonomous motion strategies, specifically designed for internal aero-engine inspections. The robot's design draws inspiration from the natural locomotion of inchworms, providing enhanced flexibility and mobility within the confined and complex internal structures of aero-engines. The proposed design includes innovations such as a multi-fold leech-inspired vacuum suction mechanism, which achieves rapid adhesion and release without the need for a vacuum release device. This mechanism increases the suction capacity by 36.5 % and improves adaptability to the curved surfaces of the engine's inner walls. Additionally, a deep learning-based autonomous motion control strategy, utilizing monocular depth estimation and edge detection algorithms, enables the robot to perform autonomous obstacle avoidance and path tracking with a video recognition frame rate of 15 FPS. The robot's performance, including foot adhesion, path-following capabilities, and autonomous obstacle avoidance, was experimentally validated, demonstrating stable and autonomous operation within the aero-engine environment.

【文献 4】
作者: Zhang, BY
来源: INTERNATIONAL ARAB JOURNAL OF INFORMATION TECHNOLOGY
题目: Power Inspection Robot Dog Inspection Line Planning and Autonomous
摘要: JOURNAL OF INFORMATION TECHNOLOGY

【文献 5】
作者: Rodríguez, H
来源: SYNERGETIC COOPERATION BETWEEN ROBOTS AND HUMANS, VOL 1, CLAWAR 2023
题目: Swerve Drive Autonomous Robot for Tiles Thermographic Inspection
摘要: Quality inspection of buildings is a task that can be tedious and prone to human errors, so the use of autonomous mobile robots is very attractive. In this work, a swerve drive mobile robot is proposed for tile hollowness inspection using an active thermographic approach. It integrates a hot air blower as heating source and an infrared thermal camera to observe the hollow region contour, which is the result of the characteristics of the thermal diffusion process in different media. In addition, image processing techniques were used to segment the hollow region contour. As a result, an automatic, fast and effective inspection strategy is proposed.

【文献 6】
作者: Rodríguez, H
来源: ADVANCES IN AUTOMATION AND ROBOTICS RESEARCH, LACAR 2023
题目: Swerve Drive Autonomous Robot for Tiles Thermographic Inspection
摘要: Quality inspection of buildings is a task that can be tedious and prone to human errors, so the use of autonomous mobile robots is very attractive. In this work, a swerve drive mobile robot is proposed for tile hollowness inspection using an active thermographic approach. It integrates a hot air blower as heating source and an infrared thermal camera to observe the hollow region contour, which is the result of the characteristics of the thermal diffusion process in different media. In addition, image processing techniques were used to segment the hollow region contour. As a result, an automatic, fast and effective inspection strategy is proposed.

【文献 7】
作者: Merkle, D
来源: OPTICAL SENSING AND DETECTION VIII
题目: Autonomous measurement robotics for advanced mapping and inspection
摘要: Mapping the environment is the basis for measurement tasks, planning processes, monitoring over time, and decision-making. Moreover, the inspection of engineering structures, roads and railways, water and wastewater, and energy infrastructure is essential to ensure safety and sustainability. For an automated mapping and inspection process, the use of robotic systems is indispensable. Due to the complexity of the environments of structures and the requirements for inspection, the automated operation requires real-time navigation, specific sensor solutions, and automated interpretation of the data. In this study, we present various concepts of autonomous measurement robotics for mapping and inspecting challenging environments and damage types. Robotic systems based on unmanned aerial vehicles, unmanned ground vehicles, and unmanned underwater vehicles are addressed for various scenarios including pipes, dams, bridges, tunnels, coastal areas from the air, and underwater structures. After identifying the demands and needs and presenting the state-of-the-art methods and systems and their identifying limitations, we present ways for an autonomous operation of robotic systems equipped with specific sensors for LiDAR-based subsurface damage detection of cavities or delaminations, underwater laser scanning, high-resolution bathymetry, and multispectral LiDAR for moisture detection. This includes real-time data interpretation, iterative inspection strategies, automated data interpretation, and the strategy for the integration of the human operator. In conclusion, this work gives an overview of existing methods and systems and their limitations for mapping and inspection tasks. Moreover, we show how advanced sensor systems can be used as part of autonomous robotic systems to overcome current limitations for high-quality mapping and inspection of challenging environments and structures.

【文献 8】
作者: Huang, BZ
来源: 2023 9TH INTERNATIONAL CONFERENCE ON MECHATRONICS AND ROBOTICS
题目: Development of BIM Semantic Robot Autonomous Inspection and Simulation
摘要: Building information modeling (BIM) has been widely used in the construction industry. By coordinating the collaboration of diverse construction participants, architectural design and project management has been improved efficiency. The application of BIM has been extended with the application of mobile robots in construction intelligence and automation. The abundance of precise geometric and semantic information provided by the BIM model could assist and even directly guide the robot to complete various tasks more precisely and productively. In this paper, We proposed a method to use BIM to deploy the robot system simply and efficiently for building progress monitoring. In our method, BIM is utilized not only to provide robots with environment geometric and semantic information but also generate detailed actions for construction tasks to guide robots to perform inspection. The results have proved that by our method, the robot system has quickly performed a independent navigation in the new environment and simultaneously update the progress information without exploring the map in advance.

【文献 9】
作者: Zhang, MH
来源: IFAC PAPERSONLINE
题目: Autonomous Localization and Motion Control of Under-Vehicle Inspection
摘要: This paper focuses on the train under-vehicle inspection robot equipped with 3D solid- state LiDAR and laser distance sensor. Dedicated 3D Simultaneous Localization and Mapping (SLAM) framework and motion control algorithm are proposed for the robot operating in the hazardous environment of the inspection pit. The constrained working environment includes repetitive and monotonous spatial features makes autonomous localization and navigation task a challenging issue. The proposed SLAM localization framework has a complete architecture that includes frontend laser odometry and loop closure detection. The method optimized for 3D solid- state LiDAR is utilized for feature extraction. The pose optimization problem is initialized with the motion estimation provided by wheel odometry. The wheel odometry interpolation is utilized to increase the pose update frequency. Moreover, the features of the vehicle wheels in the measurements of laser distance sensor are utilized for loop closure detection. The proposed motion control algorithm extracts the side features of the inspection pit for feedback of angular velocity closed-loop control. The experimental results from simulation and real-world show that the robot can be driven accurately along a straight trajectory in the inspection pit with a maximum integrated localization and navigation error of less than 0.015m in movement over 200m, which demonstrates the effectiveness of the proposed algorithms. Copyright (c) 2023 The Authors. This is an open access article under the CC BY-NC-ND license (https://creativecommons.org/licenses/by-nc-nd/4.0/)

【文献 10】
作者: [Anonymous]
来源: MATERIALS EVALUATION
题目: DEEP ROBOTICS' X30 ROBOT DOG PIONEERS SMART INSPECTION IN SINGAPORE
摘要: N/A

【文献 11】
作者: Ginting, MF
来源: 2024 IEEE/RSJ INTERNATIONAL CONFERENCE ON INTELLIGENT ROBOTS AND SYSTEMS
题目: Semantic Belief Behavior Graph: Enabling Autonomous Robot Inspection in
摘要: EMIRATES

【文献 12】
作者: Aziz, A
来源: COMPUTING IN CIVIL ENGINEERING 2023-DATA, SENSING, AND ANALYTICS
题目: Fully Autonomous Fire Safety Equipment Inspection Missions on a Legged
摘要: As building automation becomes more prevalent, smart buildings will be integrated into smart cities. Using artificial intelligence (AI) is expected to increase efficiency and the automation level throughout the whole life cycle of building information modeling (BIM). Especially for the management phase of a building, facility managers are recognizing the value of machine learning and robotics for the automation of different maintenance tasks. In the fire safety management field, documentation of fire safety equipment (FSE) is required due to recurring maintenance work, system changes, and relocations. This study concentrates on the automatic detection of inspection tags on FSE using a YOLO network and its deployment on a mobile robot. The goal is to create a fully autonomous inspection mission for a legged robot by executing predefined routes, identifying FSE, and extracting necessary information, such as the next maintenance date tags.

【文献 13】
作者: Chen, JJ
来源: 2024 9TH INTERNATIONAL CONFERENCE ON AUTOMATION, CONTROL AND ROBOTICS
题目: Autonomous Inspection for Mobile Robot Based on Visual and Inertial SLAM
摘要: This paper introduces a position acquisition method and post-matching processing technique tailored for Simultaneous Localization and Mapping (SLAM) based on visual -inertial fusion. In visual-inertial SLAM systems, the position estimation accuracy is improved by trust region dogleg optimization algorithm combined with manifold optimization in response to the problem of the limited accuracy of Direct Linear Transform (DLT) in the initialization stage. An inlier set generation technique based on second-order spatial compatibility (SC2) is proposed to improve the problem of partial error matching which is unavoidable in feature matching. In the feature tracking stage, a number of compatible point pairs are selected as seed points. In the loop closing stage, points with higher confidence are selected as seed points based on the principal eigenvector of the adjacency matrix of the graph. The VI-SLAM system is deployed and integrated to the physical prototype of the inspection robot, and the inspection capability of the robot is verified in indoor and outdoor scenarios with different characteristics. The results show that the inspection robot system built in this paper can stably complete inspection tasks in multiple scenarios.

【文献 14】
作者: Ma, WH
来源: COMPUTERS AND ELECTRONICS IN AGRICULTURE
题目: Autonomous inspection robot for dead laying hens in caged layer house
摘要: Daily inspections of individual laying hens in large-scale egg farms are both labor-intensive and time-consuming, requiring farm staff to manually check each caged hen and promptly remove any deceased birds to prevent the spread of disease within the battery cages. To streamline this process, a specialized robot has been developed to enhance inspection efficiency, reduce manual labor, and enable rapid identification of dead hens. This inspection robot integrates cutting-edge technologies such as deep learning for real-time detection and identification, QR code-based positioning for precise localization, and autonomous navigation for seamless movement through the farm. It automates the otherwise tedious inspection process by visualizing and pinpointing the location of dead hens within the cages. In experimental tests, the robot achieved a detection accuracy of 90.61 % by incorporating a supplementary lighting system, setting an inspection speed of 9 m per minute, and fine-tuning the inspection algorithm with a probability value parameter of 0.48 and an area ratio parameter of 0.05. Additionally, the robot demonstrated a low false detection rate of 0.14 % and a minimal obvious false detection rate of 0.06 %. Compared to traditional manual inspection methods, this robotic system not only automates the task but also significantly reduces labor requirements and improves the overall management efficiency of large-scale egg farms. With its high accuracy and speed, the robot presents a viable solution for modern poultry operations, ensuring timely removal of dead hens and contributing to better farm hygiene and animal welfare.

【文献 15】
作者: Ruscio, F
来源: OCEAN ENGINEERING
题目: Autonomous boundary inspection of Posidonia oceanica
摘要: Monitoring provides important information for the planning and execution of marine environment preservation operations. Posidonia oceanica is one of the principal bioindicators in Mediterranean coastal areas and regular monitoring activities play a crucial role in its conservation. However, an efficient observation of vast areas colonised with P. oceanica is extremely challenging and it currently requires tedious and time consuming diving activities. Autonomous Underwater Vehicles (AUVs) endowed with optical sensors could represent a viable solution in carrying out visual inspection surveys. Nevertheless, AUVs are usually programmed to perform pre-defined trajectories, which are not effective for seagrass monitoring applications, as meadows may be fragmented and their contours may be irregular. This work proposes a framework based on machine learning and computer vision that enables an AUV equipped with a down-looking camera to autonomously inspect the boundary of P. oceanica meadows to obtain an initial estimate of the meadow size. The proposed boundary inspection solution is composed of three main modules: (1) an image segmentation relying on a Mask R CNN model to recognise P. oceanica in underwater images, (2) a boundary tracking strategy that generates guidance references to track P. oceanica contours, (3) a loop closure detector fusing visual and navigation information to identify when a meadow boundary has been completely explored. The image segmentation model and the visual part of the loop closure detection module were validated on real underwater images. The overall inspection framework was tested in a realistic simulation environment, using mosaics obtained from real images to replicate the actual monitoring scenarios. The results show that the proposed solution enables the AUV to autonomously accomplish the boundary inspection task of P. oceanica meadows, therefore representing an effective tool towards the conservation and protection of marine environments.

【文献 16】
作者: Guo, W
来源: STRUCTURES
题目: Automatic crack detection method for high-speed railway box girder based
摘要: Box girders serve as crucial upper-level load-bearing components in high-speed railway simply-supported bridges, requiring sufficient structural rigidity during operation. The occurrence of cracks compromises the overall stiffness of the structure, posing significant safety risks and potentially leading to substantial loss of life and property. Therefore, it is essential to rapidly and accurately detect cracks within the girder structure, particularly in the interior of box girders where access for maintenance by personnel is inconvenient. To address this issue, this paper proposes a robot-based framework for crack detection in high-speed railway box girder, and accurately evaluate the damage status of structures. This comprehensive framework includes an image generation network for generating high-quality crack images, a lightweight object detection algorithm for rapidly identifying crack targets, and a high-precision semantic segmentation algorithm for accurately extracting crack pixels. Comparative analysis with mainstream algorithms validates the superiority of the proposed methods. Moreover, preliminary validation through simulated tests highlights the feasibility of the proposed framework, offering novel method and theoretical support for the intelligent operation and maintenance of high-speed railway bridge structures.

【文献 17】
作者: Huang, FY
来源: IEEE ACCESS
题目: Research on Autonomous Positioning Method for Inspection Robot Based on
摘要: To address the positioning problem during autonomous operation process under the automated vehicle inspection scenario for annual vehicle inspections. This paper proposes a method for autonomous positioning of inspection robot based on distributed two-dimensional LiDAR system. This method begins by utilizing cylinders stickered with retro-reflective tape to achieve high-precision joint calibration of the extrinsic parameters between distributed LiDARs and the robot-mounted LiDAR, thereby establishing a global coordinate system for the robot's navigation system. Subsequently, this method utilizes the distributed LiDARs calibration results to fuse the vehicle point clouds, it then segments and extracts vehicle feature information to obtain the vehicle's parking pose, which is transformed into the global coordinate system to realize autonomous positioning of the inspection robot. The experimental results show that the joint calibration accuracy of extrinsic parameters for multiple LiDARs can reach +/- 9mm, the positioning accuracy of the vertices of the vehicle's bounding rectangle can achieve +/- 17mm, and the detection accuracy of the vehicle door opening angle can reach +/- 0.8 degrees. This paper proposed method can improve the efficiency and accuracy of automated vehicle inspection.

【文献 18】
作者: Pfändler, P
来源: AUTOMATION IN CONSTRUCTION
题目: Non-destructive corrosion inspection of reinforced concrete structures
摘要: Autonomous non-destructive testing (NDT) on reinforced concrete structures has a large potential to overcome the limitations of current routine inspection techniques, often not capable of detecting corrosion at an early stage. Here, the development and validation of two probes, tailored to acquire contact-based NDT data with the help of a hexacopter, is presented. Each probe allows the combined measurement of two essential parameters in the condition assessment: half-cell potentials and concrete electrical resistivity. Strategies are presented to monitor probe functionality during operation and detect loss of contact between probe and structure, which is considered essential for autonomous NDT. The presented approach enables effective and reliable autonomous corrosion inspection, surpassing traditional visual inspections by localizing corrosion at an early stage, allowing engineers a better planning of maintenance. The successful application in a concrete bridge inspection sets the stage for future research in autonomous inspections with robots in various fields of applications.

【文献 19】
作者: Li, S
来源: FIRE-SWITZERLAND
题目: An Indoor Autonomous Inspection and Firefighting Robot Based on SLAM and
摘要: Indoor fire accidents have become increasingly common in recent years. More and more firefighting robots have been designed to solve the problem of fires. However, the indoor environment is very complex, with high temperatures, thick smoke, more turns, and various burning substances. In this study, a firefighting robot with autonomous inspection and automatic fire-extinguishing functions intended for use in unknown indoor environments was designed. Considering water's poor efficiency and its inability to extinguish some combustion materials, other fire extinguishers were applied to design the extinguishing system. The robot can install four different extinguishers as required and select the appropriate fire extinguisher to spray it automatically. Based on the Cartographer SLAM (simultaneous localization and mapping) theory, a robot map-building system was built using Lidar scanners, IMU (inertial measurement unit) sensors, encoders, and other sensors. The accurate identification and location of the fire source were achieved using an infrared thermal imager and the YOLOv4 deep learning algorithm. Finally, the performance of the firefighting robot was evaluated by creating a simulated-fire experimental environment. In an autonomous inspection process of the on-fire environment, the firefighting robot could identify the flame in real-time, trigger the fire-extinguishing system to carry out automatic fire extinguishing, and contain the fire in its embryonic stage.

【文献 20】
作者: Shen, Y
来源: 2023 IEEE INTERNATIONAL CONFERENCE ON SOFT ROBOTICS, ROBOSOFT
题目: Design of a Pneumatically Driven Inchworm-Like Gas Pipe Inspection Robot
摘要: Periodic inspection of aging gas pipes is important. However, the conventional inspection approach of excavation is unfriendly to the environment. From the perspective of Sustainable Development Goals (SDGs), in this study, we introduced a pneumatically driven robot system called WATER7 to observe the inner environment of aging pipes, in particular water inside these pipes, without excavation. The robot can locomote similar to an inchworm with a thrust module operating in a periodical pattern, select direction with an active bending module, and acquire images using a camera. The robot is designed and assembled within a diameter of 12[mm] to enable insertion into a gas meter valve as well as transition and retrieval from a 7[m] service pipe consisting of 8 pipe bends. To improve the driving performance, we also shortened the transit time by increasing air flow and improved the robustness of each module of the robot. Furthermore, an autonomous control system for autonomous burr avoidance based on image processing was developed. According to experiments, the robot average transit time and retrieval without damage count for the assumed scenario were 81[min] and 9 times, respectively. In addition, the autonomous burr avoidance was confirmed to be effective.

【文献 21】
作者: Fabian, S
来源: 2023 IEEE INTERNATIONAL SYMPOSIUM ON SAFETY, SECURITY, AND RESCUE
题目: Hector UI: A Flexible Human-Robot User Interface for (Semi-)Autonomous
摘要: The remote human operator's user interface (UI) is an important link to make the robot an efficient extension of the operator's perception and action. In rescue applications, several studies have investigated the design of operator interfaces based on observations during major robotics competitions or field deployments. Based on this research, guidelines for good interface design were empirically identified. The investigations on the UIs of teams participating in competitions are often based on external observations during UI application, which may miss some relevant requirements for UI flexibility. In this work, we present an open-source and flexibly configurable user interface based on established guidelines and its exemplary use for wheeled, tracked, and walking robots. We explain the design decisions and cover the insights we have gained during its highly successful applications in multiple robotics competitions and evaluations. The presented UI can also be adapted for other robots with little effort and is available as open source.

【文献 22】
作者: Wang, ZL
来源: JOURNAL OF CIRCUITS SYSTEMS AND COMPUTERS
题目: Autonomous Inspection Method of UHV Substation Robot Based on Deep
摘要: Aiming at the problem that substation robots cannot automatically find and analyze the fault equipment and carry out patrol inspection, a method of autonomous patrol inspection for ultra-high voltage (UHV) substation robots based on deep learning in cloud computing environment is proposed. Firstly, based on the cloud computing environment, an autonomous patrol system is designed to upload the data obtained by robots to the cloud platform for processing, so as to complete data analysis quickly and with high quality. Then, the deep learning algorithm (DL) is used for fault diagnosis, and the FP-growth algorithm is combined to realize the association mining of fault data, so as to clarify the patrol order of fault-related nodes. Finally, the improved ant colony algorithm (IAC) is used to optimize the path of the robot to complete the reliable inspection of the substation in the shortest time. Based on the selected UHV substation, the experimental analysis of the proposed method shows that the fault diagnosis error rate and time are about 4.3% and 14.2s, respectively, and the patrol path is only 180.351m, and the patrol time is 19.708s, which can realize the optimal patrol of the robot.

【文献 23】
作者: Marquette, W
来源: IEEE ROBOTICS AND AUTOMATION LETTERS
题目: Semi-Autonomous Teleoperation Using Differential Flatness of a Crane
摘要: Visual inspection of confined spaces such as aircraft wings is ergonomically challenging for human mechanics. This work presents a novel crane robot that can travel the entire span of the aircraft wing, enabling mechanics to perform inspection from outside of the confined space. However, teleoperation of the crane robot can still be a challenge due to the need to avoid obstacles in the workspace and potential oscillations of the camera payload. The main contribution of this work is to exploit the differential flatness of the crane-robot dynamics for designing reduced-oscillation, collision-free time trajectories of the camera payload for use in teleoperation. Autonomous experiments verify the efficacy of removing undesired oscillations by 89%. Furthermore, teleoperation experiments demonstrate that the controller eliminated collisions (from 33% to 0%) when 12 participants performed an inspection task with the use of proposed trajectory selection when compared to the case without it. Moreover, even discounting the failures due to collisions, the proposed approach improved task efficiency by 18.7% when compared to the case without it.

【文献 24】
作者: Tian, SX
来源: 2023 5TH INTERNATIONAL CONFERENCE ON CONTROL AND ROBOTICS, ICCR
题目: A Novel Intelligent Inspection Robot Navigation Strategy for Autonomous
摘要: The pipeline external inspection technology utilizing DC voltage gradient (DCVG) is widely used to detect the defects of buried pipeline anti-corrosion coating due to the merits of high precision and accuracy. However, the current DCVG detection process relies on experienced operators, leading to a substantial workload. To address these issues, a new navigation strategy is first proposed for defect searching in the pipeline anti-corrosion coating based on an intelligent inspection robot. The proposed scheme transforms the autonomous defect search into an optimization problem by utilizing the measured environmental information. Firstly, the intelligent inspection robot, incorporating the coordinated operation of track wheels and a robotic arm, is designed to enhance the stability of movement and the flexibility of detection. Secondly, an ideal polynomial regression model is established to represent the relationship between the ground potential and the distance to the defect point. Finally, a comprehensive defect point trajectory planning scheme is proposed for the designed robot. Through the four phases of the scheme: initialization, search, decision-making, and fixed-point, optimal control of the direction and walking step size is achieved without relying on location information. The accuracy and feasibility of the proposed intelligent inspection robot are verified through simulation and experiment.

【文献 25】
作者: Le, CP
来源: 2024 IEEE/RSJ INTERNATIONAL CONFERENCE ON INTELLIGENT ROBOTS AND SYSTEMS
题目: CAIS: Culvert Autonomous Inspection Robotic System
摘要: EMIRATES

【文献 26】
作者: Lupi, F
来源: 5TH INTERNATIONAL CONFERENCE ON INDUSTRY 4.0 AND SMART MANUFACTURING,
题目: CAD-based Autonomous Vision Inspection Systems
摘要: Automated industrial Visual Inspection Systems (VIS) are typically customized for specific applications, limiting their flexibility. They are characterized by a demanding setup, high capital investments, and significant knowledge barriers. In this paper, we propose an alternative architecture for the visual inspection of 3D printed parts or complex assemblies using a robotic arm equipped with hand-eye sensors and controllable lighting system. The core of the proposed Flexible Vision Inspection System (FVIS) is the self-extraction of 3D text annotations from STandard for the Exchange of Product model (STEP) AP242 files. The system selfselects and parametrizes the most suitable inspection algorithm, including lighting settings. Additionally, it autonomously performs self-localization, self-referencing of physical products, and self-planning of robot inspection path based on CAD information. This framework, characterized by self-X, cost-effective, non-invasive, and plug-and-play architecture has the potential to disrupt the business model of vision inspection, enabling an as-a-service solution aligned with the next generation of flexible manufacturing.

【文献 27】
作者: Yan, YH
来源: MEASUREMENT & CONTROL
题目: Tooth root crack detection of planet gear in industrial robot RV reducer
摘要: The windowed synchronous averaging (WSA) is widely utilized for planetary structures. However, it cannot be applied for the fault detection of the planetary structure in the industrial robot rotate vector (RV) reducer. The robot usually works within a specified angle range, which causes the RV reducer rotates incompletely. To address this issue, an angle compensation local synchronous fitting scheme is proposed. To detect the localized planet gear fault in the RV reducer, the observed vibration is equi-angle resampled. And the synchronous interference contained in the resampled vibration is constructed and removed according to the angle compensation strategy. The residual data is used to construct the synthetic vibration of the planet gear. Then, the fault feature of the planet gear can be detected. Experiments on the RV reducer test rig under the robot running conditions support the effectiveness of the proposed scheme positively.

【文献 28】
作者: Zhou, SX
来源: MEASUREMENT
题目: Enhancing autonomous pavement crack detection: Optimizing YOLOv5s
摘要: To enhance the safety and comfort of vehicle travel, detecting pavement cracks is a critical task in road management. This article introduces an advanced single-stage target detection method utilizing the YOLOv5s algorithm to enhance real-time performance and accuracy. Initially, Squeeze-and-Excitation Networks are integrated into the model to facilitate self-learning for improved crack characterization. Subsequently, anchors computed through the K-means clustering algorithm are closely aligned with the fracture dataset, achieving an adaptation rate of 99.9 % and enhancing the recall rate of the model. Furthermore, the inclusion of the SimSPPF module from YOLOv6 diminishes memory usage and expedites detection speed. By replacing the original nearest up- sampling method with transposed convolution, optimization of up-sampling for crack datasets is achieved. Performance assessments reveal that the refined YOLOv5s algorithm attains an F1 score of 91 %, a mean Average Precision (mAP) of 93.6 %, and a 1.54 % increase in frames per second (fps) for pavement crack detection. This enhancement in detection technology signifies a substantial advancement in the maintenance and longevity of road infrastructure.

【文献 29】
作者: Nieves, MYV
来源: 2023 IEEE 50TH PHOTOVOLTAIC SPECIALISTS CONFERENCE, PVSC
题目: Modular, Array-Mounted Photovoltaic Inspection Robot
摘要: Due to the exponential deployment of new photovoltaic systems in recent years, there is a pressing need for efficient array inspection methods. While drone-based methods can detect hotspots and large outages in an array, diagnostic and prognostic measurements of PV modules are more suited for stable, proximal use. To enable these measurements, students were tasked with the design and creation of a robotic platform which would autonomously traverse PV arrays, fit to various array sizes and configurations, and be able to deploy various characterization tools including imaging and spectroscopic techniques. This document details the development and demonstration of the robot.

【文献 30】
作者: Rosa, D
来源: OCEANS 2024 - SINGAPORE
题目: Forward-looking Sonar Based Autonomous Aquaculture Inspection
摘要: In recent years, there has been a rising interest in aquaculture development due to the increasing demand for food driven by population's growth. Aquaculture infrastructures require regular monitoring to assure their preservation and food safety, traditionally conducted by slow and risky manual methods involving professional divers. To solve this issue, an autonomous aquaculture net inspection using an Autonomous Underwater Vehicle (AUV) is proposed. The AUV is equipped with a Forward-Looking Sonar (FLS), which provides acoustic images of the environment. By using image processing techniques to remove noise, the vehicle's distance and orientation relative to the net cage are measured. The net cage's center is estimated, using a circle regression, to compute the relative orientation. Then, velocity and orientation references are generated, so the AUV can cover the net whole perimeter, while staying at a desired distance. Finally, many tests are performed to analyze the vehicle's behavior while encircling the net.

【文献 31】
作者: Pech-May, NW
来源: SPIE FUTURE SENSING TECHNOLOGIES 2023
题目: Robot-assisted infrared thermography for surface breaking crack
摘要: Infrared thermography using a focused (spot or line) beam has proved to be effective for detection of surface breaking cracks on planar samples. In this work, we use the same principle, but applied to complex shaped components, like a rail section, a gear, and a gas turbine blade. We use a six-axis robot arm to move the sample in front of our thermographic setup. Several scanning paths and thermographic parameters are explored: scanning speed, density of points in each scanning slice, laser power and camera frame-rate. Additionally, we explore semi-automatic evaluation algorithms for crack detection, as well as 2D-to-3D registration of the found indications.

【文献 32】
作者: Roberts, JS
来源: 2023 SEVENTH IEEE INTERNATIONAL CONFERENCE ON ROBOTIC COMPUTING, IRC
题目: A Skeleton-based Approach For Rock Crack Detection Towards A Climbing
摘要: Conventional wheeled robots are unable to traverse scientifically interesting, but dangerous, cave environments. Multi-limbed climbing robot designs, such as ReachBot, are able to grasp irregular surface features and execute climbing motions to overcome obstacles, given suitable grasp locations. To support grasp site identification, we present a method for detecting rock cracks and edges, the SKeleton Intersection Loss (SKIL). SKIL is a loss designed for thin object segmentation that leverages the skeleton of the label. A dataset of rock face images was collected, manually annotated, and augmented with generated data. A new group of metrics, LineAcc, has been proposed for thin object segmentation such that the impact of the object width on the score is minimized. In addition, the metric is less sensitive to translation which can often lead to a score of zero when computing classical metrics such as Dice on thin objects. Our fine-tuned models outperform previous methods on similar thin object segmentation tasks such as blood vessel segmentation and show promise for integration onto a robotic system.

【文献 33】
作者: Lv, ZM
来源: SENSORS
题目: Lightweight Sewer Pipe Crack Detection Method Based on Amphibious Robot
摘要: Aiming at the problem of difficult crack detection in underground urban sewage pipelines, a lightweight sewage pipeline crack detection method based on sewage pipeline robots and improved YOLOv8n is proposed. The method uses pipeline robots as the equipment carrier to move rapidly and collect high-definition data of apparent diseases in sewage pipelines with both water and sludge media. The lightweight RGCSPELAN module is introduced to reduce the number of parameters while ensuring the detection performance. First, we replaced the lightweight detection head Detect_LADH to reduce the number of parameters and improve the feature extraction of modeled cracks. Finally, we added the LSKA module to the SPPF module to improve the robustness of YOLOv8n. Compared with YOLOv5n, YOLOv6n, YOLOv8n, RT-DETRr18, YOLOv9t, and YOLOv10n, the improved YOLOv8n has a smaller number of parameters of only 1.6 M. The FPS index reaches 261, which is good for real-time detection, and at the same time, the model also has a good detection accuracy. The validation of sewage pipe crack detection through real scenarios proves the feasibility of the proposed method, which has good results in targeting both small and long cracks. It shows potential in improving the safety maintenance, detection efficiency, and cost-effectiveness of urban sewage pipes.

【文献 34】
作者: Pearson, E
来源: JOURNAL OF MECHANISMS AND ROBOTICS-TRANSACTIONS OF THE ASME
题目: Robust Autonomous Mobile Manipulation for Substation Inspection
摘要: The need for autonomous infrastructure inspections performed by mobile robots is becoming increasingly prevalent, to mitigate human error and inspect critical infrastructure with increased frequency, while reducing costs. Electric distribution substations contain a variety of high-power equipment that may occasionally fail, and we focus on inspecting pothead compartments as a representative test case. Frequent measurements of acoustic and transient earth voltage data can indicate degradation before failures occur. Handheld partial discharge (PD) sensors can gather these types of data. Measurements using PD sensors can be automated using a mobile manipulation platform with a custom end-effector. Accurate mapping, localization, and navigation are required to perform autonomous inspection tasks in indoor environments with unmanned ground vehicles. Computer vision and precise manipulator control are necessary for successful handheld sensor interactions. In this paper, we present and analyze a custom-integrated mobile manipulation system capable of performing these functions, which achieves a tradeoff between the small size needed to navigate through low-clearance areas, and the reach capabilities needed to collect the required measurements from pothead compartments.

【文献 35】
作者: Betco, D
来源: ACTA ASTRONAUTICA
题目: Autonomous navigation for the Martian Inspection Drone
摘要: The interest of science community towards planet Mars has progressively increased in past decades. Rovers are already doing an excellent job in exploring the Martian surface environment and showed that the Red Planet is not suited for life. However, there is a possibility of enabling human exploration by having astronauts take shelter in habitats located within lava tubes, which can provide protection against radiation and dust storms. These tubes are extremely hard to explore with rovers, thus a flying vehicle will be more suitable for such tasks. INGENUITY (Martian helicopter) had already demonstrated that it is possible to fly on Mars by doing small and simple manoeuvres. Based on NASA's design, this article will introduce the development of guidance, navigation, and control operations of a Martian Inspection Drone (MID) that will be capable of finding the lava tube and scan it in real time using a RealSense camera. Linux based system was chosen in the development process, which allow ROS catkin workspace to connect ArduPilot and Gazebo. The navigation is accomplished using a combination of an inertial measurement unit, a camera, and a laser rangefinder. The flight computer runs an optical flow algorithm for control and navigation purpose. Also, a convolutional neural network is used to perform lava tube entrance detection and inspection.

【文献 36】
作者: Yu, ZW
来源: AUTOMATION IN CONSTRUCTION
题目: Automatic crack detection and 3D reconstruction of structural appearance
摘要: Detecting structural surface cracks in underwater environments requires careful data collection and analysis. Deep learning enables high-precision detection unaffected by environmental factors, while 3D reconstruction algorithms provide complete structural appearance information. Combining these technologies offers an efficient underwater crack detection solution using wall-climbing robots with high-definition cameras. Therefore, this paper proposes a method for underwater crack detection and 3D structural appearance reconstruction. By employing semantic segmentation, geometric quantification, and 3D reconstruction, the actual structural surface is restored, while a label propagation algorithm ensures the integrity of cracks, thereby comprehensively displaying the structural health condition. The results show that the proposed semantic segmentation model achieved an mIoU of 0.867 and a detection speed of 21.76 FPS, the error between the width measurement method and the manual method is 7.31%, and the 3D reconstruction algorithm can achieve high-precision reconstruction within 1 to 2 h, significantly reducing costs compared to the original algorithm.

【文献 37】
作者: Zuniga, WJT
来源: HULL STRUCTURE FOR ROBOT INSPECTION
题目: FRIENDLY FPSO HULL STRUCTURE FOR ROBOT INSPECTION
摘要: The Floating Production Storage and Offload system (FPSO) is widely used in offshore oil and gas exploitation. Traditional FPSO hull structures are typically constructed employing stiffened steel panels consisting of steel plates and closely spaced stiffeners. Classification societies require periodic full inspections of the cargo tanks. Nevertheless, the structural impediments created by these stiffeners can pose challenges or even prevent using autonomous equipment for remote inspection of tank structures. Additionally, inspection surveys with people inside the tanks are time-consuming and risky. Hence, a steel-concrete-steel sandwich plate system to address this challenge is proposed as an alternative solution to the conventional stiffened plate composition. Using sandwich panels in the bottom of oil cargo tanks and their longitudinal and transverse bulkheads smooths the surfaces by eliminating the need to use stiffeners, thus facilitating remote inspections with autonomous equipment inside the tanks. Furthermore, this approach can potentially reduce the time needed for periodic inspections of tank structures, following the recommendations set by classification societies to mitigate operational risks. The article presents the results of numerical ultimate strength analyses of sandwich panels and the initial scantlings of an FPSO hull main structure built with the proposed technology. Also, a comparative analysis of the relative total weight is performed.

【文献 38】
作者: Song, B
来源: NUCLEAR ENGINEERING AND TECHNOLOGY
题目: PBIS: A Pre-Batched Inspection Strategy for spent nuclear fuel
摘要: Nuclear power plants play a pivotal role in the global energy infrastructure, fulfilling a substantial share of the world's energy requirements in a sustainable way. The management of these facilities, especially the handling of spent nuclear fuel (SNF), necessitates meticulous inspections to guarantee operational safety and efficiency. However, the prevailing inspection methodologies lean heavily on human operators, which presents challenges due to the potential hazards of the SNF environment. This study introduces the design of a novel Pre-Batched Inspection Strategy (PBIS) that integrates robotic automation and image processing techniques to bolster the inspection process. This methodology deploys robotics to undertake tasks that could be perilous or time-intensive for humans, while image processing techniques are used for precise identification of SNF targets and regulating the robotic system. The implementation of PBIS holds considerable promise in minimizing inspection time and enhancing worker safety. This paper elaborates on the structure, capabilities, and application of PBIS, underlining its potential implications for the future of nuclear energy inspections.

【文献 39】
作者: Ioannou, G
来源: IEEE AEROSPACE AND ELECTRONIC SYSTEMS MAGAZINE
题目: Underwater Inspection and Monitoring: Technologies for Autonomous
摘要: The underwater environment poses numerous challenges and risks, making unmanned underwater vehicles (UUVs) an indispensable alternative to human operators. Numerous remotely operated vehicles (ROVs) and autonomous underwater vehicles (AUVs) have been developed as a valuable resource in a broad spectrum of underwater operations. However, the deployment and operation of UUVs face significant challenges due to the unique underwater environment that critically affects positioning, navigation, and timing performance, making it incomparable to above-water applications. This discrepancy significantly impacts the decision-making process of industrial operators, particularly those in the sector of critical undersea infrastructures (CUIs). Despite advancements, they persist in the use of heavy ROVs deployed from an expensive and environmentally impactful mothership for inspection and monitoring (I&M) tasks. To explore the potential revolutionary impact on underwater operations, we analyze the resilience of CUIs, and we review the most promising robotics developments that are currently or soon to be available. The forthcoming solutions not only promise to enhance the efficiency of I&M operations, thereby bolstering the security of CUIs, but they also have the potential to transform the broader field of underwater operations as a whole.

【文献 40】
作者: Jose, N
来源: 2024 20TH IEEE/ASME INTERNATIONAL CONFERENCE ON MECHATRONIC AND EMBEDDED
题目: Motion Blur Impact on Measurement in Autonomous Inspection
摘要: This paper investigates the application of mobile robotic platforms for visual data capture in infrastructure inspection tasks. The captured data offer significant value for both manual and automated inspection processes. It can produce detailed visual information for human inspectors and serve as input for automated systems to detect anomalies or assist inspectors through computer-aided analysis. Additionally, these data can be integrated into the robot navigation system for real-time path optimisation. A critical challenge in optimising data capture is highlighted: balancing the desired precision with the time invested in inspections. The study explores this tradeoff by analysing the impact of motion blur on measurement errors. Capturing high-quality images with minimal motion blur necessitates slower inspection speeds. The findings suggest that for extensive inspection areas, prioritising mid-range object distances can optimise data capture, as errors increase at a slower pace at these distances compared to closer or farther ranges. This research paves the way for further advancements. Future areas of exploration include evaluating noise reduction techniques, incorporating real-world complexities into testing environments, and investigating the impact of capture speed on machine learning algorithms.

【文献 41】
作者: Herbers, P
来源: COMPUTING IN CIVIL ENGINEERING 2023-RESILIENCE, SAFETY, AND
题目: Autonomous Defect Inspection and Mapping for Building Maintenance
摘要: As structures age, the continuous surveying of defects to structural elements becomes a central part of the life cycle. Automated and frequent inspections could allow for an enriched defect history for faster decisions and reduce the time until problems are first reported. This requires recording the defect on an autonomous platform and automatically classifying areas of interest. We propose and test a prototype that uses a defect segmentation network for classifying defect instances deployed on a Spot mobile robot for autonomous maintenance missions. The robot maps its surroundings using a continuous laser scan and annotates the resulting environment mesh using the defect segmentation network. We test the feasibility of entirely autonomous, repeat missions and the possible use cases of such a system.

【文献 42】
作者: Andrews, T
来源: PIPELINES 2024: CONDITION ASSESSMENT
题目: Multi-Sensor Autonomous Inspection Device for Condition Assessment
摘要: PICA provides a breadth of services to asset managers, and we built a Multi-Sensor Autonomous Inspection Device (MSAID) for low-resolution condition assessment. PICA used this device to help the municipality of Jasper, AB, Canada, gather data on a fire water pipeline. The device was inserted and retrieved through fire hydrants connected to the line. The data collected by the device were used to determine a leak location. This allowed the municipality of Jasper, AB, to repair that specific section that had been causing a significant loss of water. Applications of technology are constantly evolving by becoming more efficient, resilient, and effective. Using a Multi-Sensor Autonomous Inspection Device allowed the Municipality of Jasper, AB, to gain a low-resolution data set without interrupting service.

【文献 43】
作者: McMahon, J
来源: 2023 IEEE/RSJ INTERNATIONAL CONFERENCE ON INTELLIGENT ROBOTS AND
题目: Simultaneous Survey and Inspection with Autonomous Underwater Vehicles
摘要: As the future of autonomous underwater vehicle (AUV) deployments tends to multi-vehicle systems, new approaches in coordination and control are needed. In this work, we consider the problem of simultaneous survey and inspection where one vehicle dynamically discovers objects while another vehicle must inspect as many of the objects as possible over the course of the mission. This requires a fully autonomous inspection vehicle, and to this end, we present a planning approach which couples sampling-based motion planning with timed roadmap constraints as well as a real-time execution framework. The methods presented address the underlying challenges that arise during simultaneous survey and inspection using AUVs, namely those of communication constraints, safety of navigation constraints, and dynamically discovered tasks. Additionally, we present field results for the simultaneous survey and inspection mission using teamed AUVs.

【文献 44】
作者: Sun, RH
来源: APPLIED SCIENCES-BASEL
题目: Research on Inspection Method of Intelligent Factory Inspection Robot
摘要: To address the issues of low efficiency and high omission rates in monitoring workers' compliance with safety dress codes in intelligent factories, this paper proposes the SFA-YOLO network, an enhanced real-time detection model based on a Selective Feature Attention mechanism. This model enables inspection robots to automatically and accurately identify whether the workers' attire meets the safety standards. First, this paper constructs a comprehensive dataset of safety attire, including images captured under various scenarios, personnel numbers, and operational conditions. All images are manually annotated to enhance the model's generalization capability. The dataset contains 3966 images, covering four classes: vest, no-vest, helmet, and no-helmet. Second, the proposed model integrates the SFA mechanism to improve the YOLO architecture. This mechanism combines multi-scale feature fusion with a gated feature extraction module to improve detection accuracy, strengthening the model's ability to detect occluded targets, partial images, and small objects. Additionally, a lightweight network structure is adopted to meet the inference speed requirements of real-time monitoring. The experimental results demonstrate that the SFA-YOLO model achieves a detection precision of 89.3% and a frame rate of 149 FPS in the safety attire detection task, effectively balancing precision and real-time performance. Compared to YOLOv5n, the proposed model achieves a 5.2% improvement in precision, an 11.5% increase in recall, a 13.1% gain in mAP@0.5, and a 12.5% improvement in mAP@0.5:0.95. Furthermore, the generalization experiment confirms the model's robustness in various task environments. Compared with conventional YOLO models, the proposed method performs more stably in safety attire detection, offering a reliable technical foundation for safety management in intelligent factories.

【文献 45】
作者: Rea, P
来源: ACTUATORS
题目: Hybrid Inspection Robot for Indoor and Outdoor Surveys
摘要: In this paper, simulation and experimental tests are reported for a hybrid robot being used for indoor and outdoor inspections. Automatic or tele-operated surveys can be performed by mobile robots, which represent the most efficient solution in terms of power consumption, control, robustness, and overall costs. In the context of structures and infrastructure inspection, robots must be able to move on horizontal or sloped surfaces and overpass obstacles. In this paper, the mechatronic design, simulations, and experimental activity are proposed for a hybrid robot being used for indoor and outdoor inspections, when the environmental conditions do not allow autonomous navigation. In particular, the hybrid robot is equipped with external and internal sensors to acquire information on the main structural elements, avoiding the need for experienced personnel being directly inside the inspection site, taking information from the environment and aiding the pilot to understand the best maneuvers/decisions to take. Given the current state of research and shortcomings worldwide, this paper discusses inspection robots taking into account the main issues in their use, functionality and standard systems, and how internal sensors can be set in order to improve inspection robots' performances. On this basis, an illustrative study case is proposed.

【文献 46】
作者: Huang, B
来源: SENSORS
题目: Inspection Robot Navigation Based on Improved TD3 Algorithm
摘要: The swift advancements in robotics have rendered navigation an essential task for mobile robots. While map-based navigation methods depend on global environmental maps for decision-making, their efficacy in unfamiliar or dynamic settings falls short. Current deep reinforcement learning navigation strategies can navigate successfully without pre-existing map data, yet they grapple with issues like inefficient training, slow convergence, and infrequent rewards. To tackle these challenges, this study introduces an improved two-delay depth deterministic policy gradient algorithm (LP-TD3) for local planning navigation. Initially, the integration of the long-short-term memory (LSTM) module with the Prioritized Experience Re-play (PER) mechanism into the existing TD3 framework was performed to optimize training and improve the efficiency of experience data utilization. Furthermore, the incorporation of an Intrinsic Curiosity Module (ICM) merges intrinsic with extrinsic rewards to tackle sparse reward problems and enhance exploratory behavior. Experimental evaluations using ROS and Gazebo simulators demonstrate that the proposed method outperforms the original on various performance metrics.

【文献 47】
作者: Jiang, Q
来源: IEEE TRANSACTIONS ON INDUSTRIAL ELECTRONICS
题目: Active Pose Relocalization for Intelligent Substation Inspection Robot
摘要: Intelligent inspection robots widely applied in substations are required to capture the inspection image consistent with the calibration image during routine inspection of electrical equipment. However, it is a challenging work for the inspection robot to capture the inspection image meeting the requirement due to navigation error and mechanical wear. To address this problem, an active pose relocalization (APR) method is proposed in this article. Specifically, an error model describing the relationship between the pixel error in the image plane and the robot pose error is established. Then, a decoupling three-stage proportional-integral control strategy based on the error model is provided to relocate the robot to the calibration pose, wherein, a translation error estimation algorithm based on homography transformation is proposed to compute the absolute translation scale between calibration and inspection poses, which avoid the degradation problem of the classic 2-D-2-D pose estimation algorithm. Finally, the performance of the proposed APR method is demonstrated through comparative relocalization experiments of ten calibration points in virtual and real-world environments, respectively.

【文献 48】
作者: Han, CH
来源: JOURNAL OF MANUFACTURING SCIENCE AND ENGINEERING-TRANSACTIONS OF THE
题目: Hybrid Semiconductor Wafer Inspection Framework via Autonomous Data
摘要: In smart manufacturing, semiconductors play an indispensable role in collecting, processing, and analyzing data, ultimately enabling more agile and productive operations. Given the foundational importance of wafers, the purity of a wafer is essential to maintain the integrity of the overall semiconductor fabrication. This study proposes a novel automated visual inspection (AVI) framework for scrutinizing semiconductor wafers from scratch, capable of identifying defective wafers and pinpointing the location of defects through autonomous data annotation. Initially, this proposed methodology leveraged a texture analysis method known as gray-level co-occurrence matrix (GLCM) that categorized wafer images-captured via a stroboscopic imaging system-into distinct scenarios for high- and low-resolution wafer images. GLCM approaches further allowed for a complete separation of low-resolution wafer images into defective and normal wafer images, as well as the extraction of defect images from defective low-resolution wafer images, which were used for training a convolutional neural network (CNN) model. Consequently, the CNN model excelled in localizing defects on defective low-resolution wafer images, achieving an F1 score-the harmonic mean of precision and recall metrics-exceeding 90.1%. In high-resolution wafer images, a background subtraction technique represented defects as clusters of white points. The quantity of these white points determined the defectiveness and pinpointed locations of defects on high-resolution wafer images. Lastly, the CNN implementation further enhanced performance, robustness, and consistency irrespective of variations in the ratio of white point clusters. This technique demonstrated accuracy in localizing defects on high-resolution wafer images, yielding an F1 score greater than 99.3%.

【文献 49】
作者: Yeo, MSK
来源: APPLIED SCIENCES-BASEL
题目: Recovery Motion Analysis for False Ceiling Inspection Robot
摘要: The false ceiling plenum is a common and essential part of building infrastructure. However, false ceiling infrastructure requires constant maintenance, which is cumbersome and dangerous for humans since they have to work at high heights and conduct repetitive actions for false ceiling panel replacement. As a solution, robots have been developed to inspect false ceilings. However, these robots can fall during navigation in false ceilings, such as in rugged areas. Therefore, this paper discusses the self-righting capabilities implemented on a false ceiling inspection robot known as FalconX. Mechanisms that aid in self-righting the robot back to a moving position after being toppled due to obstacles within the false ceiling environment were explored, along with their force analysis. Simulations were conducted in Gazebo environments and real hardware experiments were conducted to validate the robot's self-righting capabilities. The experimental results confirm the self-righting capability of the robot.

【文献 50】
作者: da Silva, ES
来源: SYNERGETIC COOPERATION BETWEEN ROBOTS AND HUMANS, VOL 1, CLAWAR 2023
题目: Walking Robot Applied to the Tube Inspection Activity
摘要: Over the last years, the industry increased its production and optimized the transport of items through pipelines. Despite the easy handling of sub-products, there are some problems with using the transport through tubes, such as the difficulty in maintenance and in inspection to predict future troubles. Another recurring problem in industries is the sliding of the maintenance robot along the inner walls of the tube, which can cause the robot to lose the reference of its location. In this way, the study presented here shows the type and preliminary dimensional synthesis of a new walking capsule robot that has the ability to move inside a tube. The use of legs instead of wheels increases the contact surface area between the robot and the tube, allowing greater slip resistance.

【文献 51】
作者: Mendoza, MJ
来源: 2024 IEEE INTERNATIONAL CONFERENCE ON ROBOTICS AND AUTOMATION, ICRA 2024
题目: High-Curvature, High-Force, Vine Robot for Inspection
摘要: Robot performance has advanced considerably both in and out of the factory, however in tightly constrained, unknown environments such as inside a jet engine or the human heart, current robots are less adept. In such cases where a borescope or endoscope can't reach, disassembly or surgery are costly. One promising inspection device inspired by plant growth are "vine robots" that can navigate cluttered environments by extending from their tip. Yet, these vine robots are currently limited in their ability to simultaneously steer into tight curvatures and apply substantial forces to the environment. Here, we propose a plant-inspired method of steering by asymmetrically lengthening one side of the vine robot to enable high curvature and large force application. Our key development is the introduction of an extremely anisotropic, composite, wrinkled film with elastic moduli 400x different in orthogonal directions. The film is used as the vine robot body, oriented such that it can stretch over 120% axially, but only 3% circumferentially. With the addition of controlled layer jamming, this film enables a steering method inspired by plants in which the circumference of the robot is inextensible, but the sides can stretch to allow turns. This steering method and body pressure do not work against each other, allowing the robot to exhibit higher forces and tighter curvatures than previous vine robot architectures. This work advances the abilities of vine robots-and robots more generally-to not only access tightly constrained environments, but perform useful work once accessed.

【文献 52】
作者: Lei, HH
来源: PROCEEDINGS OF THE 44TH ANNUAL AMERICAN ASTRONAUTICAL SOCIETY GUIDANCE,
题目: DEEP REINFORCEMENT LEARNING FOR MULTI-AGENT AUTONOMOUS SATELLITE
摘要: The commodification of space is resulting in larger numbers of satellites requiring increasingly complex operation and logistics and servicing support. The current standard of planning and executing maneuvers days in advance by teams of operators is not tenable, and will require endowing satellites with greater autonomy. We consider one such use case, specifically the problem of autonomous multi-agent collaborative satellite rendezvous and proximity operations, where multiple satellites collaboratively inspect a target platform for on-orbit servicing and manufacturing missions. In this scenario, the satellites must, in a distributed and coordinated way, determine how to maneuver in order to perform a full inspection of the platform. We present a hierarchical, deep reinforcement learning solution to this problem. We show that reinforcement learning provides an alternative to traditional optimal control methods for generating high-performance policies requiring minimal computational overhead once deployed.

【文献 53】
作者: Thompson, F
来源: 2024 27TH INTERNATIONAL CONFERENCE ON INFORMATION FUSION, FUSION 2024
题目: Autonomous Inspection and Data Fusion for Maritime Critical
摘要: Automation and robotics are essential for the effective monitoring and inspection of maritime critical infrastructures and marine environments. We demonstrate the use of an unmanned surface vehicle, integrated with acoustic and optical sensors, to perform fast and accurate inspections of maritime infrastructures in confined areas (i.e., presence of buildings and multiple obstacles). High resolution maps are obtained fusing offline data from LiDAR and 2D acoustic multi-beam forward looking camera point clouds. The technological pipeline is demonstrated through a survey in Copenhagen harbour. The data acquisition leverages on methods to improve path planning and localization to correct failures in the RTK GNSS due to shadowing of buildings and other obstacles. The entire data flow is streamlined to produce fast delivery time and data access, optimizing data acquisition and processing, providing results into a dedicated web service tailored to users' needs for knowledge and information extraction.

【文献 54】
作者: Hagmanns, R
来源: 2024 IEEE INTERNATIONAL SYMPOSIUM ON SAFETY SECURITY RESCUE ROBOTICS,
题目: Path Planning for the Autonomous Inspection of Voxelized Structures
摘要: We present an infrastructure and model inspection planning approach for known structures in unknown environments. The utilized voxel-based model representations increase the input model flexibility and allow to include environment maps of the surroundings in the planning problem. By simultaneously optimizing both coverage and path length using a GTSP representation, we are able to outperform other state-of-the-art inspection planning routines in both runtime and path length while achieving equivalent coverage.

【文献 55】
作者: Drogalis, C
来源: PROCEEDINGS OF ASME 2023 INTERNATIONAL MECHANICAL ENGINEERING CONGRESS
题目: FOOD QUALITY INSPECTION AND SORTING USING MACHINE VISION, MACHINE
摘要: The integration of Industry 4.0 technologies, such as machine vision, machine learning, and robotics, has transformed the food industry by enabling more efficient, accurate, and productive food quality inspection. This scope of this work is to implement these technologies in the inspection of chocolate chip cookies in a lab based setting. The PC-based system includes a conveyor belt, webcam, low-cost robotic arm, grayscale sensors, and MATLAB and Arduino microcontroller for communication between the vision system and the robot. Machine vision captures images of cookies and processes these images for feature extraction, while machine learning algorithms classify cookies based on their visual features and identify defects. The use of Artificial Neural Networks for training and testing results in an overall accuracy of 95% and 90%, respectively. The sorting of cookies based on the machine learning classification is carried out using a robotic arm. The robotic arm receives signals from the ML algorithm to remove defective cookies from the conveyor into a rejected cookies bin. The closed-loop system effectively (98%) inspects food quality, reducing defective food from reaching consumers. Machine vision and machine learning techniques offer a promising approach to improving quality control in the food industry.

【文献 56】
作者: Jang, I
来源: IEEE ROBOTICS & AUTOMATION MAGAZINE
题目: Towards Fully Integrated Autonomous Excavation: Autonomous Excavator for
摘要: Autonomous excavator systems (AESs) can alleviate the issues caused by the shortage of skilled labor forces and increasing labor costs. For autonomous excavation, real-time landscape estimation, excavation path generation, control, and precise landscape inspection are all essential. In this article, we propose and experimentally validate an integrated AES that incorporates all these elements. Specifically, unlike previous research, we introduce a sensor arrangement capable of sufficiently covering the regions of interest (ROIs) regardless of the inclination of the target landscape, a motion planning method that satisfies geometric and physical constraints, and a precise postexcavation inspection module using onboard sensors only. The proposed methodology was experimentally validated using a real 30-ton hydraulic excavator. It successfully performed a cutting task on an upward slope with 45 degrees inclination and achieved a centimeter-level accuracy through autonomous repetitive excavation; also, the proposed postexcavation inspection method demonstrated subcentimeter precision within seconds using onboard sensors only.

【文献 57】
作者: Lin, TH
来源: AUTOMATION IN CONSTRUCTION
题目: High-mobility inchworm climbing robot for steel bridge inspection
摘要: The High Mobility Inchworm Climbing Robot (HMICRobot), capable of traversing diaphragms between sections and performing internal inspections of steel box girder bridges, was developed through our mechanical design combined with finite element simulations and adhesion force experiments. Compared to existing robots in the literature, the developed HMICRobot exhibits superior climbing and obstacle-crossing capabilities, benefiting from its hybrid power design, unique footpad electromagnetic control, and central core module with large-size wheels that provide stable and efficient mobility on both steel surfaces and the ground. The robot's exceptional locomotion capabilities, including vertical and horizontal climbing, 360-degree flipping, and obstacle crossing, make it a promising solution for complex inspection and maintenance tasks in steel box girders. In the field of inspection robots, the HMICRobot represents a significant advancement, especially for performing internal in-spections of steel box girder bridges.

【文献 58】
作者: Tao, GH
来源: 2024 IEEE INTERNATIONAL CONFERENCE ON MECHATRONICS AND AUTOMATION, ICMA
题目: Error Calibration Analysis of Long Cantilever Inlet Inspection Robot
摘要: The long cantilever tandem articulated robot has great application prospects in complex curved surface working environments such as aircraft inlet inspection due to its advantages of flexibility and strong obstacle avoidance ability, but it is easy to cause the end position error accumulation. To increase the robot's precision and decrease end inaccuracy, the robot needs to be calibrated. In this paper, the kinematics calibration method and the neural network calibration method are used to calibrate the long cantilever series articulated robot based on the rotary platform independently designed by the team. Compared with before calibration, the average position errors are reduced to 3.684 mm and 0.304 mm, respectively. It shows that the neural network calibration method has good applicability for this kind of robot.

【文献 59】
作者: Saha, A
来源: 2023 IEEE AEROSPACE CONFERENCE
题目: An Autonomous Aircraft Inspection System using Collaborative Unmanned
摘要: The maturity in autonomous navigation of Unmanned Aerial Vehicles (UAVs) provides the possibility to deploy UAVs for different kinds of inspection jobs. Aircraft inspection is a well-known periodic process in aviation history, which is long, costly, and subjective. Manual inspection usually takes several hours, where multiple sensors on the aircraft's outer surface, dents, lightning strikes, paint, etc. are mainly checked. The main advantage of a UAV-based inspection is to minimize the turnaround time, which reduces the cost. Deployment of multiple collaborative UAVs is a must because most of the off-the-shelf UAVs do not have endurance for more than 30 minutes. There exist multiple challenges, e.g., safety of the multi-million dollar aircraft while multiple UAVs navigate, accurate identification of defects in the millimeter range, accurate localization of the defect on the aircraft body surface, etc. Moreover, the solution should be independent of the aircraft model and scalable for any model with minimal effort. To this end, we present a visual inspection system for aircraft using collaborative autonomous UAVs that is auto-adaptable to any aircraft model with minimal manual intervention. The system allows each UAV to start from any random location in the vicinity of the aircraft and uses a novel registration algorithm to collaborate between them and navigate using LiDAR-Inertial measurements. The navigation algorithm creates multilayered zones for safe navigation and to avoid obstacles. The system uses a low-cost RGB-D camera for inspection and detects defects. To the best of our knowledge, there is no open data for such a task, and given the limitations in creating it with a real aircraft, we evaluate our proposed system in a Gazebo simulation with an anonymous aircraft model. The proposed approach is able to complete the inspection task within 10 minutes using two UAVs.

【文献 60】
作者: Xing, JX
来源: 2023 IEEE/RSJ INTERNATIONAL CONFERENCE ON INTELLIGENT ROBOTS AND
题目: Autonomous Power Line Inspection with Drones via Perception-Aware MPC
摘要: Drones have the potential to revolutionize power line inspection by increasing productivity, reducing inspection time, improving data quality, and eliminating the risks for human operators. Current state-of-the-art systems for power line inspection have two shortcomings: (i) control is decoupled from perception and needs accurate information about the location of the power lines and masts; (ii) obstacle avoidance is decoupled from the power line tracking, which results in poor tracking in the vicinity of the power masts, and, consequently, in decreased data quality for visual inspection. In this work, we propose a model predictive controller (MPC) that overcomes these limitations by tightly coupling perception and action. Our controller generates commands that maximize the visibility of the power lines while, at the same time, safely avoiding the power masts. For power line detection, we propose a lightweight learning-based detector that is trained only on synthetic data and is able to transfer zero-shot to real-world power line images. We validate our system in simulation and real-world experiments on a mock-up power line infrastructure. We release our code and datasets to the public.

【文献 61】
作者: Liu, JX
来源: APPLIED ARTIFICIAL INTELLIGENCE
题目: Research on Path Optimization Method for Warehouse Inspection Robot
摘要: With the increasing popularity of intelligence, many enterprises' warehouse inspection work is completed through robots. However, due to the multiple target points of warehouse inspection, the low efficiency of planning intelligent robot inspection paths is a problem that needs to be solved. In order to solve the above problems, this paper proposes an HPSO-ACO algorithm based on hybrid particle swarm optimization (HPSO) to optimize the parameters of the ant colony optimization (ACO) algorithm, and establishes a path optimization model for intelligent inspection robots in warehouse management. Compared with HPSO algorithm and ACO algorithm, the experimental results show that the proposed method has faster convergence speed, fewer iterations, and shorter optimal path under the same conditions, which provides a theoretical reference for path optimization for inspection robot.

【文献 62】
作者: Lin, TH
来源: AUTOMATION IN CONSTRUCTION
题目: Multispecies hybrid bioinspired climbing robot for wall tile inspection
摘要: This study introduces GLEWBOT (Gecko, Leech, and Woodpecker-inspired Robot), a cutting-edge, multispecies hybrid bioinspired robot engineered for inspecting exterior wall tiles. By integrating the adaptive locomotion techniques of leeches, the stability mechanisms found in gecko tails, and the percussive impact strategy of woodpeckers, GLEWBOT revolutionizes the inspection of wall surfaces. This innovative approach not only boosts inspection accuracy but also increases efficiency substantially. Constructed using 3D printing technologies, GLEWBOT combines lightweight design with structural integrity and integrates advanced microcontrollers for real-time, efficient inspection tasks. The robot features vacuum-based suction cup footpads for solid adhesion, minimizing detachment risks, and employs a compact machine learning model for instant on-site defect detection. This model, designed for edge computing, overcomes the hurdles of high computational demands. Additionally, GLEWBOT is equipped with an AI-powered acoustic recognition module, using a dedicated convolutional neural network (CNN) to assess tile integrity objectively. This significantly advances subjective manual inspections, offering a more reliable, objective analysis. Experimental trials involving systematic tapping across 26 tiles on a model wall validated GLEWBOT's defect detection capabilities. With an overall accuracy of 74.62%, precision of 75.4%, recall of 73.08%, and an F1-score of 74.22%, confirm GLEWBOT's potential as a reliable tool for identifying tile defects. Future work will focus on expanding GLEWBOT's versatility across different architectural materials and contexts while considering economic and regulatory factors for broader deployment. This research sets a new standard in robotic inspection for building maintenance and fosters interdisciplinary collaboration by illustrating the successful integration of bioinspired designs and artificial intelligence in structural inspections.

【文献 63】
作者: Khalid, O
来源: WIND ENERGY
题目: Cost-benefit assessment framework for robotics-driven inspection of
摘要: Operations and maintenance (O&M) of floating offshore wind farms (FOWFs) poses various challenges in terms of greater distances from the shore, harsher weather conditions, and restricted mobility options. Robotic systems have the potential to automate some parts of the O&M leading to continuous feature-rich data acquisition, operational efficiency, along with health and safety improvements. There remains a gap in assessing the techno-economic feasibility of robotics in the FOWF sector. This paper investigates the costs and benefits of incorporating robotics into the O&M of a FOWF. A bottom-up cost model is used to estimate the costs for a proposed multi-robot platform (MRP). The MRP houses unmanned aerial vehicle (UAV) and remotely operated vehicle (ROV) to conduct the inspection of specific FOWF components. Emphasis is laid on the most conducive O&M activities for robotization and the associated technical and cost aspects. The simulation is conducted in Windfarm Operations and Maintenance cost-Benefit Analysis Tool (WOMBAT), where the metrics of incurred operational expenditure (OPEX) and the inspection time are calculated and compared with those of a baseline case consisting of crew transfer vessels, rope-access technicians, and divers. Results show that the MRP can reduce the inspection time incurred, but this reduction has dependency on the efficacy of the robotic system and the associated parameterization e.g., cost elements and the inspection rates. Conversely, the increased MRP day rate results in a higher annualized OPEX. Residual risk is calculated to assess the net benefit of incorporating the MRP. Furthermore, sensitivity analysis is conducted to find the key parameters influencing the OPEX and the inspection time variation. A key output of this work is a robust and realistic framework which can be used for the cost-benefit assessment of future MRP systems for specific FOWF activities.

【文献 64】
作者: Liu, ZL
来源: APPLIED SCIENCES-BASEL
题目: Virtual Simulation and Experiment of Quality Inspection Robot
摘要: (1) Background: Quality inspection robots are widely used in automated production lines. However, the design cycle is long, iteration costs are high, and algorithm development is challenging. It is difficult to perform effective validation during the design phase. Applying virtual reality technology to simulate quality inspection robot workstations offers a new approach to addressing the issues. (2) Methods: The research creates a simulation platform for quality inspection robot workstations based on a virtual reality architecture. The platform creates an immersive quality inspection robot workstation operation interface and conducts testing of the inspection process, thereby validating the rationality of the quality inspection robot workstation design. Building upon this foundation, we conducted experimental comparisons of various defect detection algorithms. (3) Results: Compared to the traditional YOLOv7 algorithm, the improved YOLOv7 algorithm achieved an 18.1% increase in recognition precision. Experimental results demonstrate that the quality inspection robot workstation simulation platform can be applied to validating workstation design proposals. (4) Conclusions: It has a positive impact on reducing the research and development costs of quality inspection robot workstations and shortening the defect recognition algorithm development cycle.

【文献 65】
作者: Zhao, Y
来源: AIP ADVANCES
题目: An image enhancement method for cable tunnel inspection robot
摘要: Influenced by multiple factors such as low light intensity, dispersed light, excessive dust, and excessive ambient noise inside cable tunnels, the images captured by cable tunnel inspection robots have shortcomings such as low contrast, low pixel values, and high noise. To improve the image quality, this paper proposes an image enhancement method suitable for cable tunnel inspection robot. First, in this paper, a bivariate hybrid optimization module using the alternating direction multiplier method and the adaptive learning rate acceleration SVRG algorithm is constructed to achieve image pre-processing. Second, a feature extraction module combining a U-Net network and a coordinate attention mechanism is constructed to extract features from the original image and the pre-processed image. Third, the progressive feature fusion module and the image recovery module are constructed to fuse the above features and are combined with the original image to obtain the enhanced image. Finally, the pixel compensation module is constructed to compensate for the features of the enhanced image and recover the lost texture details.

【文献 66】
作者: Calamoneri, T
来源: COMPUTERS & OPERATIONS RESEARCH
题目: Autonomous data detection and inspection with a fleet of UAVs
摘要: Consider an area of interest A , where a set of n sites lie. Two kinds of information can be captured from each site: light and heavy information. A fleet of m homogeneous UAVs, each one equipped with a battery B , is available at a common depot, where the flight mission of each UAV starts and finishes. The problem we consider focuses on a single flight of the fleet of UAVs and aims at collecting their light information from all sites (that can be retrieved, not necessarily passing over each site, but simply "close"to it). At the same time, the fleet will have to select a limited number of sites from which to collect their heavy information. Flying among sites and acquiring information from them (both light and heavy) has a battery cost. On the other hand, a profit is associated with the action of acquiring heavy information from a site. We refer to the extraction of light and heavy information from a site as to weakly or strongly cover the site. The aim of the problem consists of retrieving light information from all sites while maximizing the overall profit, keeping the battery consumption of each UAV within B . In this paper, we model this real -life situation as a new combinatorial optimization problem that we call m3DIP, for which we provide a mixed integer programming model. Given the high degree of complexity of the problem, in this way we are not able to provide a solution in a reasonable time. To address larger instances we propose a matheuristic in which we exploit a path -based algorithm filled with only a subset of feasible cycles (paths) provided by different heuristics. The output indicates which path to select and the set of nodes to be strongly and weakly covered by each trip. We compare our matheuristic with the results obtained by every single heuristic on a large set of instances, showing that the matheuristic strongly outperforms them. An interesting insight is that even paths provided by a heuristic with very bad performances can be useful if combined with paths provided by other heuristics and if the coverage decisions are reoptimized by the matheuristic. We also show the benefit of adding fictitious additional points that UAVs can visit to weakly cover a subset of sites, without actually visiting none of them.

【文献 67】
作者: Navarro, D
来源: 32ND EUROPEAN SIGNAL PROCESSING CONFERENCE, EUSIPCO 2024
题目: Towards autonomous robotic structure inspection with dense-direct
摘要: We present a comprehensive framework based on direct Visual Simultaneous Localization and Mapping (V-SLAM) to observe a vertical coastal cliff. The precise positioning of data measurements (such as ground-penetrating radar) is crucial for environmental observations. However, in GPS-denied environments near large structures, the GPS signal can be severely disrupted or even unavailable. To address this challenge, we focus on the accurate localization of drones using vision sensors and SLAM systems. Traditional SLAM approaches may lack robustness and precision, particularly when cameras lose perspective near structures.

【文献 68】
作者: Dalmedico, N
来源: ROBOTICS AND AUTONOMOUS SYSTEMS
题目: Climbing robot for advanced high-temperature weld bead inspection
摘要: High-temperature industrial inspection has several challenges, especially if it is an autonomous inspection through mobile robots. This paper introduces the mobile robot CRAS (Climbing Robot for Advanced inSpection) for autonomous non-destructive testing (NDT) of weld beads from industrial super-duplex stainless steel vessels. It covers the design process, previous works, main challenges, and field testing. The main objective of the robot is to perform ultrasonic inspection over a heated separator tank while it operates. The metallic surfaces of the structure to be inspected are under constant high temperatures (80 degrees C-135 degrees C) when in operation. CRAS presents magnetic wheels as an adhesion method and a perception system able to identify and follow weld beads. The NDT method uses the phased-array ultrasonic technique. This paper approaches and proposes a solution for three challenges due to the high temperature: the loss of robot adhesion, ultrasound signal deformation, and the risk of damaging sensitive equipment such as sensors, cameras, and any electronic component. The CRAS adopted solutions are detailed and future steps of CRAS development are also addressed.

【文献 69】
作者: Wang, YM
来源: INDUSTRIAL ROBOT-THE INTERNATIONAL JOURNAL OF ROBOTICS RESEARCH AND
题目: Mechanism design and mechanical analysis of pipeline inspection robot
摘要: PurposeThis study aims to address the issues of limited pipe diameter adaptability and low inspection efficiency of current pipeline inspection robots, a new type of pipeline inspection robot capable of adapting to various pipe diameters was designed.Design/methodology/approachThe diameter-changing mechanism uses a multilink elastic telescopic structure consisting of telescopic rods, connecting rods and wheel frames, driven by a single motor with a helical drive scheme. A geometric model of the position relationships of the hinge points was established based on the two extreme positions of the diameter-changing mechanism.FindingsA pipeline inspection robot was designed using a simple linkage agency, which significantly reduced the weight of the robot and enhanced its adaptive pipe diameter ability. The analysis determined that the robot could accommodate pipe diameters ranging from 332 mm to 438 mm. A static equilibrium equation was established for the robot in the hovering state, and the minimum pressing force of the wheels against the pipe wall was determined to be 36.68 N. After experimental testing, the robots could successfully pass a height of 15 mm, demonstrating the good obstacle capacity of the robot.Practical implicationsThis paper explores and proposes a new type of multilink elastic telescopic variable diameter pipeline inspection robot, which has the characteristics of strong adaptability and flexible operation, which makes it more competitive in the field of pipeline inspection robots and has great potential market value.Originality/valueThe robot is characterized by the innovative design of a multilink elastic telescopic structure and the use of a single motor to drive the wheel for spiral motion. On the basis of reducing the weight of the robot, it has good pipeline adaptability, climbing ability and obstacle-crossing ability.

【文献 70】
作者: Scognamiglio, V
来源: 2024 INTERNATIONAL CONFERENCE ON UNMANNED AIRCRAFT SYSTEMS, ICUAS
题目: Autonomous Visual Inspection of Industrial Plants Using Unmanned Aerial
摘要: The development of autonomous systems has spurred numerous innovative inspection strategies. Some operations, such as monitoring the condition of industrial structures, typically entail significant deployment of human resources and pose risks to human safety. In this context, this paper presents a visual inspection framework that leverages unmanned aerial vehicles to explore designated facilities, identifying structural damages such as cracks or fissures for inspection. The proposed approach integrates autonomous navigation and high-level decision-making capabilities to effectively explore predefined points of interest within partially known environments and to select and inspect candidate spots for further analysis. The framework is validated through both simulated and real-world experiments conducted in GPS-denied environments, utilizing only onboard UAV capabilities.

【文献 71】
作者: Soares, SB
来源: 2023 IEEE 21ST INTERNATIONAL CONFERENCE ON INDUSTRIAL INFORMATICS, INDIN
题目: An Autonomous Inspection Method for Pitting Detection Using Deep
摘要: The corrosion inspection process in ship tanks used by the oil industry for the production, storage, and disposal of oil, which is known as Floating Production Storage and Offloading (FPSO), is predominantly manual. It requires a long production downtime, and is an unhealthy job for inspectors. In the literature, some works proposed methods for corrosion segmentation. However, none of them classifies the level of corrosion in accordance with the International Association of Classification Societies (IACS) standard. This work proposes the use of U-Net-based network for segmentation of pitting corrosion, and also provides a corrosion level analysis algorithm relating the identified pitting to the IACS standard. Furthermore, data augmentation methods are adopted to make the dataset more diversified, aiming to generalize the neural network learning. The results indicate a mean squared error of only 0.1639 using the proposed method, and an intersection-of-union of 0.9453. In addition, we compared our method with classical methods such as Canny, Laplacian, Otsu, and Sobel methods, where a relevant advantage is obtained with U-Net.

【文献 72】
作者: Hinostroza, MA
来源: 2024 20TH IEEE/ASME INTERNATIONAL CONFERENCE ON MECHATRONIC AND EMBEDDED
题目: Autonomous Inspection and Maintenance Operations employing Multi-Robots:
摘要: Deploying multi-robot systems for Inspection and Maintenance (IM) operations offers several advantages, including covering a large mission area and providing redundancy against individual failure. These features are particularly relevant for IM operations in offshore oil and gas platforms due to the extensive number of sensors and equipment involved, as well as the high level of fault-tolerant resilience required. This paper presents preliminary experimental results of a multi-robot mission planning system designed for performing autonomous inspection at Equinor's K-Lab Test Centre in Haugesund, Norway. The high-level action planner is based on the Simultaneous Task Planning (STP) algorithm. The STP planner has the capability to compute plans for multi-robot systems, as it allows for concurrent actions. The system can take into account high-level actions and their expected durations, such as "visiting a specific location", "inspecting a sensor", or "taking a picture of a specified component". Additionally, STP can replan in real-time in case of events such as the need to revisit a waypoint or low battery status. Communication with the robots is achieved through Equinor's Integration and Supervisory Control of Autonomous Robots (ISAR) framework, ensuring a fast and secure Wi-Fi connection. The collision avoidance, guidance, and control system is handled at the low-level layer of each robot. The proposed technique was validated through field experiments involving two unmanned ground robots performing an inspection round, which includes visiting a sequence of predefined waypoints and incorporates replanning for low battery situations. Two types of experiments were conducted: one scenario where robots collaborated by overlapping capabilities; and another scenario where robots collaborated by combining capabilities.

【文献 73】
作者: Rakoczy, AM
来源: STRUCTURAL ENGINEERING INTERNATIONAL
题目: Technologies and Platforms for Remote and Autonomous Bridge Inspection -
摘要: Recent scientific and technological advancements have enabled a more efficient structural condition assessment of bridges, mainly through the implementation of intelligent inspection strategies. These intelligent strategies can provide early identification of critically damaged components before failure, and will therefore play a key role in extending the life of infrastructure. The latest inspection technologies can provide inspection plans with damage conditions, create a damage report as well as provide statistics and comparisons to the previous inspection findings. The new and existing inspection technologies are directed to help with the digitalization of the Bridge Management System (BMS). The complexity of maintenance/inspection requires organized, automated, open and transparent digital processes, which should consider both-structure and asset management data. Further, the inspection/monitoring findings serve as a source for decision-making models. The digitalized aspects of autonomous inspection provide better performance prediction models and guarantee safety for the users. This paper presents the latest findings in the field of remote inspection of bridges. In particular, the main technologies for inspection and geometrical assessment are depicted, especially those based on computer vision systems installed in UAVs and robots, LiDAR, radar, satellites and other non-contact systems including on-board monitoring. The accompanying article entitled: "Methodologies for remote bridge inspection" deals with the methodologies used for data processing based on Artificial Intelligence (AI).

【文献 74】
作者: Türk, N
来源: INTELLIGENT AUTONOMOUS SYSTEMS 18, VOL 1, IAS18-2023
题目: Autonomous Model-Based Inspection Planning for 3D Visual Coverage Tasks
摘要: The visual inspection planning of 3D objects consists of the problem of finding suitable viewpoints. While complete visual coverage of the object surface is required, not too many viewpoints should be needed. In this paper a view-planning algorithm for a camera-equipped Mobile Industrial Robot Arm (MIRA) is proposed. The MIRA is mounted on a mobile platform, which can move freely on the ground around the object being inspected. The proposed model-based approach finds the required platform positions and the corresponding viewpoints to obtain a full visual coverage. Random sampling and a potential-field method are used to generate viewpoint and platform position candidates. The viewpoint and platform position planning both are formulated as a Set Covering Problem (SCP) afterwards. The effectiveness of selected algorithms to solve the SCP for this application is compared through a number of computational tests. The overall applicability of the proposed approach is demonstrated in a simulated environment.

【文献 75】
作者: Zhu, MJ
来源: IEEE ACCESS
题目: Target Recognition of Multi Source Machine Vision Pan Tilt Integrated
摘要: With the continuous changes in socio-economic needs, traditional methods of power facility inspection can no longer meet practical needs due to their low efficiency and lack of scalability. In response to this challenge, this study delves into the integrated motion control technology of inspection robots equipped with gimbal mechanisms, aiming to improve the convenience and efficiency of dynamic data collection. A customized multi-source heterogeneous visual detection and recognition model based on the YOLOv3 framework has been proposed, and simultaneously using path aggregation networks to enhance information processing capacity by fusing multi-scale features. Experimental analysis shows that as the robot's movement speed increases, the error rate correspondingly increases, indicating the direction of optimization. In the target recognition experiment, the proposed model achieved an average accuracy of 94.26% in visible light images and 68.05% in infrared images. In addition, Sub_ The YOLO algorithm demonstrates a fast detection speed of 30 frames per second, with an average accuracy of over 80%, marking an important progress in real-time object detection applications. In the linear motion test, the relative error of the robot's motion accuracy was 0.33% at a speed of 500 millimeters per second. However, when the speed was increased to 1200 millimeters per second, the error increased to 2.45%, indicating a significant increase in slip. This indicates that the linear motion accuracy of the robot is acceptable at low to medium speeds, but the accuracy decreases significantly at high speeds. Overall, the research results confirm the synergistic effect of integrated motion control between inspection robots and gimbals, as well as Sub_ The superiority of YOLO in target recognition has improved the ability to use wheeled robots for electrical inspections, bringing substantial technological progress to the field of autonomous inspection.

【文献 76】
作者: You, YQ
来源: ACTUATORS
题目: Development of a Small-Sized Urban Cable Conduit Inspection Robot
摘要: Cable conduits are crucial for urban power transmission and distribution systems. However, current conduit robots are often large and susceptible to tilting issues, which hampers the effective and intelligent inspection of these conduits. Therefore, there is an urgent need to develop a smaller-sized conduit inspection robot to address these challenges. Based on an in-depth analysis of the characteristics of the cable conduit working environment and the associated functional requirements, this study successfully developed a small-scale urban cable conduit inspection robot prototype. This development was grounded in relevant design theories, simulation analyses, and experimental tests. The test results demonstrate that the robot's bracing module effectively prevents tilting within the conduit. Additionally, the detection module enables comprehensive 360-degree conduit inspections, and the vacuuming module meets the negative pressure requirements for efficient absorption of dust and foreign matter. The robot has met the expected design goals, effectively enhanced the automation of the cable conduit construction process, and improved the quality control of cable laying.

【文献 77】
作者: Tarapongnivat, K
来源: SYNERGETIC COOPERATION BETWEEN ROBOTS AND HUMANS, VOL 1, CLAWAR 2023
题目: Hybrid Omnidirectional Wheeled Climbing Robot with an Electromagnet for
摘要: The article presents a mobile robot for climbing on vertical ferromagnetic surfaces. The robot was designed with hybrid omnidirectional rubber/silicone roller wheels for proper asymmetric friction force generation and a controllable electromagnet for ferromagnetic surface adhesion. The friction force of rubber and silicone rollers and the adhesion force of the electromagnet were investigated. An embedded electronic system with a mobile processor was installed on the robot to allow for remote control. Our results show versatile robot climbing on a ferromagnetic wall with omnidirectional motion and a lower adhesion force-to-weight ratio as compared to that of the others.

【文献 78】
作者: Yin, SY
来源: IEEE SENSORS JOURNAL
题目: sEMG and NCLA-Based Gesture Recognition for Sewer Inspection Robot
摘要: In the domain of human-computer interaction (HCI), the recognition of emergency gestures based on surface electromyography (sEMG) signals is critical for minimizing the risk of inaccurate control in sewer inspection robots. This study is dedicated to establish a mapping relationship between forearm multichannel sEMG signals and emergency gestures, leading to the creation of the rainwater and sewage management gesture dataset (RSMGD) alongside a corresponding gesture recognition methodology. A comprehensive evaluation encompassing classification accuracy, CPU running time, and required feature dimensionality is conducted for this gesture recognition. Initially, RSMGD is collected utilizing Noraxon equipment, and an effective wavelet transform technique is applied to extract 2-D feature maps from the signals. To overcome the limitations of most traditional feature selection algorithms, which rely on a single fitness evaluation and involve high-dimensional features, this study introduces a novel feature optimization algorithm-the Nifty crow learning algorithm (NCLA). Inspired by the Levy flight random walk model, originally used to simulate the stochastic movement and long-distance migratory behaviors of birds, the algorithm incorporates an innovative mutation strategy through crow following behavior and a memory updating mechanism, combined with a multitiered fitness evaluation mechanism, achieving more optimal feature selection. The results indicate that NCLA, using only 16 features (p < 0.05), achieves a classification accuracy of 99.04% in just 0.201 s (p < 0.05), with the true positive rate (TPR) and false positive rate (FPR) reaching 99.09% and 1.81%, respectively, demonstrating its exceptional performance in rapid and accurate gesture recognition.

【文献 79】
作者: Zhang, K
来源: NUCLEAR ENGINEERING AND TECHNOLOGY
题目: Dimensional synthesis of an Inspection Robot for SG tube-sheet
摘要: To ensure the operational safety of nuclear power plants, we present a Quadruped Inspection Robot that can be used for many types of steam generators. Since the Inspection Robot relies on the Holding Modules to grip the tube-sheet, it can be regarded as a hybrid robot with variable configurations, switching between 4-RRR-RR, 3RRR-RR, and two types of 2-RRR-RR, and the variable configurations bring a great challenge to dimensional synthesis. In this paper, the kinematic model of the Inspection Robot in multiple configurations is established, and the analytical solution is given. The workspace mapping is analyzed by the solution-space, and the workspace of multiple configurations is decomposed into the workspace of 2-RRR to reduce the analysis complexity, and the workspace calculation is simplified by using the envelope rings. The optimization problem of the manipulator is transformed into the calculation of the shortest contraction length of the swing leg. The switching performance of the Inspection Robot is evaluated by stride-length, turning-angle, and workspace overlap-ratio. The performance indexes are classified and transformed based on the proportions and variation trends of dimensional parameters to reduce the number of optimization objective functions, and Pareto optimal solutions are obtained using an intelligent optimization algorithm.

【文献 80】
作者: Rivera, A
来源: PRESENCE-VIRTUAL AND AUGMENTED REALITY
题目: Collaborative Robot Teleoperation in Mixed Reality Environment for
摘要: Collaborative robots are increasing their presence in the industrial production context due to their flexibility in the tasks they can perform. However, programming and teleoperating them could be a difficult task from the perspective of an unqualified worker. In cases where the worker has to collaborate remotely with a robotic arm, the probability of collisions between the robotic arm and the environment increase. In this work, we describe the design and implementation of a mixed-reality multimodal interface for a worker to teleoperate a robotic arm by interacting with its holographic digital twin, while remaining aware of surrounding obstacles to avoid any collisions. To teleoperate the robot, we have developed a multimodal interface with which the user can communicate naturally with voice (through a semantics-based task-oriented dialogue system), and by performing manual actions on holographic objects. The application also provides audiovisual information to the user about the status of the collaborative robot as well as the actions commanded. We compute and command the behavior of the robotic arm with ROS (Robot Operating System) and "MoveIt!" (motion planning framework), and we represent computed trajectories and the real-time pose of the robot in the mixed reality environment.

【文献 81】
作者: Papavasileiou, A
来源: APPLIED SCIENCES-BASEL
题目: A Voice-Enabled ROS2 Framework for Human-Robot Collaborative Inspection
摘要: Quality inspection plays a vital role in current manufacturing practice since the need for reliable and customized products is high on the agenda of most industries. Under this scope, solutions enhancing human-robot collaboration such as voice-based interaction are at the forefront of efforts by modern industries towards embracing the latest digitalization trends. Current inspection activities are often based on the manual expertise of operators, which has been proven to be time-consuming. This paper presents a voice-enabled ROS2 framework towards enhancing the collaboration of robots and operators under quality inspection activities. A robust ROS2-based architecture is adopted towards supporting the orchestration of the process execution flow. Furthermore, a speech recognition application and a quality inspection solution are deployed and integrated to the overall system, showcasing its effectiveness under a case study deriving from the automotive industry. The benefits of this voice-enabled ROS2 framework are discussed and proposed as an alternative way of inspecting parts under human-robot collaborative environments. To measure the added value of the framework, a multi-round testing process took place with different parameters for the framework's modules, showcasing reduced cycle time for quality inspection processes, robust HRI using voice-based techniques and accurate inspection.

【文献 82】
作者: Wang, JL
来源: IEEE ROBOTICS AND AUTOMATION LETTERS
题目: Transformable Inspection Robot Design and Implementation for Complex
摘要: Pipeline inspections are crucial to ensure the reliability of the transmission system. However, with the growing complexity and aging of the pipe system, traditional pipeline inspection robots struggle to adapt to complex environments with obstacles, cracks, changing cross-section, and other challenges. This letter introduces a novel transformable inspection robot with remarkable adaptability to varying pipeline environment from 163 mm to 312 mm inner diameter. The robot is composed of several motion modules that are arranged along its central axes at a 60-degree angle. The pneumatically powered robot has good active and passive deformation capabilities, enabling it to passively adapt to its surroundings and actively change between different postures. Our robot can also achieve automatic navigation in complex pipeline environments based on a LiDAR camera. Experiments demonstrate the robot adjusting to varying pipeline scenarios, including obstacles, diameter changes, turning up to 90 degrees, climbing up to 45 degrees, and crossing-section changes with a deformation rate up to 191.4$ %, overcoming the limitations of traditional designs.

【文献 83】
作者: Liu, HY
来源: APPLIED ARTIFICIAL INTELLIGENCE
题目: Research on Automatic Path Planning Method of Warehouse Inspection Robot
摘要: The patrol robot is an important guarantee for the safety of the enterprise's warehouse. However, due to the large number of patrol target points, the automatic path planning of the patrol robot is difficult and inefficient. In order to solve this problem, the hybrid particle swarm optimization (HPSO) algorithm is combined with A-star algorithm, and a path optimization method for inspection robot based on HPSO-A-star model is proposed. First, the grid model of the map is established, A-star algorithm calculates the path between two inspection points, and then HPSO algorithm realizes the nonlinear planning of the path according to the length of different paths, so as to find an optimal inspection route. The comparative experimental analysis results show that the path planned by HPSO algorithm is 5.71% shorter than that planned by PSO algorithm; the smaller the map grid size is, the shorter the calculated optimal path length is, but it will consume more computing resources. Finally, the HPSO-A-star algorithm is compared with the PSO-A-star algorithm; the experimental results show that the path of the HPSO-A-star algorithm is shortened by 29.79%, and the HPSO-A-star algorithm can better realize the path planning of the patrol robot.

【文献 84】
作者: Kyuroson, A
来源: IFAC PAPERSONLINE
题目: Autonomous Point Cloud Segmentation for Power Lines Inspection in Smart
摘要: LiDAR is currently one of the most utilized sensors to effectively monitor the status of power lines and facilitate the inspection of remote power distribution networks and related infrastructures. To ensure the safe operation of the smart grid, various remote data acquisition strategies, such as Airborne Laser Scanning (ALS), Mobile Laser Scanning (MLS), and Terrestrial Laser Scanning (TSL) have been leveraged to allow continuous monitoring of regional power networks, which are typically surrounded by dense vegetation. In this article, an unsupervised Machine Learning (ML) framework is proposed, to detect, extract and analyze the characteristics of power lines of both high and low voltage, as well as the surrounding vegetation in a Power Line Corridor (PLC) solely from LiDAR data. Initially, the proposed approach eliminates the ground points from higher elevation points based on statistical analysis that applies density criteria and histogram thresholding. After denoising and transforming of the remaining candidate points by applying Principle Component Analysis (PCA) and Kd-tree, power line segmentation is achieved by utilizing a two-stage DBSCAN clustering to identify each power line individually. Finally, all high elevation points in the PLC are identified based on their distance to the newly segmented power lines. Conducted experiments illustrate that the proposed framework is an agnostic method that can efficiently detect the power lines and perform PLC-based hazard analysis. Copyright (c) 2023 The Authors.

【文献 85】
作者: Du, RC
来源: 8TH INTERNATIONAL CONFERENCE ON BIG DATA AND INTERNET OF THINGS, BDIOT
题目: Tracked Inspection Robot: Teleoperation based on ROS and Unity 3D
摘要: Deficiencies in depth and perspective perception often hinder teleoperation based on video observation, especially in monocular settings. The integration of virtual reality (VR) allows the operator to be more immersed in the working environment, which can optimize the operation process and reduce the difficulty of operation. Taking cable trench inspection as an application scenario, this paper proposed a teleoperation system based on robot operating system (ROS) and Unity. The proposed method achieved the synchronization of both virtual and real robot actions, enabling the transmission and visualization of multimodal sensor data, and eventually allowing operators to utilize the virtual interface for immersive robot operation. The results of the user survey verified the feasibility of using the tracked robot based on ROS to achieve teleoperation combined with VR, and the introduction of VR reduces the cognitive load.

【文献 86】
作者: Li, JW
来源: IEEE ROBOTICS AND AUTOMATION LETTERS
题目: Localized Coverage Planning for a Heat Transfer Tube Inspection Robot
摘要: The heat transfer tubes of the steam generator are critical components of the nuclear power system and require regular inspection to ensure safety. The SG-Climbot, a quadruped heat transfer tube inspection robot, is equipped with a guiding device capable of simultaneously aligning with and inspecting two heat transfer tubes. Furthermore, The guiding device must execute hundreds of pose configuration transformations to complete a localized coverage inspection, thereby presenting challenges to the robot's efficient autonomous planning. This letter presents a planning framework for the SG-Climbot's localized coverage inspection task. The framework consists of four planning levels: pair planning, position and orientation planning for the guiding device, inspection sequence planning, and time-optimal trajectory planning. A maximum matching algorithm suitable for robotic arms equipped with dual execution devices to perform tasks has been proposed, achieving the optimal pairing of heat transfer tubes and reducing inspection time by over 48 minutes (18.32% improvement). In addition, we analyze the impact of various Traveling Salesman Problem (TSP) solving algorithms on sequence planning issues that require reaching numerous nodes within short operation times, reducing the arm operating time by 33.20 s (6.99% improvement). Finally, the effectiveness of the proposed planning algorithm was validated through simulations and experiments.

【文献 87】
作者: Cardaillac, A
来源: JOURNAL OF INTELLIGENT & ROBOTIC SYSTEMS
题目: ROV-Based Autonomous Maneuvering for Ship Hull Inspection with Coverage
摘要: Hull inspection is an important task to ensure sustainability of ships. To overcome the challenges of hull structure inspection in an underwater environment in an efficient way, an autonomous system for hull inspection has to be developed. In this paper, a new approach to underwater ship hull inspection is proposed. It aims at developing the basis for an end-to-end autonomous solution. The real-time aspect is an important part of this work, as it allows the operators and inspectors to receive feedback about the inspection as it happens. A reference mission plan is generated and adapted online based on the inspection findings. This is done through the processing of a multibeam forward looking sonar to estimate the pose of the hull relative to the drone. An inspection map is incrementally built in a novel way, incorporating uncertainty estimates to better represent the inspection state, quality, and observation confidence. The proposed methods are experimentally tested in real-time on real ships and demonstrate the applicability to quickly understand what has been done during the inspection.

【文献 88】
作者: Karagöz, Z
来源: JOURNAL OF FIELD ROBOTICS
题目: Model Reference-Based Neural Controller for Transmission Line Inspection
摘要: The regular inspection of the power transmission lines is essential for the uninterrupted transmission of electrical energy to demand points. This quickly requires actions with economically, efficiently, and safely. Therefore, the transmission line inspection robots are inevitable solution as an alternative to existing line inspection methods. This study present design and control of a transmission line inspection robot (I-Robot). Since the I-Robot exhibits nonlinear behavior and has multiple inputs and multiple outputs, a model reference-based neural controller is determined to achieve nonlinear control. The robot design process consists of four stages which are kinematic modelling, dynamic modelling, actuator modelling and controller design. To meet inspection requirements, the conceptual design of the I-Robot is performed, and the kinematic model are calculated in terms of the transformation matrices. According to the design requirements and system constraints, the dynamic model of the I-Robot is created. To provide desired motions and trajectory tracking, the actuator models are determined. Then, the I-Robot is prototyped. According to the dynamics of joint, robot and constraints, the system identification is performed to create reference model. During the system identification, the logged data are used the train the reference model. Finally, the desired trajectory for the driving cycles is created by manual excitation of the I-Robot. During the manual excitation, the logged data are used to train the neural network (NN)-based controller. Eventually, the I-Robot is assessed under the test scenarios in term of the trajectory tracking performance as regression value and mean squared errors. According to the experiments, the neuron numbers and the training algorithm of the NN controller are determined. It was observed that the controller is quickly optimized with the adapting algorithm designed for the NN reference model. As a result, the performance of the model reference-based neural controller was determined as 99%.

【文献 89】
作者: Rodriguez-Vazquez, J
来源: SENSORS
题目: Real-Time Object Detection for Autonomous Solar Farm Inspection via UAVs
摘要: Robotic missions for solar farm inspection demand agile and precise object detection strategies. This paper introduces an innovative keypoint-based object detection framework specifically designed for real-time solar farm inspections with UAVs. Moving away from conventional bounding box or segmentation methods, our technique focuses on detecting the vertices of solar panels, which provides a richer granularity than traditional approaches. Drawing inspiration from CenterNet, our architecture is optimized for embedded platforms like the NVIDIA AGX Jetson Orin, achieving close to 60 FPS at a resolution of 1024 x1376 pixels, thus outperforming the camera's operational frequency. Such a real-time capability is essential for efficient robotic operations in time-critical industrial asset inspection environments. The design of our model emphasizes reduced computational demand, positioning it as a practical solution for real-world deployment. Additionally, the integration of active learning strategies promises a considerable reduction in annotation efforts and strengthens the model's operational feasibility. In summary, our research emphasizes the advantages of keypoint-based object detection, offering a practical and effective approach for real-time solar farm inspections with UAVs.

【文献 90】
作者: Wang, Z
来源: MACHINES
题目: Development and Experiment of Clamp Type Submarine Cable Inspection
摘要: Relying on the research and development project of an auxiliary device for a submarine cable to cross a steel pipeline, with regard to a long-distance submarine cable crossing a pipeline, combined with a pipe-climbing robot and the laying of the submarine cable, this paper developed a detection robot that walks along the outer wall of the cable inside the submarine casing. The non-enclosed four-bar linkage mechanism is adopted, a stepper motor is used to drive a roller to walk on the submarine cable, the diameter change of the submarine cable is recorded in real-time, the damage of the submarine cable is detected when it is moved along the submarine cable, and the walking experiment is carried out. The submarine cable diameter measurement verification experiment showed that the outer wall detection robot of the submarine cable could stably travel on the submarine cable, and at the same time, could measure the real-time diameter of the submarine cable and record the actual condition of the submarine cable through video.

【文献 91】
作者: Halder, S
来源: CONSTRUCTION RESEARCH CONGRESS 2024: ADVANCED TECHNOLOGIES, AUTOMATION,
题目: Challenges of Human-Robot Partnership in Future Construction Inspection
摘要: This research explores the challenges of human-quadruped robot partnership in the process of construction inspection and monitoring. Quadruped robots have shown potential in construction inspection and monitoring because of their ability in traversing uneven terrains. Construction inspection and monitoring which is a crucial process in construction can benefit from the use of autonomous robots by collecting frequent as-built data for facilitating inspection by different stakeholders. However, the adoption of robotic technologies in construction is still low. To realize the full potential of quadruped robotic technologies in construction inspection and monitoring, it is important to understand fundamental challenges that can restrict its widespread adoption. Therefore, this study uses a qualitative approach to identify the challenges of the adoption of an inspector assistant quadruped robot in construction inspection and monitoring through on-site observations, interviews, and a focus group study. The results of this study identified 20 different challenges grouped into five categories, namely, economical, operational, organizational, social, and other challenges. The findings of the study can facilitate the development and implementation of quadruped robotic technology in construction inspection and monitoring.

【文献 92】
作者: Jeon, M
来源: COMPUTER-AIDED CIVIL AND INFRASTRUCTURE ENGINEERING
题目: Autonomous flight strategy of an unmanned aerial vehicle with multimodal
摘要: This study proposes an innovative method for achieving autonomous flight to inspect overhead transmission facilities. The proposed method not only integrates multimodal information from novel sensors but also addresses three essential aspects to overcome the existing limitations in autonomous flights of an unmanned aerial vehicle (UAV). First, a novel deep neural network architecture titled the rotational bounding box with a multi-level feature pyramid transformer is introduced for accurate object detection. Second, a safe autonomous method for the transmission tower approach is proposed by using multimodal information from an optical camera and 3D light detection and ranging. Third, a simple yet accurate control strategy is proposed for tracking transmission lines without necessitating gimbal control because it keeps the UAV's altitude in sync with that of the transmission lines. Systematic analyses conducted in both virtual and real-world environments confirm the effectiveness of the proposed method. The proposed method not only enhances the performance of autonomous flight but also provides a safe operating platform for inspection personnel.

【文献 93】
作者: Kötter, D
来源: 2023 5TH INTERNATIONAL CONFERENCE ON CONTROL AND ROBOTICS, ICCR
题目: Development of an Augmented Reality User Interface for Collaborative
摘要: This paper presents, implements and evaluates an augmented reality user interface for collaborative robots (cobots) in a quality inspection environment. The aim is to enable a worker to understand the movement of a cobot through an innovative projection design and to ease the teaching of new movement points into the robotic system. A virtual camera captures the position of the cobot and additional information in the RViz simulation, and a projector projects the actual and future positions of the cobot as well as visual cues and buttons on the ground of the shared workspace. Laser scanners are utilized to detect feet interacting with the visual buttons. To evaluate the effectiveness of the augmented reality user interface, a survey with people working alongside the cobot equipped with the visualization-system is conducted. The survey assesses the perceived usefulness, ease of understanding, and overall satisfaction with the user interface. The result of the survey indicates that the user interface significantly improves the comprehension of cobots actions. Participants working with this visualization system report enhanced situational awareness, reduced ambiguity, and increased efficiency. The qualitative feedback highlights the effectiveness of the visual cues in conveying information and the ease of integration into existing production processes.

【文献 94】
作者: França, M
来源: 2023 LATIN AMERICAN ROBOTICS SYMPOSIUM, LARS, 2023 BRAZILIAN SYMPOSIUM
题目: Autonomous Robotic System for Visual Inspection in Electrical
摘要: This article presents a research project focused on improving the efficiency and safety of maintenance processes in hazardous and difficult-to-access environments, such as openair electrical substations and cable galleries. A mobile robot integrates perception technologies to gather and process an inner representation of the environment allowing it to recognize known inspection targets. The developed prototype incorporates teleoperation and autonomous mission capabilities, as well as object recovery and incremental learning techniques to enhance detection accuracy. The article also details creation of both real and synthetic datasets for training and testing, as well as the evaluation of the robot's performance in object detection and tracking process. Results suggest that the developed technology can improve maintenance processes, reduce downtime and costs associated with equipment failures, and minimize occupational hazards.

【文献 95】
作者: Tian, M
来源: SOLARPACES 2022, 28TH INTERNATIONAL CONFERENCE ON CONCENTRATING SOLAR
题目: Toward Autonomous Field Inspection of CSP Collectors With a Polarimetric
摘要: We developed a polarimetric imaging drone to perform field inspections of heliostats and carried out field tests at Sandia's National Solar Thermal Test Facility (NSTTF). The preliminary results show that Degree of Linear Polarization (DOLP) and Angle of Polarization (AOP) images greatly enhanced the edge detection results compared with the conventional visible images, supporting fast and accurate detection of heliostat mirror edges and cracks. The system holds the promise to enable future automated detection of heliostats optical errors and mirror defects.

【文献 96】
作者: Gao, KD
来源: POWDER TECHNOLOGY
题目: Simulation and analysis of propulsive performance for screw propulsion
摘要: Screw propulsion mechanism is a kind of special walking mechanism suitable for traveling on the loose terrain, with the characteristics of small grounding specific pressure, strong trafficability and small turning radius. Based on this, a screw-propelled inspection robot for sandy land is designed. To evaluate the propulsive performance of the robot, The DEM-MBD coupling method was used to analyze the influence law of rotary-screw propulsor blade diameter, tooth profile Angle, lead and spiral blade number on the propulsive performance of screwpropelled inspection robot. The results show that the rotary-screw propulsor with larger blade outer diameter has stronger propulsion speed and better directivity, while its stability gradually deteriorates. The rotaryscrew propulsor with smaller tooth Angle has better propulsive ability and stability. For the rotary-screw propulsor with single helix, the driving ability becomes stronger with the increase of the lead, and the stability deteriorates exponentially. When the pitch is similar, the directivity and stability of double helix are better than that of single helix and triple helix. The research results provide basic data for the performance optimization of screw propulsion mechanism and have practical significance.

【文献 97】
作者: Amer, A
来源: 2023 21ST INTERNATIONAL CONFERENCE ON ADVANCED ROBOTICS, ICAR
题目: Visual Tracking Nonlinear Model Predictive Control Method for Autonomous
摘要: EMIRATES

【文献 98】
作者: Li, ZS
来源: IEEE ROBOTICS AND AUTOMATION LETTERS
题目: Model-Based Trajectory Planning of a Hybrid Robot for Powerline
摘要: This letter presents the first trajectory planning method for hybrid robot to perform powerline inspection involving obstacle navigation and landing. We develop a geometric model that incorporates constraints for landing the hybrid robot on a powerline, obstacle avoidance, and objectives that maximize the visibility of the powerline during flight. The trajectory generation is achieved via solving a multiple shooting nonlinear programming problem with respect to system dynamics and geometric constraints. The formulation of the problem accommodates both powerline-to-powerline and air-to-powerline trajectory planning scenarios. It runs onboard and is capable of generating trajectories within 50 ms, regardless of whether the hybrid robot's initial state is positioned on the powerline or hovering above it. Through simulation experiments, we illustrate the impact of our proposed geometric model on trajectory planning. Furthermore, real-world experimental results validate the efficacy of the proposed planning method. Compared with the existing feedback-control-based work, the landing and obstacle navigation time are significantly reduced.

【文献 99】
作者: Chien, JL
来源: 2024 SICE FESTIVAL WITH ANNUAL CONFERENCE, SICE FES 2024
题目: Development of a flexible magnetic multi-arm robot for ceiling
摘要: Continuum robots have the capability to perform flexible locomotion for navigation through tortuous paths. In this paper, we propose a multi-arm robot with multiple flexible tendon-driven continuum robots with magnets to demonstrate how compliance can be used to navigate through difficult environments. Furthermore, we propose an extended mathematical model from the Cosserat rod model to solve for the inverse kinematics of the robot to use motor displacements as inputs for robot control. We show an initial prototype of our system, and demonstrate the proposed robot's movement with an obstacle avoidance and an u-shaped locomotion experiment.

【文献 100】
作者: Hou, MQ
来源: 2024 IEEE/RSJ INTERNATIONAL CONFERENCE ON INTELLIGENT ROBOTS AND
题目: Design and implementation of a novel wheel-based cable inspection robot
摘要: EMIRATES

【文献 101】
作者: Zoula, M
来源: 2024 IEEE INTERNATIONAL CONFERENCE ON ROBOTICS AND AUTOMATION, ICRA 2024
题目: Wireless Communication Infrastructure Building for Mobile Robot Search
摘要: In the paper, we address wireless communication infrastructure building by relay placement based on approaches utilized in wireless network sensors. The problem is motivated by search and inspection missions with mobile robots, where known sensing ranges may be exploited. We investigate the relay placement, establishing network connectivity to support robust food-based communication routing. The proposed method decomposes the given area into Open space and Corridor space where specific deployment patterns allow for guaranteed k-connectivity, making the resulting network redundant while keeping channel utilization bounded. In particular, a hexagonal tesselation coverage pattern with 3-connectivity is investigated in Open space and a linear 4-connectivity pattern in Corridor space, respectively. The proposed approach is empirically evaluated in a realistic scenario, and based on the reported results, it is found superior compared to the existing stochastic randomized dual sampling schema.

【文献 102】
作者: Tang, W
来源: MACHINE VISION AND APPLICATIONS
题目: Active perception based on deep reinforcement learning for autonomous
摘要: In this study, an artificial intelligence framework is developed to facilitate the use of robotics for autonomous damage inspection. While considerable progress has been achieved by utilizing state-of-the-art computer vision approaches for damage detection, these approaches are still far away from being used for autonomous robotic inspection systems due to the uncertainties in data collection and data interpretation. To address this gap, this study proposes a framework that will enable robots to select the best course of action for active damage perception and reduction of uncertainties. By doing so, the required information is collected efficiently for a better understanding of damage severity which leads to reliable decision-making. More specifically, the active damage perception task is formulated as a Partially Observable Markov Decision Process, and a deep reinforcement learning-based active perception agent is proposed to learn the near-optimal policy for this task. The proposed framework is evaluated for the autonomous assessment of cracks on metallic surfaces of an underwater nuclear reactor. Active perception exhibits a notable enhancement in the crack Intersection over Union (IoU) performance, yielding an increase of up to 69% when compared to its raster scanning counterpart given a similar inspection time. Additionally, the proposed method can perform a rapid inspection that reduces the overall inspection time by more than two times while achieving a 15% higher crack IoU than that of the dense raster scanning approach.

【文献 103】
作者: Naganuma, M
来源: 2024 SICE FESTIVAL WITH ANNUAL CONFERENCE, SICE FES 2024
题目: Development of Thrust Deflection Mechanism for Controlling Attitude of
摘要: Concrete structures such as bridges and tunnels are constructed on three-dimensional surfaces, and their complex environments make it difficult for robots to move. This paper describes a method for constructing stability models of floors and walls necessary for a robot to move on a three-dimensional surface. A new robot was developed based on this theory. The proposed mechanism not only uses the thrust generated by the propeller to stabilize its attitude, but also allows for efficient movement by deflecting the thrust in an appropriate direction. Based on this deflection thrust, this paper clarified the conditions under which the developed robot would not slip or fall on a three-dimensional surface, and constructed a theoretical stability model. In the experiment, we verified the robot's movement from the floor to the wall, and confirmed that the robot can move efficiently between the two surfaces.

【文献 104】
作者: Tandon, A
来源: ADVANCES IN INFORMATION TECHNOLOGY IN CIVIL AND BUILDING ENGINEERING,
题目: Autonomous Navigation of Quadruped Robots for Monitoring and Inspection
摘要: Mobile robots have increasingly been gaining recognition in monitoring and inspection of civil infrastructure, owing to their potential to improve efficiency and accuracy. Specifically, quadruped robots offer enhanced stability and adaptability, rendering the robots ideal candidates for automated monitoring and inspection. To enable quadruped robots to conduct monitoring and inspection of civil infrastructure, the robots must be capable of autonomous navigation. Existing approaches towards autonomous navigation usually incorporate joint-state information to plan motions with whole-body controllers, representing a non-linear, high-dimensional problem that is computationally expensive to solve and is a particular burden when implemented into the robots for real-time navigation. This paper presents an integrated architecture for automated monitoring and inspection that consists (i) of a mission planning framework devised for planning monitoring and inspection tasks and (ii) a robust motion planning framework to enable real-time navigation. Specifically, the motion planning problem is decoupled into high-level task-space and low-level joint-space components, and the motion planning framework, building upon the "Cartographer" simultaneous localization and mapping algorithm, combines the "batch-informed trees" and A* algorithm for global planning and the "timed elastic band" for local planning and obstacle avoidance. For validation, the integrated architecture for automated monitoring and inspection is implemented into quadruped robots, and validation tests are conducted in indoor office environments to be inspected. As a result, it is demonstrated that the quadruped robots navigate safely and collision-free in real time, accommodating both static and dynamic obstacles. Enhancing the efficiency and accuracy of autonomous navigation of quadruped robots in complex and dynamic environments, the architecture is expected to pave the way for future research in robust controller development and 3D-state space planning.

【文献 105】
作者: Wang, HW
来源: INDUSTRIAL ROBOT-THE INTERNATIONAL JOURNAL OF ROBOTICS RESEARCH AND
题目: Adaptive autonomous navigation system for coal mine inspection robots:
摘要: PurposeIn response to the navigation challenges faced by coal mine tunnel inspection robots in semistructured underground intersection environments, many current studies rely on structured map-based planning algorithms and trajectory tracking techniques. However, this approach is highly dependent on the accuracy of the global map, which can lead to deviations from the predetermined route or collisions with obstacles. To improve the environmental adaptability and navigation precision of the robot, this paper aims to propose an adaptive navigation system based on a two-dimensional (2D) LiDAR.Design/methodology/approachLeveraging the geometric features of coal mine tunnel environments, the clustering and fitting algorithms are used to construct a geometric model within the navigation system. This not only reduces the complexity of the navigation system but also optimizes local positioning. By constructing a local potential field, there is no need for path-fitting planning, thus enhancing the robot's adaptability in intersection environments. The feasibility of the algorithm principles is validated through MATLAB and robot operating system simulations in this paper.FindingsThe experiments demonstrate that this method enables autonomous driving and optimized positioning capabilities in harsh environments, with high real-time performance and environmental adaptability, achieving a positioning error rate of less than 3%.Originality/valueThis paper presents an adaptive navigation system for a coal mine tunnel inspection robot using a 2D LiDAR sensor. The system improves robot attitude estimation and motion control accuracy to ensure safe and reliable navigation, especially at tunnel intersections.

【文献 106】
作者: Fu, JY
来源: COMPUTERS AND ELECTRONICS IN AGRICULTURE
题目: Autonomous net inspection and cleaning in sea-based fish farms: A review
摘要: In sea-based fish farms, biofouling and net damage are unavoidable challenges. To ensure safe, reliable, and sustainable fish production, timely monitoring of nets is crucial for detecting biofouling and net damage, along with providing decision support for subsequent maintenance and cleaning. In recent years, technological advancements have driven the automation of production processes, with a growing trend toward using robots instead of human labor for net operations in sea-based fish farms. However, there is a lack of a systematic review of autonomous net inspection and cleaning. This paper addresses this gap by reviewing and analyzing the current state of autonomous net inspection and cleaning in sea-based fish farms. Key technologies, including robot control, net inspection, and net cleaning, are summarized, along with their future development in practical applications. This paper also emphasizes Industry 4.0 technologies that support these advancements, such as sensors, robotics, artificial intelligence (AI), the Internet of Things (IoT), big data analytics, and the digital twin (DT). Furthermore, advanced robotic solutions currently used for autonomous net inspection and cleaning, as well as their potential benefits and drawbacks, are presented. Finally, the challenges and future research directions are highlighted, offering valuable insights for institutions and companies working to enhance the autonomy and intelligence of net operations in sea-based fish farms.

【文献 107】
作者: Lee, M
来源: APPLIED SCIENCES-BASEL
题目: Development of a Capsule-Type Inspection Robot Customized for Ondol
摘要: Ondol is a heating system unique to Korean homes that increases indoor temperatures by supplying hot water through pipes embedded in floor slabs. Known for its comfort and sustained heating advantages, ondol has garnered international interest in countries requiring efficient heating solutions. Given the inherent challenges faced during installation and operation, timely inspection of ondol is crucial due to difficulties in detecting and locating defects in buried concrete pipes, often leading to costly rework and removal. However, specialized inspection systems tailored to ondol pipes remain underexplored. Therefore, this paper proposes a robotic inspection system capable of assessing the conditions of ondol pipelines. We analyze the characteristics of ondol piping to establish system requirements and develop a prototype of a compact capsule-shaped inspection robot tailored for ondol pipe inspection. Subsequent laboratory testing validates system performance and usability, confirming field applications through curvature maneuverability and image reception quality tests. This study aims to motivate advancements in ondol-specific system implementation and performance validation, potentially contributing to the smartification of ondol maintenance practices.

【文献 108】
作者: Kita, T
来源: 2023 IEEE UNDERWATER TECHNOLOGY, UT
题目: Prototyping of Automatic Navigation of Underwater Robot for Underwater
摘要: Underwater robots are expected to support the underwater inspection performed by divers because the number of divers has been decreasing in Japan. To progress the introduction of robots to underwater inspection, we prototyped the automatic navigation system for underwater plies. We modified a commercially available small ROV into an AUV without cable, that hold down the cost. In addition, we investigated the navigation path and prototyped automatic navigation software for piles. Then we conducted a navigation test in a water tank. As the result, we confirmed that the robot was navigated automatically on the planned path and identify issues.

【文献 109】
作者: Müller, D
来源: 2024 18TH INTERNATIONAL CONFERENCE ON CONTROL, AUTOMATION, ROBOTICS AND
题目: Inside Bridges: Autonomous Crack Inspection with Nano UAVs in
摘要: EMIRATES

【文献 110】
作者: Sang, IC
来源: IEEE SENSORS JOURNAL
题目: An Autonomous Underwater Vehicle Simulation With Fuzzy Sensor Fusion for
摘要: Underwater pipeline inspection is an important topic in off-shore subsea operations. Remotely operated vehicles (ROVs) can play an important role in multiple application areas including military, ocean science, aquaculture, shipping, and energy. However, using ROVs for inspection is not cost-effective, and the fixed leak detection sensors mounted along the pipeline have limited precision. Although the cost can be significantly reduced by applying autonomous underwater vehicles (AUVs), the unstable current, low visibility, and loss of GPS signal make the navigation of AUVs underwater very challenging. Previous studies have been conducted on coordinate-based, vision-based, and fusion-based navigation algorithms. However, the coordinate-based algorithms suffered from the denial of GPS signals while the vision-based methods typically relied on terrain and landscape knowledge that required collection prior to the mission. As a result of these issues, a navigation system for an AUV that incorporates vision and sonar sensors is presented in this article. In a robot operating system (ROS)/Gazebo-based simulation environment, the AUV had the ability to find and navigate toward the pipeline and continuously traverse along its length. Additionally, with a chemical concentration sensor mounted on the AUV, the system demonstrated the capability of inspecting the pipeline and reporting the leak point with a resolution of 3 m along the pipeline.
