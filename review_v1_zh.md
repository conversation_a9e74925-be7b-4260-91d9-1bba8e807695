---
title: 机器人裂缝检测综述：从平台到部署算法的系统级视角
author: [作者姓名]
date: \today
---

摘要：土木基础设施的安全性和耐久性在很大程度上依赖于有效的结构健康监测（SHM），其中裂缝是结构退化的主要标志。尽管机器人系统已成为替代高风险人工作业的颠覆性技术，但在孤立的算法进步与将其稳健地集成到可现场部署的机器人平台之间，仍然存在着巨大的鸿沟。本文对机器人裂缝检测进行了全面的系统级综述，描绘了从硬件到已部署智能的完整技术流程。我们首先对构成检测系统物理基础的多样化机器人平台和多模态传感器套件进行分类和分析。然后，本综述的核心部分深入探讨了感知算法的演进，追溯了从传统图像处理到先进深度学习模型的发展轨迹，并特别关注了对设备端推理至关重要的架构创新和轻量化设计。至关重要的是，我们通过审视算法部署策略、感知-规划-执行一体化的架构范式以及从真实世界案例中汲取的经验，将理论与实践联系起来。本文的主要贡献在于其整体性的、面向集成的综合分析，这与以往的综述不同，它强调了在传感器融合和人工智能技术推动下，算法与硬件的协同设计。最后，通过剖析持续存在的挑战并确定未来的研究方向（如基础模型、以数据为中心的人工智能和多智能体系统），本综述为开发下一代稳健、高效和可信赖的机器人检测系统提供了战略路线图。

关键词：机器人检测，裂缝检测，结构健康监测（SHM），无人机（UAV），深度学习，系统集成，传感器融合，自主系统。

<br>
<div align="center">
    <b>图示摘要概念</b><br>
    <pre class="mermaid">
    graph TD
        subgraph "A. 物理层：平台与传感器"
            P[机器人平台<br>(无人机, 地面无人车, 攀爬机器人)]
            S[多模态传感器<br>(RGB相机, 激光雷达, 热成像, 无损检测)]
            P -->|搭载| S
        end
        
        subgraph "B. 感知与智能层"
            D[原始传感器数据]
            S --> D
            M{AI模型<br>(CNN, Transformer, 轻量化)}
            D --> M
            O[优化的设备端模型<br>(TensorRT, TFLite)]
            M --> O
        end

        subgraph "C. 行动与应用层"
            A[自主行为<br>(路径规划, 反应式控制)]
            O -->|触发| A
            DT[数字孪生与缺陷地图]
            A -->|更新| DT
            PM[预测性维护决策]
            DT --> PM
        end

        linkStyle 2 stroke:blue,stroke-width:1.5px
        linkStyle 6 stroke:green,stroke-width:1.5px
        linkStyle 8 stroke:red,stroke-width:1.5px
    </pre>
    <br>
    <b>机器人检测流程的高层概览，展示了从物理平台与传感器（A），经由AI驱动的感知（B），到自主行动与基础设施管理（C）的协同作用。</b>
</div>
<br>

## 1. 引言

### 1.1. 研究背景与意义

桥梁、隧道、大坝和管道等土木基础设施构成了现代社会的基石[1, 2]。然而，这些资产的结构完整性因老化、环境应力和运营荷载而持续受损，通常表现为表面裂缝。这些裂缝若不加监测，可能扩展并导致严重的结构失效，从而造成灾难性的经济损失和人员伤亡。因此，结构健康监测（SHM），特别是及时准确地检测裂缝，是任何有效的基础设施管理和公共安全策略中不可或缺的组成部分[3]。

几十年来，检测的标准做法一直依赖于人工目视评估，这种方法充满局限性[1]。它具有主观性，结果取决于检查员的经验[4, 5]；它劳动强度大、成本高，尤其对于大型结构；最关键的是，在检查难以到达的位置（如桥梁缆索或风力涡轮机叶片）时，它使人员面临巨大的安全风险[6,7,8]。这些深刻的缺陷催生了一场向自动化检测系统的范式转变，该系统利用了机器人技术和人工智能的进步[9]。

过去十年见证了这类系统潜力的研究激增。先进的深度学习模型现在可以高精度地检测裂缝[10, 11]，而包括无人机（UAVs）[12, 13]、地面车辆（UGVs）[14, 15]和专用攀爬机器人[16, 17]在内的多样化机器人平台，为复杂的结构环境提供了前所未有的可达性。然而，尽管在这些独立组件上取得了重大进展，一个关键的挑战已经浮现：在实验室环境中开发高性能感知算法与在现场将其部署为可靠、自主的机器人系统之间存在差距。从学术原型到稳健、可现场部署的解决方案的成功过渡，取决于一种目前文献中尚未充分探讨的整体性、系统级的方法。

### 1.2. 核心术语与问题定义

本综述的核心焦点是**机器人裂缝检测**，这是一个涉及机器人技术、计算机视觉和土木工程的跨学科领域。这一概念（在早期集成移动机器人与无损评估（NDE）传感器的系统中形成[18, 19]）涉及利用自主或半自主移动机器人在目标结构上导航，获取感官数据（主要是视觉图像），处理这些数据以识别和表征裂缝，并在高级系统中，将其位置映射到结构的全局坐标系中。

关键术语和构成子问题定义如下：

*   **机器人平台：** 用于数据采集的移动机器人系统[1, 20]。这包括广泛的系统，从提供对高处和偏远区域快速、非接触式访问的**无人机（UAVs）**[21]，到适用于桥面[22]或钢结构[17]等表面的**无人地面车辆（UGVs）**和**攀爬机器人**。平台的有效性由其机动性、稳定性和有效载荷能力定义。
*   **传感器系统：** 集成在机器人平台上的传感器套件[23, 24]。虽然**高分辨率RGB相机**是视觉裂缝检测的主要传感器，但系统通常会集成其他模态。**激光雷达（LiDAR）**和**RGB-D相机**为定位和建图提供3D信息[13, 15]；**热成像相机**可以检测次表层缺陷或水分[25]；**超声波传感器**可以量化裂缝深度[26]。集成和融合来自这些传感器的数据对于全面评估至关重要。
*   **定位与建图：** 为使机器人系统能够自主运行并使缺陷位置具有意义，机器人必须能够确定其自身相对于结构的位置和方向，并创建其环境的地图。这通常通过**同时定位与建图（SLAM）**技术来解决[27, 28]，这在GPS信号受限的环境中（如桥下或隧道内）尤为关键[2, 13]。
*   **裂缝检测算法：** 指用于处理收集的传感器数据并识别裂缝的计算方法[4, 5]。这些算法已从**传统图像处理**方法（例如，边缘检测、阈值分割）演变为**机器学习**，以及最近的**基于深度学习的方法**[15]。现代算法不仅旨在检测，还旨在进行语义分割（像素级定位）和量化（测量长度、宽度和方向）。
*   **路径规划：** 该子问题涉及为机器人确定一条最优轨迹，以确保完全有效地覆盖感兴趣的结构表面[29, 30]。路径必须可导航、无碰撞，并设计为保持适当的距离和视角，以获取高质量的数据。

因此，解决机器人裂缝检测问题需要一种整体的、系统级的方法。仅仅开发一个精确的裂缝检测算法是不够的；该算法必须能够部署在一个能够在复杂的真实世界环境中自主可靠地执行检查任务的移动平台上。

### 1.3. 本文贡献与结构

虽然已有众多综述探讨了自动化检测的特定方面，但它们通常孤立地研究感知算法[4]或无损检测传感技术[1]。这种以组件为中心的观点忽略了在硬件、软件和真实世界操作约束的交叉点上出现的关键、相互依赖的挑战。本文旨在通过对整个机器人裂缝检测流程进行全面的、系统级的综合分析，并明确关注其实际部署路径，从而弥合这一差距。本综述的主要贡献有四方面：

1.  **系统级综合分析：** 我们提供了一个整体性分析，将机器人平台和传感器的能力与局限性（"身体"）与感知算法的演进和性能（"大脑"）联系起来，提供了一个先前文献所缺乏的统一框架。
2.  **面向部署的视角：** 我们明确地探讨了在资源受限的机器人硬件上部署复杂算法以及将感知与自主导航集成的实际挑战，强调了对真实世界应用至关重要的策略。这使得我们的综述区别于那些纯粹以算法为中心的工作。
3.  **关注协同集成：** 我们特别分析了传感器、人工智能和机器人平台之间的协同作用，展示了它们的协同设计对于创建真正的自主现场系统是多么关键，这是一个经常被忽视的核心主题。
4.  **战略性前瞻分析：** 我们提炼了该领域面临的最持久的挑战，并将其映射到具有高影响力的未来研究方向，提供了一个旨在指导研究人员和实践者开发下一代稳健和智能检测系统的战略路线图。

本综述的组织结构如下：第二章详细介绍了各种机器人平台和传感器系统。第三章对感知算法的发展进行了详尽的分析。第四章讨论了算法部署和系统集成的实际问题。第五章概述了当前的挑战和未来的研究趋势。最后，第六章总结了主要发现并对该领域的未来发展轨迹提出了最终展望，从而结束本文。

---

## 第二章 机器人平台与传感器系统

自动化裂缝检测策略的成功实施，根本上依赖于底层硬件的能力。一个机器人检测系统是一个复杂的集成体，它包含提供必要机动性和可达性的移动平台，以及执行数据采集的传感器套件。这些组件的选择和设计并非随意的；它们由基础设施的具体特征、环境条件以及期望的检测结果所决定。本章详细考察了构成现代结构检测物理基础的多样化机器人平台和多模态传感器系统。我们首先对各种移动平台进行分类和分析，然后深入探讨它们所采用的传感器技术。

### 2.1. 用于裂缝检测的机器人平台

机器人平台的选择是设计检测系统的第一个关键决策，因为它直接决定了系统在目标环境中导航和有效定位传感器的能力。多年来，研究人员开发和改造了各种各样的平台，每种平台都有其独特的优势和固有的局限性。这些平台可以根据其主要操作领域大致分为：地面与攀爬平台、空中平台和水上平台。然而，机械设计、机动性约束和环境适应性的异质性，要求我们对每种平台的操作领域和权衡进行情境化的理解。以下小节将详细探讨每个类别。

#### 2.1.1. 地面移动与结构附着机器人

基于地面的机器人系统，包括移动平台和结构附着平台，是高精度、近距离结构检测的基石。它们的主要优势在于能够承载大量有效载荷，并为各种无损检测传感器提供稳定的基础，从而实现其他平台通常无法完成的详细数据采集。本节深入探讨了各种类型的地面和攀爬机器人，突出了它们的操作能力和特定的应用领域。

##### 2.1.1.1. 地面检测平台（轮式、履带式和腿式）

轮式和履带式无人地面车辆（UGV）是检查大型水平表面（如桥面、路面和隧道地面）最成熟的平台。其坚固的设计提供了高有效载荷能力和长续航时间，使其成为携带综合传感器套件的理想选择。一个著名的例子是机器人辅助桥梁检测工具（RABIT），这是一个集成了探地雷达（GPR）、冲击回波传感器和摄像头用于自主、多模态桥面评估的全向UGV [18, 23, 31, 19]。更新的系统利用SLAM的进步来融合激光雷达和摄像头数据，使UGV能够生成具有精确定位缺陷的高保真3D损伤地图[15]。虽然这些平台在预备表面上非常有效，但它们的主要局限性在于无法逾越重大障碍或穿越不连续和垂直的地形。为了解决这一机动性差距，腿式机器人，特别是四足机器人，已成为一个有前途的替代方案。像ANYmal这样的平台已经展示了在工业检测任务中穿越不平坦地形和爬楼梯的能力[6]，先进的状态估计技术使其即使在具有挑战性的表面上也能实现稳健的运动[32]。然而，增加的机械和控制复杂性仍然是其广泛采用的重大障碍。

##### 2.1.1.2. 爬墙与磁性平台

为了解决标准UGV无法到达的垂直维度，一类专门的结构附着机器人被开发出来。这些攀爬机器人被设计用于穿越垂直或倒置的表面，使其在检查钢桥构件、高层建筑外墙和工业储罐等结构时不可或缺。附着力通过针对表面材料量身定制的多种机制实现。对于铁磁结构，通常采用配备永磁体和高摩擦轮或履带的磁性爬行器，展示了在复杂钢结构几何形状上导航并克服接头和焊缝等障碍的能力[16, 17]。对于非铁磁表面，附着力通常通过负压（真空吸附）或空气动力推力实现。基于推力器的攀爬器通常是混合空中-攀爬系统，使用螺旋桨产生持续的下压力，将机器人压在墙上[33, 34, 35, 36]。这些平台为在垂直表面上进行详细的、近距离的视觉或基于无损检测的检查提供了独特的能力。然而，它们的实际应用面临着相当大的挑战，包括运动速度慢、能耗高以及在肮脏、潮湿或有纹理的表面上附着力不可靠，这需要复杂的控制和机械设计[37, 30]。

##### 2.1.1.3. UGV与机械臂协作系统

为了增强地面平台的多功能性并扩展其感官范围，许多现代系统将UGV与机器人操纵臂集成在一起。这种协作配置允许移动基座保持在稳定、可达的表面上，而手臂则将传感器载荷定位到复杂或难以到达的位置。这对于需要精确定位传感器或物理接触的任务尤为关键，例如定位超声波换能器以测量裂缝深度[26]或瞄准摄像头检查桥梁梁的下侧。例如，ROBO-SPECT项目利用移动车辆上的起重机和高精度机械臂进行详细的隧道缺陷评估[26]。这些系统中的主要挑战在于协调高自由度操纵臂与移动基座的运动，特别是在减轻操纵臂运动对基座的干扰以确保末端执行器的稳定性和精度方面。该领域的研究探索了先进的控制技术，例如使用广义雅可比矩阵来最小化基座运动和反作用力矩，从而提高操持任务的准确性[38]。

#### 2.1.2. 空中机器人平台

虽然地面系统在近距离、高保真度检测方面表现出色，但它们通常由空中平台作为补充，以实现快速、大面积的覆盖。无人机（UAVs），特别是多旋翼无人机，在现代检测工作流程中扮演着至关重要的辅助角色。它们为桥梁上部结构、塔架、风力涡轮机叶片和冷却塔等高处和难以到达的位置提供了无与伦比的可达性[12, 8, 21, 39]。它们的主要优势是能够快速高效地进行初步的、大规模的视觉勘察，识别出可能需要地面或攀爬机器人进行更详细后续检查的潜在关注区域。然而，无人机用于详细裂缝检测的效用常常受到风阵[40]引起的稳定性问题、有限的飞行续航时间以及维持与检测表面恒定、最佳对峙距离的固有困难的限制，这会影响图像质量和测量精度[41]。因此，许多研究致力于开发稳健的飞行控制器和自主路径规划算法，以提高无人机在近距离检测任务中的可靠性[29, 42]。

#### 2.1.3. 水面与密闭空间平台

除了陆地和空中领域，对于水下或密闭空间内的基础设施，也需要专门的机器人。对于水下资产，如桥墩、坝面和海上结构物，使用无人水面艇（USVs）和无人水下航行器（UUVs）。这些平台使用声纳和专用相机，在对人类潜水员危险且无法进入的环境中进行检查[21, 31, 34]。对于城市公用事业网络，管道内检测机器人对于检测下水道和供水管道中的裂缝和堵塞至关重要[2, 43]。这些机器人的设计必须能够承受极端的环境条件，同时克服与通信受限、在特征稀疏的环境中导航以及缺乏GPS相关的基本挑战。

#### 2.1.4. 通用平台设计与智能导航能力

无论其具体操作领域如何，现代检测机器人的有效性越来越不仅仅由其机械设计决定，还由其自主水平决定。实现自主操作取决于一个稳健的智能导航系统，这是所有平台类型的共同要求。一个关键的使能技术是SLAM，它允许机器人在构建未知环境地图的同时，跟踪自己在其中的位置。这在GPS信号受限的区域（如桥下或隧道内）至关重要[14, 2]。现代SLAM系统通常融合来自多个传感器（通常是激光雷达、相机和惯性测量单元（IMU））的数据，以实现稳健和准确的状态估计[27, 28, 44, 45]。

基于SLAM生成的地图或预先存在的建筑信息模型（BIM），自主机器人必须接着规划其路径。**覆盖路径规划（CPP）**算法对于生成最优轨迹至关重要，以确保有效且无碰撞地检查整个感兴趣的表面[29, 30]。这些算法旨在最小化检查时间和能耗，同时保证传感器保持适当的姿态以获取高质量数据[42, 39]。这些导航能力的成功集成，将一个遥控机器转变为一个智能代理。然而，这种自主性严重依赖于感知系统提供的感官数据的质量和丰富性，这构成了通往后续章节所讨论主题的关键桥梁。

### 2.2. 多模态传感器系统

机器人平台提供了物理上的可达性，而机载的多传感器套件则实现了感知和数据采集。一次有效的检查取决于一组经过精心选择和集成的传感器，它们不仅能够捕捉可见的表面缺陷，还能捕捉到次表面的异常和结构的几何形状。当前的趋势明显是从单一传感器系统向多模态方法转变，即融合来自不同传感器的数据，以创建对结构健康状况更全面、更可靠的理解。这种协同方法使得一种传感器的优势能够弥补另一种传感器的局限性。最终，稳健的传感器融合是构建可靠的自主行动感知系统的基石。本节回顾了关键的传感器技术，并讨论了它们集成的关键实践。

#### 2.2.1. 视觉与光学传感器

视觉和光学传感器是非接触式机器人检测的基石，为裂缝检测（`检测任务`）和三维环境重建（`建图任务`）提供丰富的数据。

*   **高分辨率RGB相机：** 这是裂缝检测的主要传感器。相机在机器人上的物理安装至关重要；不当的倾斜角度会引入透视畸变，而来自平台的机械振动会降低图像清晰度。先进的系统专注于优化整个数据采集流程。例如，Spencer等人[5]开发了STRUM分类器，该分类器处理来自机器人桥梁扫描系统的图像，实现了95%的裂缝检测准确率。然而，在动态条件下，由于`光照变化、运动伪影和天气引起的噪声`等环境因素，图像质量仍然是一个挑战。为了解决运动模糊问题，Morita等人[46]提出了一种从混凝土的斑驳纹理中估计模糊参数的方法，利用倒谱分析来锐化移动相机拍摄的图像。为了应对光照问题，Kim等人[47]提出了VIVID++数据集，其中包含对齐的RGB、热成像和事件相机数据，促进了更稳健的视觉算法的开发。

*   **三维几何传感器（激光雷达、RGB-D、结构光）：** 这些传感器对于为`建图`和`避障`任务捕捉几何背景至关重要。激光雷达在构建精确的数字孪生中发挥着重要作用。Kim等人[13]在无人机上使用倾斜的3D激光雷达，通过HG-SLAM实现自主桥梁建图。Li等人[15]展示了一款地面机器人，使用增强的KISS-ICP算法处理激光雷达数据，以实现高效的3D损伤建图。为获得更高分辨率，则使用结构光。Lu等人[48]使用结构光相机和PointNet++模型，以超过72%的交并比（IoU）识别复合材料中的平面外缺陷。

*   **热成像（红外）相机：** 超越可见光谱，热成像相机能够揭示次表层缺陷。Laureti等人[49]开发了一个使用脉冲热成像的机器人检测平台，展示了其显著应用。他们的工作引入了一种新颖的算法，用于精细对齐和融合多幅热成像图像，从而能够准确检测具有复杂几何形状部件的次表层缺陷。

#### 2.2.2. 非视觉传感器的集成

全面的评估需要探测地表以下（`检测任务`）并确保稳健的导航（`定位任务`），这些任务通过集成非视觉传感器来完成。

*   **导航与定位传感器：** 惯性测量单元（IMU）是状态估计的基础。其重要性在紧耦合的SLAM框架中表现得最为明显，如LIO-SAM [27]和FAST-LIO [28]，它们融合IMU和激光雷达数据以实现稳健的轨迹估计，尤其是在快速移动或特征稀疏的环境中。这些方法对于在GNSS信号不可用时保持准确定位至关重要[13, 44]。

*   **无损评估（NDE）传感器：** 为了量化损伤，机器人携带NDE传感器，其选择在很大程度上取决于材料。对于**混凝土结构**，像RABIT [18]这样的平台携带**探地雷达（GPR）**来绘制钢筋图并检测次表层分层。对于**钢结构**，像He等人[17]的机器人集成了**涡流传感器**来发现疲劳裂纹。对于**复合材料结构**，Liu等人[50]使用带有**声学传感器**和CNN模型的机器人，以96.8%的准确率对界面空隙进行分类。许多这些基于接触的方法都部署在协作式的UGV-机械臂系统上，由操纵臂高精度地定位传感器[26]。

#### 2.2.3. 数据采集与传感器集成实践

拥有多个传感器并不足够；通过精心的布局、校准和同步进行有效集成，才能实现可靠的数据融合。图2.1展示了一个典型的数据融合流程。

*   **数据融合与系统集成：** 目标是将数据融合成一个单一的、信息丰富的模型。一个典型的例子是Li等人[15]的系统，它通过**将二维裂缝特征投影到三维点云空间**来实现精确的三维损伤可视化。RABIT平台（图2.2）进一步说明了这一点，其中来自GPR、冲击回波和相机的数据被融合以生成桥面的统一状况图[18, 19]。

<br>
<div align="center">
    <b>图 2.1.</b> 机器人检测多传感器融合流程概念图，展示了从原始传感器数据（相机、激光雷达、IMU）经过校准和同步，到融合输出（如3D缺陷图）的流程。
</div>
<br>
<div align="center">
    <b>图 2.2.</b> RABIT机器人检测平台及其标记的多传感器NDE套件，包括GPR阵列、相机和冲击回波传感器。（来源：[18]）
</div>
<br>

*   **传感器校准与同步：** 可靠的融合取决于精确的时空对齐。**外参校准**，即确定传感器之间的相对姿态，是关键的第一步。Gonzalez-de-Soto等人[51]强调需要自动化校准框架来提高准确性。**时间同步**同样至关重要，特别是对于像LIO-SAM [27]这样的紧耦合算法，毫秒级的时间误差都可能导致估计器失效。

表2.1总结了常见的传感器模态及其特性。

<br>

**表 2.1.** 用于机器人裂缝检测的传感器模态。

| 传感器模态 | 主要功能 | 对应的机器人任务 | 主要局限性 |
| :--- | :--- | :--- | :--- |
| **RGB相机** | 捕捉高分辨率表面纹理和颜色 | 缺陷检测、视觉伺服 | 对光照敏感、运动模糊、仅限于表面特征 |
| **激光雷达** | 提供精确的三维几何点云 | 建图、定位（SLAM）、导航中的避障 | 数据稀疏、成本高、难以处理反射或透明表面 |
| **热成像相机** | 测量表面温度分布 | 次表层缺陷检测（分层、空洞） | 需要热对比度、解读可能复杂、分辨率较低 |
| **IMU** | 测量线性加速度和角速度 | 状态估计、定位（SLAM） | 随时间漂移严重、需要与其他传感器融合以保证精度 |
| **GPR / 涡流 / 声学** | 检测内部材料属性/缺陷 | 次表层/内部缺陷量化 | 通常需要接触/近距离、依赖材料、有效载荷重 |
| **RGB-D / 结构光** | 提供密集的短距离三维深度图 | 局部建图、导航中的避障、三维重建 | 范围有限、对环境光敏感（特别是结构光） |

<br>

随着多传感器机器人平台的普及，挑战从数据采集转向了智能解读。下一节将探讨如何利用先进的基于学习的方法，从这些传感器系统产生的丰富的多模态数据流中提取可操作的见解。

---

## 第三章 智能缺陷感知的算法进展

前几章确立了作为结构评估"眼睛"和"耳朵"的物理机器人平台和传感器系统。本章深入探讨检测系统的"大脑"：将原始、通常带有噪声的传感器数据转化为关于结构完整性的可操作知识的复杂算法。缺陷（特别是裂缝）的自动检测是结构健康监测（SHM）和无损检测（NDT）的基石，应用范围广泛，从桥梁和隧道等土木基础设施[52, 53, 54]，到管道[55, 56]、风力涡轮机叶片[57]等关键能源部件，甚至在先进材料制造[58, 59]和医疗诊断[60, 61]中都有应用。及时准确地识别缺陷对于确保安全、运营可靠性和经济效率至关重要。

这些检测算法的演进以一个显著的范式转变为标志：从基于手工规则的方法转向数据驱动的深度学习（DL）模型[62, 63, 64]。虽然传统技术奠定了基础，但它们往往难以应对多样化的真实世界条件所需的鲁棒性。DL的出现彻底改变了该领域，通过直接从数据中学习复杂的缺陷特征，提供了前所未有的性能[65]。本章通过系统地分析近期文献中提出的具体创新，描绘了这些算法进展的轨迹。我们将首先通过研究以数据为中心的增强策略来解决数据质量的基础挑战。然后，我们将回顾核心DL模型的架构演变，接着探讨如何利用注意力机制和多模态数据融合来增强感知。最后，我们将超越标准的监督训练，讨论那些有望实现更自主、更具适应性的检测系统的新兴学习范式。

### 3.1. 以数据为中心的增强策略以实现稳健检测

任何深度学习模型的性能都从根本上受限于其训练数据的质量和多样性。在实践中，从现场检查中获取的数据很少是完美的，存在许多可能严重降低算法性能的问题。因此，一个重要的研究方向不仅关注模型架构本身，还关注以数据为中心的策略来提高数据质量。这些策略主要包括数据预处理、增强和增广。

数据预处理旨在标准化输入并突出复杂背景下的显著缺陷特征。这是许多研究中解决的关键第一步。例如，**Azouz等人[66]**的综述系统地对这些初始步骤进行了分类，强调了在应用任何检测算法之前，普遍使用图像滤波技术来消除噪声和模糊，同时使用图像增强方法来提高对比度和可见度。在此基础上，**Peng等人[67]**在其关于高坝视觉感知的综述中，特别指出了直方图均衡化（及其自适应变体CLAHE）和基于Retinex的算法是减轻光照变化影响的关键方法。Retinex算法受人类视觉系统在不同光照下感知一致颜色的能力启发，对于标准化在光照条件差且不一致的环境（如大坝隧道）中捕获的图像尤为有效。除了这些像素级的调整，更先进的以数据为中心的策略也越来越受到关注。这些包括使用生成对抗网络（GANs）来合成真实的缺陷图像以进行数据增广，从而丰富训练集；以及应用领域自适应技术来提高模型在不同检查环境和条件下的泛化能力[68]。此外，在处理多模态数据时，精确的传感器校准和时空数据对齐是关键的预处理步骤，以确保后续有意义的特征融合[69]。这些增强和增广阶段并非可有可无的程序；它们是确保后续深度学习模型能够有效学习判别性特征的基础，从而超越由噪声和环境伪影引起的虚假相关性，真正捕捉到缺陷的特征。

*一旦原始数据得到增强和预处理，下一个合乎逻辑的步骤是设计能够在不同条件下进行稳健和准确缺陷识别的模型。*

### 3.2. 基于深度学习的检测模型：架构与创新

任何现代自动化检测系统的核心都是负责识别缺陷的深度学习模型。这些模型的应用沿着一条日益提高精度和效率的清晰轨迹发展，从基于补丁的分类器演变为端到端的对象检测器，最终发展到像素级分割网络。

#### 3.2.1. 核心架构的光谱：从补丁到像素

深度学习在缺陷检测中的最初应用通常是将问题转化为图像分类任务。在这种范式下，一张大图被分割成更小的补丁，然后训练一个CNN来将每个补丁分类为"有缺陷"或"无缺陷"。虽然这是基础性的方法，但它计算效率低下，且忽略了缺陷的全局背景，尽管在标记数据稀缺的少样本或弱监督学习场景中仍有其价值。

为了解决这些局限性，该领域迅速采用了**端到端对象检测**框架。这些模型一次性处理整张图像，将缺陷定位在矩形边界框内。其中，像YOLO（You Only Look Once）系列这样的单阶段检测器因其在速度和精度之间取得了卓越的平衡而成为主导力量。**Aromoye等人[70]**的综述强调了此类DL算法在自主无人机管道检测中的关键作用，其中实时性能至关重要。YOLO系列的演进，从早期版本到YOLOv5、YOLOv7和YOLOv8等更新的迭代，反映了为工业应用优化这种速度-精度权衡的持续努力，众多研究针对特定的缺陷检测任务对这些框架进行了调整[71, 72, 73]。

然而，对于严谨的工程分析来说，一个粗略的边界框往往是不够的。这一需求推动了**像素级语义分割**的广泛采用，它已成为高精度缺陷分析的标准。这些模型对图像中的每个像素进行分类，生成一个精确勾勒出缺陷几何形状的详细掩码。U-Net架构是该领域无可争议的基石。其应用广泛，如**Giannuzzi等人[74]**关于评估历史建筑环境的综述所示。他们记录了U-Net及其变体如何成为从图像数据中分类包括裂缝在内的退化现象的主要工具，构成了自动化诊断系统的基础。同样，**Khan[75]**在一篇专注于水资源管理的综述中，指出分割模型是监测和检查水工结构的关键技术，其中精确的裂缝描绘对于评估结构完整性至关重要。

#### 3.2.2. Transformer与混合架构的兴起

虽然CNN通过其卷积核在提取局部特征方面表现出色，但其固有的局部感受野在模拟连续、曲折的裂缝结构所特有的长程依赖关系时可能成为一个限制。这一架构上的约束为**视觉Transformer（ViT）**的引入铺平了道路。Transformer的核心**自注意力机制**使其能够模拟图像所有部分之间的上下文关系，从而更好地感知缺陷的全局连续性。然而，标准的ViT在模拟细粒度局部纹理方面常常表现不佳，而这正是CNN的强项。**Qiao等人[76]**关于金属表面缺陷检测的综述指出了像Swin Transformer这样基于Transformer的模型的出现，它通过引入层次结构和移位窗口来改善局部上下文建模，成为一种先进的方法。因此，研究前沿越来越关注**混合架构**，这种架构协同了两种范式的优势，通常使用CNN作为强大的特征编码器，并使用Transformer作为具有上下文感知能力的解码器[77]。随着这些模型复杂性的增加，一个平行的研究趋势是轻量级Transformer（例如MobileViT）的出现，旨在使其全局推理能力能够应用于资源受限的应用。

#### 3.2.3. 用于实际部署的轻量级模型

追求更高的准确性往往会导致更大、更复杂的模型。然而，对于许多涉及移动或嵌入式系统（如无人机）的实际应用来说，计算资源受到严格限制，这使得**模型轻量化和优化**成为一个关键的研究重点。文献中提出了一套清晰的策略来实现这一点，主要集中在采用高效的主干网络、集成模块化的轻量级设计原则以及利用自动化的架构搜索。

一个主要策略是用专为效率而构建的架构替换标准的主干网络，如ResNet。例如，**GhostNet**通过廉价的线性操作生成更多特征图，得到了广泛采用[78, 79]。其他流行的轻量级主干网络包括**ShuffleNetV2**[71]和**MobileNet**，其变体被用于从轨道扣件检测到裂缝分类的各种任务[80, 81]。除了选择高效的主干网络，研究人员还专注于细粒度的模块化优化，用更高效的替代方案替换标准卷积，如**深度可分离卷积**[82]、**Ghost卷积**和**GSConv**[79, 73]。另一个强大的技术是**结构重参数化**，即将一个复杂的训练时模块融合成一个简单、快速的推理时卷积，这一原则在`RepBSB`模块[72]和`MobileOne`主干网络[83]中得到了展示。此外，像神经架构搜索（NAS）这样的自动化方法，它催生了像MobileNetV3这样的架构，正越来越多地被用来自动发现最优的轻量级模型配置。这些具体、有针对性的优化对于开发可部署在像NVIDIA Jetson系列这样的边缘平台上，并能实现适合实际现场使用的实时推理速度的模型至关重要。

然而，平衡模型的准确性和效率仍然是一个具有挑战性的权衡，尤其是在具有动态实时约束的边缘计算场景中。

<br>

**表1：轻量化技术与应用总结**

| 轻量化策略 | 核心原则 | 代表模型/模块 | 应用案例 | 检测目标 | 部署平台 | 参考文献 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **高效主干网络** | 设计一个计算高效的基础网络。 | GhostNet | 改进YOLOv4 | 绝缘子缺陷 | 无人机 | [78], [79] |
| | | ShuffleNetV2 | YOLO-LRDD | 道路损伤 | 移动设备 | [71] |
| | | MobileNet | 自定义CNN | 轨道扣件 | - | [81] |
| **模块化优化** | 用更轻量的组件替换标准组件。 | 深度可分离卷积 | 改进U-Net | 桥梁裂缝 | 无人机系统 | [82] |
| | | GSConv | 改进YOLO | 输电线路缺陷 | - | [73] |
| **结构重参数化** | 将复杂的训练时模块融合为简单的推理时模块。 | RepBSB模块 | EMB-YOLO | 电表箱缺陷 | - | [72] |
| | | MobileOne | OFN网络 | 输电线路缺陷 | 边缘设备 | [83] |
| **自动化搜索** | 自动搜索最优的轻量级架构。 | NAS (例如, MobileNetV3) | - | 钢轨表面缺陷 | - | [81] |

<br>

#### 3.2.4. 架构增强：注意力与融合

为了在架构修改之外进一步提升模型性能，研究人员越来越关注集成那些能够智能处理特征和融合补充数据源的机制。两种关键策略变得尤为突出：利用注意力机制动态地集中计算资源，以及融合来自多种传感器模态的数据以创建对缺陷性质更全面的理解。

##### 3.2.4.1. 利用注意力机制聚焦显著特征
注意力机制受人类视觉认知启发，使网络能够动态地权衡不同特征或空间位置的重要性，从而让模型专注于最显著的信息并抑制不相关的背景噪声。所回顾的文献揭示了各种注意力模块在增强缺陷检测方面的显著和创新应用，这些应用可大致分为通道注意力、组合空间-通道注意力以及Transformer固有的自注意力机制。

一种常见的策略是将成熟的、轻量级的注意力模块集成到现有的网络主干中，以在不显著增加计算成本的情况下改善特征表示。**通道注意力**，自适应地重新校准通道间的特征响应，是一个受欢迎的选择。例如，像Squeeze-and-Excitation (SE) [78]和Efficient Channel Attention (ECA) [71]这样的模块已成功地被用来提高模型性能。在此基础上，像Convolutional Block Attention Module (CBAM) [72, 84, 85]和Coordinate Attention (CA) [86]这样的**组合通道和空间注意力**模块，它们同时学习关注"什么"和"哪里"，在增强复杂背景下特征的可辨别性方面表现出强大的效果。

除了简单地应用现有模块，一些研究还提出了**新颖或修改过的注意力机制**，以适应特定的缺陷检测任务。例如，**Qiao等人[77]**提出了Defect Transformer (DefT)，其中包括一个轻量级的多池化自注意力块，以高效地建模全局上下文。**Zhang等人[87]**设计了一个双分支轻量级Transformer模块（LGT），利用坐标注意力处理局部特征和双层路由注意力处理全局上下文。此外，像Lightweight Channel-Spatial Attention (LCSA) [79]和Mixed Local Channel Attention (MLCA) [88]这样的自定义模块被专门设计来平衡移动平台部署的性能和效率。这些创新表明，设计专门的注意力策略以应对工业缺陷检测的独特挑战是一个明显的趋势。

*虽然注意力机制优化了模型处理单一数据源信息的方式，但多模态融合通过结合来自完全不同传感器类型的数据，为实现更全面的理解提供了一条路径。*

##### 3.2.4.2. 通过多模态融合创建整体视图
在所回顾的文献中，更受重视的是**多模态特征融合**，它通过结合来自不同传感器类型的信息来提供一个整体的视图。融合策略可以根据集成发生的层面大致分类：**早期融合**在输入层合并原始数据，**中间融合**在网络的不同深度合并特征，而**晚期融合**则合并独立模型的输出或决策。单一的传感器模态通常提供的是不完整的画面。通过融合这些数据流，系统可以做出远为可靠和全面的评估。文献揭示了在NDT/SHM领域中丰富的融合策略：
*   **视觉与热成像：** 将RGB图像与红外热成像（IRT）数据融合，对于检测产生热异常的次表层缺陷非常有效。**Alsuhaibani等人[69]**关于FRP-混凝土结构无损检测的综述强调了这种组合的强大功能。主动热成像技术，即施加外部热源，可以揭示分层和空洞，将这些热数据与视觉图像融合，可以精确定位次表层缺陷。**Oswald-Tranta [89]**在其对金属部件感应热成像的综述中详细介绍了一个具体应用。感应涡流加热组件，IRT相机捕捉热响应；裂缝会扰乱热流，产生清晰的热模式，当映射到视觉图像时，提供完整的诊断。
*   **视觉与声学/超声波：** 将视觉证据与声发射（AE）或导波超声检测（GUT）的数据相关联，可以深入了解缺陷的活动性和严重性。例如，**Ding等人[57]**关于风力涡轮机叶片监测的综述解释说，AE传感器可以检测到叶片复合结构内部活跃裂纹扩展释放的高频应力波，远早于损伤可见。将这种早期预警与无人机的后续视觉检查相融合，可以实现有针对性且高效的维护。同样，**Nuthalapati的综述[90]**关于应力腐蚀开裂的研究讨论了如何利用AE实时监测不锈钢部件中微裂纹的萌生和扩展。
*   **视觉与电磁：** 对于金属结构，将视觉检查与涡流检测（ECT）等方法融合，可以提供全面的诊断。正如**Machado等人[91]**所综述的，ECT探头对导电材料的表面和近表面裂缝高度敏感。通过将ECT的精确裂缝检测与视觉系统的更广泛的上下文感知相结合，检查员可以更全面地了解关键金属部件的结构完整性。

### 3.3. 超越监督训练的学习范式

虽然缺陷检测的大多数研究都基于监督学习，但对大型、精心标注的数据集的依赖仍然是一个重大的瓶颈。这促使了对替代学习范式的探索，这些范式可以减少对标记数据的依赖并增强模型的适应性。

**迁移学习**可以说是被最广泛采用且影响最深远的范式之一。这种方法不是从零开始训练模型，而是用在大型通用数据集（如ImageNet）上预训练过的权重来初始化网络。模型已经学习了丰富的通用视觉特征层次结构，然后可以在一个更小的、特定领域的缺陷数据集上进行微调。这种策略显著减少了所需的标记数据量和训练时间，同时通常还能提高最终性能。它的普遍性使其成为许多以应用为中心的回顾论文中一个基础性但常被默认的技术，例如那些为特定检测任务采用成熟架构（如U-Net或YOLO）的论文。

超越标准的迁移学习，一个有前途的前沿是应用**强化学习（RL）**来创建更智能、更高效的检测工作流程，特别是对于机器人代理。一个基于RL的代理可以通过试错学习最优的检测策略。正如**Zhang等人[92]**所综述的，无人机在像隧道这样复杂、GPS信号受限的环境中导航是一个重大挑战。他们强调深度强化学习（DRL）是一个关键的使能技术。DRL代理可以在模拟中进行训练，学习如何导航、避开障碍物并保持最佳相机角度进行检测，通过高效和全面的数据采集获得奖励。这种"主动视觉"方法标志着从仅仅构建准确的检测器到创建真正智能和自主的检测系统的转变，这些系统能够以最少的人类监督进行学习和适应。

### 3.4. 本章小结

总之，智能缺陷感知的算法骨干已经迅速发展，从传统的补丁分类器演变为适合在嵌入式平台上部署的复杂混合架构和优化的轻量级模型。注意力机制和多模态数据融合的集成进一步增强了这些系统处理复杂现实世界场景的能力和精度。然而，在平衡模型复杂性、数据多样性和泛化能力方面仍然存在挑战——这些问题正越来越多地由新兴的学习范式来解决，并将在后续章节关于未来挑战和研究方向的讨论中成为核心。

## 第四章 从感知到行动：系统集成与自主行为

<br>
<div align="center">
<pre class="mermaid">
graph TD
    subgraph "感知流程"
        A[传感器数据<br>(例如, 图像, 激光雷达)] --> B{裂缝检测模型<br>(设备端)};
        B -- 检测结果 --> C{行为触发逻辑};
    end

    subgraph "机器人行为"
        C -- 指令 --> D[运动规划与控制<br><b>(例如, 行为树, MPC, RL)</b>];
        D --> E[执行器执行];
    end
    
    E --> A;

    linkStyle 4 stroke:red,stroke-width:2px,stroke-dasharray: 5 5;
</pre>
<br>
<b>图 4.2.</b> 智能检测机器人的基本感知-行动循环。设备端感知（左）生成的结果输入到规划与控制模块（右），后者进而执行物理动作，形成一个闭环系统。
</div>
<br>

虽然先进的感知算法（第三章）和稳健的机器人平台（第二章）是基本支柱，但只有当它们被有效地集成到一个有凝聚力的、可现场部署的系统中时，它们的真正价值才能被释放。本章弥合了理论模型与实际应用之间的差距，描绘了从抽象算法到能够自主行动的智能代理的关键路径。我们采用面向过程的视角，将工程流程分解为四个关键阶段：（1）在严格的资源约束下为设备端部署准备和优化模型；（2）将感知集成到机器人的决策循环中以实现智能行为；（3）通过其行为自主性的视角审视真实世界的部署案例；（4）将讨论综合为一套开发稳健机器人检测系统的指导原则。

### 4.1. 嵌入式AI：平衡计算效率与感知保真度

那些达到最先进准确性的复杂深度学习模型通常计算量巨大，使其不适合直接部署在移动机器人上。这些平台在严格的**运行时约束**下运行，包括实时延迟要求、有限的板载内存、严格的功耗预算和热稳定性限制[93, 94]。因此，部署流程中的一个关键首要步骤是模型优化，这个领域通常被称为"设备端AI"或"资源感知AI"[95, 94]，它为在边缘计算硬件上高效推理而对大型模型进行量身定制。

部署之路得到了一个丰富多样的工具生态系统的支持。这些工具可分为三大类：

1.  **来自主要生态系统的生产级框架：** 这是在移动和嵌入式设备上部署模型最常见的工具。谷歌的**TensorFlow Lite (TFLite)**[96]和**PyTorch Mobile**[98]是成熟的框架，可为包括安卓、iOS和像Jetson系列这样的单板计算机在内的各种平台优化模型。例如，**Hammad等人[97]**通过在低成本安卓手机上部署一个用于MRI分类的量化模型，展示了TFLite的有效性。

2.  **硬件特定的加速库：** 为了实现最大性能，硬件供应商提供了与其处理器紧密集成的专门库。**NVIDIA的TensorRT**是一个典型例子，它应用积极的、硬件特定的优化来最大化NVIDIA GPU的吞吐量，**Zhan等人[99]**利用它在Jetson Orin NX上实现了实时遥感检测。苹果的**CoreML**[100]在其生态系统中扮演类似角色，为苹果设备的CPU、GPU和神经引擎优化模型。

3.  **中间表示与编译框架：** 第三类专注于跨不同硬件后端的模型可移植性和优化。像**ONNX Runtime**这样的框架为模型表示提供了一种标准化格式，而像**Apache TVM**这样的编译器可以为各种目标（从CPU和GPU到更专门的加速器）自动优化模型。这种方法增强了灵活性，但可能需要更复杂的集成。

在这些类别中，有几种关键的优化技术被普遍采用（表4.1）。除了在强大的单板计算机上部署外，研究也在推动使用专门的库如**CMSIS-NN**向超低功耗微控制器（MCU）发展，从而在STM32或Arduino等平台上实现简单的推理。

<br>

**表 4.1.** 边缘部署常用模型优化技术概述。

| 优化技术 | 核心原则 | 主要优点 | 潜在缺点 |
| :--- | :--- | :--- | :--- |
| **量化** | 降低模型权重和激活值的位精度（例如，FP32 → INT8）。 | 显著减小模型大小和延迟。 | 可能有轻微的精度下降。 |
| **剪枝** | 移除冗余（低幅值）的权重或网络结构。 | 减小模型大小和计算复杂性。 | 实现可能复杂；可能损害精度。 |
| **知识蒸馏** | 一个大型"教师"模型训练一个较小的"学生"模型。 | 将知识转移到一个紧凑的模型。 | 需要强大的教师模型和额外的训练。 |
| **硬件感知NAS** | 为特定硬件自动搜索最优网络架构。 | 发现高效、平台特定的模型。 | 搜索过程计算成本非常高。 |

<br>

选择部署框架需要在项目的具体需求（关于平台支持、性能要求和开发资源）之间进行权衡，如表4.2所述。

<br>

**表 4.2.** 代表性边缘AI部署工具链比较。

| 工具链 | 开发者/来源 | 主要生态系统 | 关键特征 | 目标平台 |
| :--- | :--- | :--- | :--- | :--- |
| **TensorFlow Lite** | 谷歌 | TensorFlow | 成熟、通用、广泛的平台支持。 | 安卓、iOS、Linux (包括树莓派)、MCU |
| **PyTorch Mobile** | Meta AI | PyTorch | 从研究到移动部署的无缝过渡。 | 安卓、iOS、Linux |
| **TensorRT** | NVIDIA | CUDA / NVIDIA | 针对NVIDIA GPU的高性能推理优化。 | NVIDIA GPU (Jetson, Tesla等) |
| **CoreML** | 苹果 | 苹果 | 紧密集成以在苹果硬件上实现最佳性能。 | iOS、macOS、watchOS |
| **OpenVINO** | 英特尔 | 英特尔 | 为英特尔CPU、iGPU和VPU优化推理。 | 英特尔硬件 |
| **ONNX Runtime** | 微软 | 不可知 | 标准模型格式的跨平台引擎。 | 跨平台 (CPU, GPU) |
| **Apache TVM** | Apache | 不可知 | 将模型编译为针对不同硬件的优化代码。 | 跨平台 (CPU, GPU, 加速器) |

### 4.2. 从检测到决策：实现机器人裂缝驱动的行为

一旦模型被部署，其输出必须转化为机器人的行动。本节通过探讨构成感知到行动循环的架构设计和控制策略来弥合这一差距。

#### 4.2.1. 架构设计：模块化与紧耦合系统
第一个设计决策是架构性的：感知、规划和控制应该耦合得多紧？这种在模块化和响应性之间的权衡决定了系统的灵活性、延迟和容错能力。

松散耦合的系统，通常建立在诸如机器人操作系统（ROS）之类的中间件之上，将感知、建图、规划和驱动分为通过消息传递接口进行通信的独立节点[101]。这促进了模块化和开发的灵活性。例如，一个裂缝检测模块可以独立于运动规划器运行，只需将检测结果发布到一个共享的话题。虽然这种架构适用于常规勘测任务或离线数据收集，但它会引入延迟，并且在动态、时间关键的场景中效果较差。

紧耦合系统更深入地集成了感知和控制，通常通过共享状态表示或统一的优化框架。一个典型的例子是视觉-惯性里程计（VIO），其中惯性测量值与图像特征以高频率融合，以实时估计精确的机器人姿态[102]。当裂缝检测直接影响运动时——例如，当机器人必须在发现缺陷后立即调整其路径时——这种低延迟耦合至关重要。

在裂缝检测中，松散耦合的架构通常足以满足常规的路径跟踪、记录和离线分析。然而，当感知输出必须动态触发原位行为调整时——例如停止、减速或重新朝向缺陷——紧耦合系统就变得至关重要。

#### 4.2.2. 闭环：从视觉检测到运动行为

感知与行动的整合体现在一个自主行为的光谱中，可以根据反馈循环的深度分为三级分类。

最基本的级别是**I型：开环系统**，其主要功能是作为移动数据记录器。在这种范式下，机器人的感知算法在任务期间不影响其运动。机器人执行预编程的轨迹，例如"割草机"模式，以确保系统性的覆盖。**Kamangir等人[103]**的路面检测UGV和**La Hoz等人[104]**的ROADS漫游车就是这种方法的例证，它们在一个预定义的网格上导航以收集图像供以后分析，最终生成一个"严重性地图"。这种系统的设计优先考虑简单性和稳健性，但这是以牺牲效率为代价的，因为机器人无法动态调整其路径，在完好和损坏的表面上花费同样的时间。

下一个自主级别是**II型：反应式系统**，它在感知和行动之间建立了一个直接的、基于规则的联系。这些系统实现了一个"感知-行动"循环，其中特定的检测会触发一个硬编码的行为，通常使用**行为树（BTs）**来保证模块化和透明度[105]。一个典型的例子是**Zhong等人[114]**的矿井检测机器人，其控制逻辑在检测到裂缝时明确地从"区域搜索"模式切换到"裂缝跟踪"模式，裂缝的几何形状决定了后续的路径。这种反应式设计对于详细的表征非常高效，但其僵化性限制了它，因为预编程的"如果-那么"规则可能无法很好地适应不可预见的情况。

最高级的级别是**III型：审议式系统**，它表现出目标驱动的重新规划。在这里，感知输出会更新一个世界模型或高级任务目标，导致对机器人计划的完全重新评估。这种动态决策由先进的方法论实现。**模型预测控制（MPC）**允许机器人根据感知到的缺陷主动优化其路径，同时遵守动态约束，生成平滑、预见性的轨迹[106, 107]。**强化学习（RL）**提供了一种无模型的替代方案，代理通过试错学习最优的行动策略，从而实现难以手工设计的复杂自适应行为[108, 109]。此外，审议式系统必须可靠。这通过**容错控制**来实现，它利用感知来检测异常并触发恢复策略以维持任务连续性[110, 111]，以及通过**语义感知SLAM**，其中机器人对环境的理解被赋予了对象意义（例如，"支撑柱"与"护栏"），从而允许更智能、具有上下文感知的任务规划和重新规划[112, 113]。虽然这代表了真正的任务级自主，但其高技术复杂性和验证安全的挑战仍然是重大的障碍。

这个层次结构总结在表4.3中。

<br>

**表 4.3.** 按自主级别对机器人检测系统进行的比较分类。

| 特征 | I型：开环系统 | II型：反应式系统 | III型：审议式系统 |
| :--- | :--- | :--- | :--- |
| **核心理念** | 机器人作为移动数据记录器。 | 机器人作为特征跟踪代理。 | 机器人作为自主任务管理者。 |
| **感知-行动联系** | **无（离线分析）。** 感知不影响运动。 | **直接且基于规则。** 检测触发一个特定的、预编程的动作。 | **间接且基于目标。** 感知更新世界模型或任务目标，导致重新规划。 |
| **使能技术** | 预设路径、GPS/里程计 | 状态机、行为树[105] | MPC[107]、RL[109]、语义SLAM[112] |
| **行为示例** | 按网格模式扫描桥面，稍后生成裂缝图。 | 看到一条裂缝，立即沿着其路径进行完整扫描。 | 看到密集的裂缝群，决定放弃当前勘测，去调查新区域。 |
| **主要局限性** | 效率低；无实时适应。 | 僵化；难以处理规则未覆盖的情况。 | 复杂性高；安全验证具有挑战性。 |
| **代表性参考文献** | [103], [104] | [114, 105] | [107, 109, 112] |

### 4.3. 经验教训与部署关键原则

综合从算法到现场部署机器人的历程，四个关键原则对于成功开发下一代机器人检测系统至关重要。

1.  **软硬件协同设计不可或缺。** 一个有效的系统不能孤立地设计。感知算法的选择受到硬件限制（功率、计算能力）的约束，而硬件选择必须以算法需求（传感器类型、数据带宽）为依据。先进的方法甚至使用硬件特性（例如通过Roofline分析）来指导神经网络架构本身的设计[99]。
2.  **从开环到闭环的智能。** 最大的价值在于系统从被动数据收集转向创建闭环的感知-行动循环。这使得机器人能够智能地行动——适应其路径，对不可预见的事件做出反应，并通过动态反馈确保任务的安全和成功[110, 111, 109]。
3.  **通过以数据为中心的策略弥合仿真到现实的差距。** 机器人技术中一个持续的挑战是，在仿真或干净数据集上训练的模型在非结构化的现实世界中常常会失败。克服这种"仿真到现实"的差距需要一种以数据为中心的方法：创建多样化、有代表性的数据集[115, 116]，采用稳健的数据增强，并利用现代仿真平台在部署前在广泛的条件下测试算法[117, 118]。
4.  **拥抱整体、系统级的思维。** 机器人检测的进步需要从以指标为中心的评估转向系统级优化，其中感知、规划、驱动和计算基础设施被设计为一个有机的整体。集成系统的整体可靠性和性能最终决定了其在现实世界中的效用。前沿正在向联合优化感知和控制的**端到端学习架构**发展，而**语义场景理解**的集成[112]对于做出更智能、更安全的决策至关重要。

## 第五章 讨论：持续的挑战与未来的范式

基于前几章所综述的先进方法和系统级进展，本节反思了剩余的瓶颈及其对未来进展的影响。第二至第四章详述的重大进展展示了向自主机器人检测系统发展的清晰轨迹。从多功能移动平台和复杂传感器，到先进的感知算法和集成系统架构，自动化结构评估的基础组件正在迅速成熟。然而，尽管取得了这些进步，从受控的实验室演示到稳健、可扩展且商业上可行的现场部署的过渡，仍然受到几个持续且相互关联的挑战的阻碍。承认这些障碍对于有效引导未来的研究至关重要。

本章首先剖析了该领域面临的最关键挑战，涵盖算法可靠性、软硬件协同设计和系统级集成。随后，它概述了直接解决这些问题的有前途的未来研究范式，这些范式有潜力克服现有局限性，并为下一代真正智能和协作的检测机器人铺平道路。

### 5.1. 在实现可扩展和可泛化的机器人检测中持续存在的挑战

虽然文献中展示了许多成功的原型，但它们的操作范围通常局限于特定的、定义明确的条件。更广泛的应用受到鲁棒性、效率、集成复杂性和可信赖性等基本问题的阻碍。

**5.1.1. 在非受控环境中的鲁棒性与泛化能力**

一个主要的挑战是感知算法在面对多变的真实世界环境时的鲁棒性有限。正如第三章所讨论的，由于领域漂移——训练数据与操作环境之间的差异——深度学习模型的性能可能会显著下降。不一致的光照、多样的表面纹理（例如，混凝土、沥青、腐蚀的金属）以及混淆特征（例如，污渍、锈迹、密封剂痕迹）等因素很容易导致误报或漏检。像矿井检测器[114]这样的专用系统的特性强调了这一点：其成功与为单一、具挑战性环境的定制设计密切相关，突显了实现通用、"全天候"检测解决方案的差距。

**5.1.2. 轻量化设计与算法准确性之间的权衡**

如第3.2.3节和第4.1节所述，在资源受限的机器人平台上部署复杂的深度学习模型需要积极的优化。像量化（例如，INT8精度）和模型剪枝等技术对于在Jetson系列等边缘设备上实现实时推理至关重要[99]。然而，这种优化可能会损害模型对细粒度视觉线索（如初期或低对比度裂缝）的敏感性。因此，在紧凑性与准确性之间取得平衡仍然是一个核心困境，因为最佳策略深度依赖于目标硬件及其特定的传感器套件。

**5.1.3. 系统集成与异构数据融合的复杂性**

开发一个功能性的检测机器人是一项多学科的工程挑战。正如第四章的系统架构所示，感知、规划和控制模块的紧密集成至关重要。有效地融合来自异构传感器的数据（例如，同步高分辨率RGB图像与稀疏的激光雷达点云和IMU读数）仍然是一项不平凡的任务。这种复杂性意味着许多系统仍然主要依赖于单一主传感器，使得多模态传感的全部潜力远未被开发，从而阻碍了更稳健、容错的自主行为的发展。

**5.1.4. 决策缺乏可解释性与信任度**

由于检测结果为高风险的维护决策提供信息，许多深度学习模型的"黑箱"性质构成了应用的一个重大障碍。如果没有对检测结果的可解释性论证，人类检查员可能难以信任系统的输出，尤其是在模棱两可或安全关键的场景中。这突显了对可解释AI（XAI）技术的需求，以培养用户信任并确保系统行为的可追溯性。

### 5.2. 智能与协作检测机器人的未来范式

从已识别的挑战中，一个向更整体、自适应和可信赖的系统加速发展的轨迹浮现出来。以下未来范式直接解决了上述瓶颈，它们不仅代表了增量改进，而且是机器人检测系统构思和开发方式的根本性转变。

<br>
<div align="center">
<pre class="mermaid">
graph TD
    subgraph "A. 传统检测流程"
        A1[1. 人工数据采集] --> A2[2. 离线数据分析];
        A2 --> A3[3. 静态报告生成];
    end

    subgraph "B. 未来智能与协作流程"
        B1[1. 多智能体自主数据采集<br>(例如, 无人机-地面无人车团队)] --> B2(2. 边云协同推理);
        B2 -- 实时检测 --> B3{3. 人在环路验证<br>(通过XAI接口)};
        B3 -- 已验证异常 --> B4[4. 动态3D数字孪生更新];
        B4 --> B5[5. 预测性维护规划];
    end
    
    style A1 fill:#f8cecc,stroke:#b85450,stroke-width:1.5px
    style A2 fill:#f8cecc,stroke:#b85450,stroke-width:1.5px
    style A3 fill:#f8cecc,stroke:#b85450,stroke-width:1.5px

    style B1 fill:#cde4ff,stroke:#6699ff,stroke-width:2px
    style B2 fill:#cde4ff,stroke:#6699ff,stroke-width:2px
    style B3 fill:#cde4ff,stroke:#6699ff,stroke-width:2px
    style B4 fill:#cde4ff,stroke:#6699ff,stroke-width:2px
    style B5 fill:#cde4ff,stroke:#6699ff,stroke-width:2px
</pre>
<br>
<b>图 5.1.</b> 传统与未来检测工作流程的比较示意图，展示了从线性、离线过程到动态、协作和智能生态系统的转变。
</div>
<br>

表5.1高度概括了这些新兴解决方案如何对应于持续存在的挑战。

<br>

**5.2.1. 迈向统一的基础检测模型**

为了解决5.1.1中讨论的鲁棒性和泛化问题，采用大规模、预训练的基础模型提供了一个有前景的解决方案。虽然这些模型带来了巨大的希望，但将其应用于缺陷检测也引入了独特的挑战。通用网络规模数据与特定工程材料之间的领域不匹配、用于微调的缺陷特定标签稀缺，以及边缘部署的限制都构成了重大障碍。然而，这些限制也为创新的适应策略创造了机会。新兴的视觉-语言模型（VLMs）和其他自监督学习器获得了丰富的视觉理解能力，只需最少的微调即可用于裂缝检测。这种范式促成了一种"大模型-小模型"的协作架构，其基础是**知识蒸馏**等技术，其中基础模型充当"教师"，指导轻量级"学生"模型的训练。此外，**边云协同推理**策略可以通过在连接允许时将较重的分析任务动态卸载到远程服务器，来平衡实时响应和复杂性，直接解决了5.1.2中提到的权衡问题。

**5.2.2. 以数据为中心的AI：自动化与人在环路的策管**

鉴于模型性能严重依赖于数据，因此应更加关注以数据为中心的AI。未来的研究不应仅仅追求新颖的模型架构，还必须投资于自动化数据生成和策管的技术。虽然先进的生成模型（如GANs、扩散模型）和高保真模拟器可以创建多样化、逼真的训练数据，但确保领域相关性仍然是一个关键挑战。这正是**人在环路标注**系统变得至关重要的地方。通过使用主动学习算法智能地选择信息量最大的未标记数据供人类专家审查，这些系统最大化了有限专家时间的价值，并加速了稳健的、领域特定的数据集的创建。这种半自动化的方法不仅提高了模型的鲁棒性，还通过将专家知识直接嵌入到训练循环中来增强可信赖性。

**5.2.3. 用于集成导航与检测的多任务学习**

为了克服5.1.3中强调的集成瓶颈，从孤立的模块转向统一的、多任务学习模型至关重要。尽管在概念上很有吸引力，但目前的集成方法在不断变化的操作环境中仍然很脆弱。一个单一的、端到端的模型可以被训练来同时执行定位、建图和缺陷分析。这些任务通常是协同的；例如，来自SLAM流程的几何特征可以为识别结构元素提供强大的上下文先验。这种集成模型不仅计算效率更高，而且还可能在复杂、GPS信号受限的环境中带来增强的性能和可靠性。尽管如此，设计这样的统一架构需要仔细的任务平衡，因为冲突的目标（例如，精确导航与缺陷敏感性）如果处理不当，可能会导致次优的收敛。

**5.2.4. 用于大规模检测的协作式多智能体系统**

最后，对于检查广阔而复杂的基础设施，未来在于协作式的多智能体系统。如果不从单机器人解决方案向协调的、异构的团队进行根本性转变，可扩展性仍然难以实现。这一愿景涉及多种平台协同工作；例如，一群无人机可以提供快速的空中勘测，引导地面UGV进行详细的、近距离的检查。实现这一愿景需要克服在分散协调、稳健的智能体间通信以及实时融合来自多个移动源的异构传感器数据方面的巨大研究挑战。

这些未来系统中一个至关重要的、常被忽视的方面是**更深层次的跨学科合作**的必要性。一个有效的检测机器人的设计需要将机器人学和AI的专业知识与土木工程、材料科学和人机交互（以设计可用且可信赖的界面）的见解相结合。将这些人为因素考虑在内，是将技术潜力转化为已部署现实的关键。

总之，实现可部署的、智能的机器人检测不仅需要技术突破，还需要对AI系统如何与不确定环境、人类操作员以及彼此之间互动进行整体性的重新思考。随着该领域向前发展，研究必须从任务特定的优化过渡到开发能够适应、解释并在真实世界的安全关键部署中被信任的通用框架。

## 第六章 结论

在对可扩展、安全和自动化检测系统日益增长的需求的推动下，本综述系统地描绘了机器人裂缝检测不断演变的格局。我们的工作旨在对这些进展进行全面和批判性的综合，以指导未来的研究朝着开发真正实用和可扩展的解决方案的方向发展。

本综述的主要贡献是一种新颖的系统级综合，它将感知算法设计与机器人硬件约束和部署考虑联系起来，为整个视觉检测流程的协同优化提供了可操作的指导。这使得我们的综述区别于以往那些要么以算法为中心，要么狭隘地关注硬件平台而未解决它们之间相互依赖性的工作。通过面向部署的视角，本综述弥合了计算机视觉、机器人学和土木工程这些通常孤立的领域，为理解多方面的挑战和机遇提供了一个统一的框架。我们认为，未来的突破更可能来自于整体的协同设计理念，即紧密耦合感知、规划、控制和硬件约束，而不是孤立的组件级优化。

最终，本综述既是对最先进技术的全面总结，也是一个战略路线图。它阐明了宏大的挑战不再仅仅是在实验室中以更高的准确性检测裂缝，而是构建能够在我们老化的基础设施的复杂、非受控环境中自主运行的稳健、高效和可信赖的机器人系统。虽然本综述提供了全面的综合，但值得注意的是，AI和机器人技术的快速发展可能很快会使某些趋势过时。因此，持续重新评估新兴范式对于保持该领域的相关性和实用价值至关重要。

展望未来，我们认为有两个战略方向对于加速从实验室到现场的过渡尤为重要。

首先，迫切需要公开的、大规模的基准数据集和标准化的评估协议。这些基准必须反映真实世界检测场景的复杂性，包括不同的光照条件、遮挡、材料纹理和环境干扰。开发这样的标准化基准不仅仅是一项学术活动，而是推动有意义的、可复现的进展并实现不同方法之间公平比较的先决条件。

其次，将视觉感知与稳健的自主规划和控制更紧密地集成是至关重要的。未来的系统必须从简单的检测超越到"主动视觉"，即感知直接影响机器人的下一个动作。这需要在实时不确定性建模方面取得进展，以量化模型置信度，并需要允许无缝操作员纠正的人在环路框架，以确保在动态现场条件下的安全性和可信赖性。


## 参考文献：
[1]Ahmed, Habib, Hung Manh La, and Nenad Gucunski. "Review of non-destructive civil infrastructure evaluation for bridges: State-of-the-art robotic platforms, sensors and algorithms." Sensors 20.14 (2020): 3954.
[2]Aitken, Jonathan M., et al. "Simultaneous localization and mapping for inspection robots in water and sewer pipe networks: A review." IEEE access 9 (2021): 140173-140198.
[3]Ahmed, Habib, Chuong Phuoc Le, and Hung Manh La. "Pixel-level classification for bridge deck rebar detection and localization using multi-stage deep encoder-decoder network." Developments in the Built Environment 14 (2023): 100132.
[4]Mohan, Arun, and Sumathi Poobal. "Crack detection using image processing: A critical review and analysis." alexandria engineering journal 57.2 (2018): 787-798.
[5]Prasanna, Prateek, et al. "Automated crack detection on concrete bridges." IEEE Transactions on automation science and engineering 13.2 (2014): 591-599.
[6]Alejo, David, et al. "SIAR: A ground robot solution for semi-autonomous inspection of visitable sewers." Advances in Robotics Research: From Lab to Market: ECHORD++: Robotic Science Supporting Innovation (2020): 275-296.
[7]Jung, Sungwook, et al. "Mechanism and system design of MAV (Micro Aerial Vehicle)-type wall-climbing robot for inspection of wind blades and non-flat surfaces." 2015 15th international conference on control, automation and systems (ICCAS). IEEE, 2015.
[8]Kanellakis, Christoforos, et al. "Towards visual inspection of wind turbines: A case of visual data acquisition using autonomous aerial robots." IEEE access 8 (2020): 181650-181661.
[9]Myung, Hyun, and Yang Wang. "Robotic sensing and systems for smart cities." Sensors 21.9 (2021): 2963.
[10]Merkle, Dominik, and Alexander Reiterer. "Automated Method for SLAM Evaluation in GNSS-Denied Areas." Remote Sensing 15.21 (2023): 5141.
[11]Shen, Yaoyang, et al. "A Texture-Based Simulation Framework for Pose Estimation." Applied Sciences 15.8 (2025): 4574.
[12]Burri, Michael, et al. "Aerial service robots for visual inspection of thermal power plant boiler systems." 2012 2nd international conference on applied robotics for the power industry (CARPI). IEEE, 2012.
[13]Jung, Sungwook, et al. "Bridge inspection using unmanned aerial vehicle based on HG-SLAM: Hierarchical graph-based SLAM." Remote Sensing 12.18 (2020): 3022.
[14]Wang, Xiaohui, Xi Ma, and Zhaowei Li. "Research on SLAM and path planning method of inspection robot in complex scenarios." Electronics 12.10 (2023): 2178.
[15]Ge, Liangfu, and Ayan Sadhu. "Deep learning-enhanced smart ground robotic system for automated structural damage inspection and mapping." Automation in Construction 170 (2025): 105951.
[16]Bui, Hoang-Dung, et al. "Control framework for a hybrid-steel bridge inspection robot." 2020 IEEE/RSJ International Conference on Intelligent Robots and Systems (IROS). IEEE, 2020.
[17]S. T. Nguyen, H. M. La, A Climbing Robot for Steel Bridge Inspection. Journal of Intelligent & Robotic Systems 102, 75 (2021).
[18]Gucunski, Nenad, et al. "Implementation of a fully autonomous platform for assessment of concrete bridge decks RABIT." Structures Congress 2015. 2015.
[19]La, Hung Manh, et al. "Autonomous robotic system for bridge deck data collection and analysis." 2014 IEEE/RSJ International Conference on Intelligent Robots and Systems. IEEE, 2014.
[20]Gibb, Spencer, et al. "A multi-functional inspection robot for civil infrastructure evaluation and maintenance." 2017 IEEE/RSJ international conference on intelligent robots and systems (IROS). IEEE, 2017.
[21]Zhang, Kong, et al. "Inspection of floating offshore wind turbines using multi-rotor unmanned aerial vehicles: literature review and trends." Sensors 24.3 (2024): 911.
[22]Lim, Ronny Salim, et al. "Developing a crack inspection robot for bridge maintenance." 2011 IEEE International Conference on Robotics and Automation. IEEE, 2011.
[23]La, Hung M., et al. "Mechatronic systems design for an autonomous robotic system for high-efficiency bridge deck inspection and evaluation." IEEE/ASME transactions on mechatronics 18.6 (2013): 1655-1664.
[24]Pfändler, Patrick, et al. "Non-destructive corrosion inspection of reinforced concrete structures using an autonomous flying robot." Automation in Construction 158 (2024): 105241.
[25]Lile, Cai, and Li Yiqun. "Anomaly detection in thermal images using deep neural networks." 2017 IEEE International conference on image processing (ICIP). IEEE, 2017.
[26]Menendez, Elisabeth, et al. "Tunnel structural inspection and assessment using an autonomous robotic system." Automation in Construction 87 (2018): 117-126.
[27]Shan, Tixiao, et al. "Lio-sam: Tightly-coupled lidar inertial odometry via smoothing and mapping." 2020 IEEE/RSJ international conference on intelligent robots and systems (IROS). IEEE, 2020.
[28]Xu, Wei, and Fu Zhang. "Fast-lio: A fast, robust lidar-inertial odometry package by tightly-coupled iterated kalman filter." IEEE Robotics and Automation Letters 6.2 (2021): 3317-3324.
[29]Jung, Sungwook, et al. "Multi-layer coverage path planner for autonomous structural inspection of high-rise structures." 2018 IEEE/RSJ International Conference on Intelligent Robots and Systems (IROS). IEEE, 2018.
[30]Xia, Zhe, et al. "Complete‐coverage path planning for surface inspection of cable‐stayed bridge tower based on building information models and climbing robots." Computer‐Aided Civil and Infrastructure Engineering (2025).
[31]La, Hung M., et al. "Autonomous robotic system for high-efficiency non-destructive bridge deck inspection and evaluation." 2013 IEEE International Conference on Automation Science and Engineering (CASE). IEEE, 2013.
[32]Kim, Yeeun, et al. "Step: State estimator for legged robots using a preintegrated foot velocity factor." IEEE Robotics and Automation Letters 7.2 (2022): 4456-4463.
[33]Bellicoso, C. Dario, et al. "Advances in real-world applications for legged robots." Journal of Field Robotics 35.8 (2018): 1311-1326.
[34]Lee, Hyungyu, et al. "CAROS-Q: climbing aerial robot system adopting rotor offset with a quasi-decoupling controller." IEEE Robotics and Automation Letters 6.4 (2021): 8490-8497.
[35]Myeong, Wancheol, and Hyun Myung. "Development of a wall-climbing drone capable of vertical soft landing using a tilt-rotor mechanism." IEEE Access 7 (2018): 4868-4879.
[36]Myeong, Wan Cheol, et al. "Drone-type wall-climbing robot platform for structural health monitoring." Proc. Int. Conf. Advances in Experimental Structural Engineering. 2015.
[37]Myeong, Wan Cheol, et al. "Development of a drone-type wall-sticking and climbing robot." 2015 12th international conference on ubiquitous robots and ambient intelligence (URAI). IEEE, 2015.
[38]Choset, Howie. Bridge inspection with serpentine robots. Transportation Research Board, IDEA Program, 2002.
[39]Pasetto, Alberto, Yash Vyas, and Silvio Cocuzza. "Zero reaction torque trajectory tracking of an aerial manipulator through Extended Generalized Jacobian." Applied Sciences 12.23 (2022): 12254.
[40]Shu, Jiangpeng, Zhe Xia, and Yifan Gao. "BIM-Based Trajectory Planning for Unmanned Aerial Vehicle-Enabled Box Girder Bridge Inspection." Remote Sensing 17.4 (2025).
[41]Bristeau, Pierre-Jean, et al. "The navigation and control technology inside the ar. drone micro uav." IFAC Proceedings Volumes 44.1 (2011): 1477-1484.
[42]Salaan, Carl John O., et al. "Close visual bridge inspection using a UAV with a passive rotating spherical shell." Journal of Field Robotics 35.6 (2018): 850-867.s
[43]Muthugala, MA Viraj J., et al. "Raptor: a design of a drain inspection robot." Sensors 21.17 (2021): 5742.
[44]Ma, Ke, et al. "Robot mapping and localisation for feature sparse water pipes using voids as landmarks." Towards Autonomous Robotic Systems: 16th Annual Conference, TAROS 2015, Liverpool, UK, September 8-10, 2015, Proceedings 16. Springer International Publishing, 2015.
[45]Menendez, Elisabeth, et al. "Tunnel structural inspection and assessment using an autonomous robotic system." Automation in Construction 87 (2018): 117-126.
[46]Kim, Youngseok, and Jaesuk Ryou. "A study of sonar image stabilization of unmanned surface vehicle based on motion sensor for inspection of underwater infrastructure." Remote Sensing 12.21 (2020): 3481.
[47]Lu, Bing-Xian, Yu-Chung Tsai, and Kuo-Shih Tseng. "GRVINS: tightly coupled GNSS-range-visual-inertial system." Journal of Intelligent & Robotic Systems 110.1 (2024): 36.
[48]Lee, Alex Junho, et al. "Vivid++: Vision for visibility dataset." IEEE Robotics and Automation Letters 7.3 (2022): 6282-6289.
[49]Tang, Chongrui, et al. "Lay-up defects inspection for automated fiber placement with structural light scanning and deep learning." Polymer Composites (2025).
[50]Mineo, Carmelo, et al. "Fine alignment of thermographic images for robotic inspection of parts with complex geometries." Sensors 22.16 (2022): 6267.
[51]Tian, Wei, et al. "Acoustic Signal-Based Deep Learning Approach and Device for Detecting Interfacial Voids in Steel–Concrete Composite Structures." Advances in Civil Engineering 2025.1 (2025): 2347213.
[52]Luo, Kui, et al. "Computer vision-based bridge inspection and monitoring: A review." Sensors 23.18 (2023): 7863.
[53]Jiang, Yandan, et al. "Tunnel lining detection and retrofitting." Automation in Construction 152 (2023): 104881.
[54]Chen, Dong, Ben Huang, and Fei Kang. "A review of detection technologies for underwater cracks on concrete dam surfaces." Applied Sciences 13.6 (2023): 3564.
[55]Hussain, Muhammad, et al. "Review of prediction of stress corrosion cracking in gas pipelines using machine learning." Machines 12.1 (2024): 42.
[56]Wang, Hao, et al. "A comprehensive review of polyethylene pipes: Failure mechanisms, performance models, inspection methods, and repair solutions." Journal of Pipeline Science and Engineering 4.2 (2024): 100174.
[57]Ding, Shaohu, Chenchen Yang, and Sen Zhang. "Acoustic-signal-based damage detection of wind turbine blades—A review." Sensors 23.11 (2023): 4987.
[58]Javidrad, Hamidreza, et al. "Fatigue performance of metal additive manufacturing: A comprehensive overview." Virtual and Physical Prototyping 19.1 (2024): e2302556.
[59]Tonga, Danladi Agadi, et al. "Nondestructive evaluation of fiber-reinforced polymer using microwave techniques: A review." Coatings 13.3 (2023): 590.
[60]Yap, Rei Chiel, Meshal Alghanem, and Nicolas Martin. "A narrative review of cracks in teeth: Aetiology, microstructure and diagnostic challenges." Journal of Dentistry 138 (2023): 104683.
[61]Qi, Jinjin, and Zhen Li. "Non-destructive testing of human teeth using microwaves: a state-of-the-art review." Journal of Electrical Engineering 74.1 (2023): 40-47.
[62]Cha, Young-Jin, et al. "Deep learning-based structural health monitoring." Automation in Construction 161 (2024): 105328.
[63]Jia, Jing, and Ying Li. "Deep learning for structural health monitoring: Data, algorithms, applications, challenges, and trends." Sensors 23.21 (2023): 8824.
[64]Spencer Jr, Billie F., et al. "Advances in artificial intelligence for structural health monitoring: A comprehensive review." KSCE Journal of Civil Engineering 29.3 (2025): 100203.
[65]Zhang, Yishuang, Cheuk Lun Chow, and Denvid Lau. "Artificial intelligence-enhanced non-destructive defect detection for civil infrastructure." Automation in Construction 171 (2025): 105996.
[66]Azouz, Zakrya, Barmak Honarvar Shakibaei Asli, and Muhammad Khan. "Evolution of crack analysis in structures using image processing technique: A review." Electronics 12.18 (2023): 3862.
[67]Peng, Zhangjun, et al. "A Comprehensive Survey on Visual Perception Methods for Intelligent Inspection of High Dam Hubs." Sensors 24.16 (2024): 5246.
[68]Shim, Seungbo, et al. "Road damage detection using super-resolution and semi-supervised learning with generative adversarial network." Automation in construction 135 (2022): 104139.
[69]Alsuhaibani, Eyad. "Nondestructive Testing of Externally Bonded FRP Concrete Structures: A Comprehensive Review." Polymers 17.9 (2025): 1284.
[70]Aromoye, Ibrahim Akinjobi, et al. "Significant Advancements in UAV Technology for Reliable Oil and Gas Pipeline Monitoring." Computer Modeling in Engineering & Sciences (CMES) 142.2 (2025).
[71]Wan, Fang, et al. "YOLO-LRDD: A lightweight method for road damage detection based on improved YOLOv5s." EURASIP Journal on Advances in Signal Processing 2022.1 (2022): 98.
[72]Liu, Zhiyong, et al. "EMB-YOLO: Dataset, method and benchmark for electric meter box defect detection." Journal of King Saud University-Computer and Information Sciences 36.2 (2024): 101936.
[73]Hao, Shuai, et al. "Transmission Line Defect Target-Detection Method Based on GR-YOLOv8." Sensors 24.21 (2024): 6838.
[74]Giannuzzi, Valeria, and Fabio Fatiguso. "Historic Built Environment Assessment and Management by Deep Learning Techniques: A Scoping Review." Applied Sciences (2076-3417) 14.16 (2024).
[75]Iqbal, Umair, et al. "The last two decades of computer vision technologies in water resource management: A bibliometric analysis." Water and Environment Journal 37.3 (2023): 373-389.
[76]Qiao, Qi, et al. "A Review of Metal Surface Defect Detection Technologies in Industrial Applications." IEEE Access (2025).
[77]Wang, Junpu, et al. "Defect transformer: An efficient hybrid transformer architecture for surface defect detection." Measurement 211 (2023): 112614.
[78]Zhang, Shihai, et al. "Multi-objects recognition and self-explosion defect detection method for insulators based on lightweight GhostNet-YOLOV4 model deployed onboard UAV." IEEE Access 11 (2023): 39713-39725.
[79]Lu, Yang, et al. "A Lightweight Insulator Defect Detection Model Based on Drone Images." Drones 8.9 (2024): 431.
[80]Hou, Yue, et al. "MobileCrack: Object classification in asphalt pavements using an adaptive lightweight deep learning." Journal of Transportation Engineering, Part B: Pavements 147.1 (2021): 04020092.
[81]Zhang, Ye, et al. "An Improved Target Network Model for Rail Surface Defect Detection." Applied Sciences (2076-3417) 14.15 (2024).
[82]Song, Fei, Ying Sun, and Guixia Yuan. "Autonomous identification of bridge concrete cracks using unmanned aircraft images and improved lightweight deep convolutional networks." Structural Control and Health Monitoring 2024.1 (2024): 7857012.
[83]Pei, Shaotong, et al. "Lightweight transmission line defect identification method based on OFN network and distillation method." IET Image Processing 18.12 (2024): 3518-3529.
[84]Liu, Yanxing, et al. "Lightweight Insulator and Defect Detection Method Based on Improved YOLOv8." Applied Sciences 14.19 (2024): 8691.
[85]Yu, Jiangli, et al. "Lcg-yolo: A real-time surface defect detection method for metal components." IEEE Access 12 (2024): 41436-41451.
[86]Jiang, Tingyao, Xuan Hou, and Min Wang. "Insulator defect detection based on the cddcr–yolov8 algorithm." International Journal of Computational Intelligence Systems 17.1 (2024): 245.
[87]Chen, Weixun, Siming Meng, and Xueping Wang. "Local and Global Context-Enhanced Lightweight CenterNet for PCB Surface Defect Detection." Sensors 24.14 (2024): 4729.
[88]Wen, Runyuan, et al. "LESM-YOLO: An Improved Aircraft Ducts Defect Detection Model." Sensors (Basel, Switzerland) 24.13 (2024): 4331.
[89]Oswald-Tranta, Beate. "Inductive thermography–review of a non-destructive inspection technique for surface crack detection." Quantitative InfraRed Thermography Journal (2025): 1-25.
[90]Qiu, Zhaomei, et al. "DCS-YOLOv5s: A lightweight algorithm for multi-target recognition of potato seed potatoes based on YOLOv5s." Agronomy 14.11 (2024): 2558.
[91]Machado, Miguel A. "Eddy currents probe design for NDT applications: A review." Sensors (Basel, Switzerland) 24.17 (2024): 5819.
[92]Zhang, Ran, et al. "Unmanned aerial vehicle navigation in underground structure inspection: A review." Geological Journal 58.6 (2023): 2454-2472.
[93]Bodenham, Matthew, and Jaeha Kung. "Skipformer: Evolving Beyond Blocks for Extensively Searching On-Device Language Models With Learnable Attention Window." IEEE Access (2024).
[94]Zawish, Muhammad, et al. "Energy-Efficient Uncertainty-Aware Biomass Composition Prediction at the Edge." Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. 2024.
[95]Rizk, Mostafa, and Adel Chehade. "Efficient Oil Tank Detection Using Deep Learning: A Novel Dataset and Deployment on Edge Devices." IEEE Access (2024).
[96]TensorFlow Lite Documentation. Available online: https://www.tensorflow.org/lite (accessed on 8 June 2025).
[97]Memon, Khuhed, et al. "Edge Computing for AI-Based Brain MRI Applications: A Critical Evaluation of Real-Time Classification and Segmentation." Sensors 24.21 (2024): 7091.
[98]PyTorch Mobile Documentation. Available online: https://pytorch.org/mobile/home/<USER>
[99]Zhao, Boya, et al. "A Fast Target Detection Model for Remote Sensing Images Leveraging Roofline Analysis on Edge Computing Devices." IEEE Journal of Selected Topics in Applied Earth Observations and Remote Sensing (2024).
[100]CoreML Documentation. Available online: https://developer.apple.com/documentation/coreml (accessed on 8 June 2025).
[101]Grigorescu, Sorin, and Mihai Zaha. "CyberCortex. AI: An AI-based operating system for autonomous robotics and complex automation." Journal of Field Robotics 42.2 (2025): 474-492.
[102]Guan, Qi, et al. "A dual-mode automatic switching feature points matching algorithm fusing IMU data." Measurement 185 (2021): 110043.
[103]Khan, Md Al-Masrur, et al. "Development of AI-and robotics-assisted automated pavement-crack-evaluation system." Remote Sensing 15.14 (2023): 3573.
[104]Mei, Alessandro, et al. "ROADS—rover for bituminous pavement distress survey: an unmanned ground vehicle (UGV) prototype for pavement distress evaluation." Sensors 22.9 (2022): 3414.
[105]Yang, Shuo, and Qi Zhang. "Towards efficient robotic software development by reusing behavior tree structures for task planning paradigms." Complex System Modeling and Simulation 3.4 (2023): 357-380.
[106]Lindemann, Lars, et al. "Safe planning in dynamic environments using conformal prediction." IEEE Robotics and Automation Letters 8.8 (2023): 5116-5123.
[107]Achirei, Stefan-Daniel, et al. "Model-predictive control for omnidirectional mobile robots in logistic environments based on object detection using CNNs." Sensors 23.11 (2023): 4992.
[108]Lakshmanan, Anirudh Krishna, et al. "Complete coverage path planning using reinforcement learning for tetromino based cleaning and maintenance robot." Automation in Construction 112 (2020): 103078.
[109]Pintos Gómez de las Heras, Borja, Rafael Martínez-Tomás, and José Manuel Cuadra Troncoso. "Self-Learning Robot Autonomous Navigation with Deep Reinforcement Learning Techniques." Applied Sciences 14.1 (2023): 366.
[110]Luo, Shuangqi, et al. "Endowing robots with longer-term autonomy by recovering from external disturbances in manipulation through grounded anomaly classification and recovery policies." Journal of Intelligent & Robotic Systems 101.3 (2021): 51.
[111]Zhou, Xingyu, et al. "Event-Triggered Robust Adaptive Fault-Tolerant Tracking and Vibration Control for the Rigid-Flexible Coupled Robotic Mechanisms With Large Beam-Deformations." IEEE Transactions on Systems, Man, and Cybernetics: Systems (2025).
[112]Zheng, Chen, et al. "Semantic map construction approach for human-robot collaborative manufacturing." Robotics and Computer-Integrated Manufacturing 91 (2025): 102845.
[113]Huang, Xiaotao, et al. "ADM-SLAM: Accurate and Fast Dynamic Visual SLAM with Adaptive Feature Point Extraction, Deeplabv3pro, and Multi-View Geometry." Sensors 24.11 (2024): 3578.
[114]Tang, Chaoquan, et al. "Inspection robot and wall surface detection method for coal mine wind shaft." Applied Sciences 13.9 (2023): 5662.
[115]Wei, Hexiang, et al. "Fusionportablev2: A unified multi-sensor dataset for generalized slam across diverse platforms and scalable environments." The International Journal of Robotics Research (2024): 02783649241303525.
[116]Feng, Dapeng, et al. "S3E: A Multi-Robot Multimodal Dataset for Collaborative SLAM." IEEE Robotics and Automation Letters (2024).
[117]Pérez-Higueras, Noé, et al. "Hunavsim: A ros 2 human navigation simulator for benchmarking human-aware robot navigation." IEEE robotics and automation letters 8.11 (2023): 7130-7137.
[118]Bakirci, Murat. "Simulation of Autonomous Driving for a Line-Following Robotic Vehicle: Determining the Optimal Manoeuvring Mode." Elektronika ir Elektrotechnika 29.6 (2023): 4-11.

</rewritten_file> 