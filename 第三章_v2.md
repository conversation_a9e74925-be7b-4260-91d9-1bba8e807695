## 第三章 智能缺陷感知的算法进展

前两章我们探讨了作为结构评估"眼睛"和"耳朵"的物理机器人平台与传感器系统。本章将深入探讨检测系统的"大脑"：那些能够将原始、充满噪声的传感器数据转化为关于结构完整性的可操作知识的复杂算法。缺陷（尤其是裂缝）的自动检测是结构健康监测（SHM）和无损检测（NDT）领域的基石，其应用横跨从桥梁、隧道等土木基础设施【4, 9, 32】到管道【24, 38】、风力涡轮机叶片【12】等关键能源部件，乃至延伸至先进材料制造【25, 16】和医疗诊断【27, 70】等多个工程领域。及时而准确地识别缺陷，对于保障安全、运营可靠性和经济效益至关重要。

这些检测算法的演进，标志着一场从传统手工特征工程到数据驱动的深度学习（DL）模型的重大范式转移【2, 14】。传统技术虽奠定了基础，但在应对多样化现实世界条件的鲁棒性方面时常捉襟见肘。深度学习的出现彻底改变了该领域，通过直接从数据中学习复杂的缺陷特征，展现出前所未有的性能【35, 65】。本章将通过系统性地分析近期文献中的具体创新，描绘这些算法进展的轨迹。我们将从探讨数据质量这一基础性挑战入手，审视以数据为中心的增强策略；随后，我们将回顾核心深度学习模型的架构演进，并探索注意力机制与多模态数据融合如何被用于增强感知能力；最后，我们将视野拓展到标准的监督式训练之外，讨论那些有望构建更自主、适应性更强的检测系统的新兴学习范式。

### 3.1 以数据为中心的增强策略：提升检测鲁棒性

任何深度学习模型的性能都从根本上受限于其训练数据的质量和多样性。在实际应用中，从现场检测中获取的数据很少是完美的，常常受到各种问题的困扰，这些问题会严重降低算法的性能。因此，一个重要的研究方向不仅仅聚焦于模型架构本身，更关注于以数据为中心的策略来提升数据质量。这些策略主要包括数据预处理、增强和增广。

数据预处理旨在标准化输入，并从复杂的背景中凸显出显著的缺陷特征。这是许多研究中都强调的关键第一步。例如，**Azouz等人【36】** 的综述系统地分类了这些初始步骤，强调了在应用任何检测算法之前，通常会使用图像滤波技术来消除噪声和模糊，并结合图像增强方法来提高对比度和可见性。在此基础上，**Peng等人【94】** 在其关于高坝视觉感知的综述中，特别指出了直方图均衡化（及其自适应变体CLAHE）和基于Retinex的算法是缓解光照变化影响的关键方法。Retinex算法的灵感来源于人类视觉系统在不同光照下感知一致颜色的能力，对于在光照条件差且不一致的大坝隧道等挑战性环境中捕获的图像进行归一化处理尤为有效。

除了这些像素级的调整，更先进的数据中心策略也日益受到关注。这包括使用生成对抗网络（GAN）来合成逼真的缺陷图像以进行数据增广，从而丰富训练集。**文献【101】** 提出了一种基于GAN的超分辨率和半监督学习方法，前者提升路面图像质量，使损伤区域更清晰可见，后者则利用大量未标记图像增强了模型的检测性能，证明了生成模型在扩充高质量训练数据方面的巨大潜力。此外，当处理多模态数据时，精确的传感器校准和时空数据对齐是至关重要的预处理步骤，以确保后续有意义的特征融合【31, 85】。这些增强和增广阶段并非可有可无的流程；它们是确保后续深度学习模型能够有效学习判别性特征的基础，使其能够超越由噪声和环境伪影引起的虚假相关性，真正捕捉到缺陷的内在特征。

*一旦原始数据得到增强和预处理，下一步自然是设计能够在各种条件下进行稳健、准确缺陷识别的模型。*

### 3.2 深度学习检测模型：架构与创新

任何现代自动化检测系统的核心，都是负责识别缺陷的深度学习模型。这些模型的应用沿着一条精度与效率不断提升的清晰轨迹发展，从基于图像块的分类器，演进到端到端的目标检测器，最终发展为像素级的分割网络。

#### 3.2.1 核心架构的光谱：从图像块到像素级

深度学习在缺陷检测中的最初应用，常将问题转化为图像分类任务。在这种模式下，一张大图被分割成若干小块（Patch），然后训练一个卷积神经网络（CNN）来判断每个图像块是否包含缺陷。这种方法虽然是基础，但计算效率低下，且忽略了缺陷的全局上下文信息，不过在标注数据稀缺的少样本或弱监督学习场景中仍有其价值。

为了克服这些局限性，该领域迅速采用了**端到端的目标检测**框架。这些模型一次性处理整张图像，用矩形边界框定位缺陷。其中，以You Only Look Once（YOLO）系列为代表的单阶段检测器，因其在速度和精度之间的卓越平衡而成为主导力量。正如**Aromoye等人[v1_70]** 的研究所强调的，这类深度学习算法在基于无人机的自动化管道巡检中扮演着关键角色，因为实时性能是首要需求。YOLO系列的演进，从早期版本到YOLOv5、YOLOv7和YOLOv8等新版本，反映了工业应用中为优化这一速度-精度权衡所做的持续努力，大量研究针对具体缺陷检测任务对这些框架进行了调整和优化【102, 111】。

然而，对于严谨的工程分析而言，一个粗略的边界框往往是不够的。这一需求推动了**像素级语义分割**的广泛应用，它已成为高精度缺陷分析的标准。这类模型对图像中的每个像素进行分类，生成一个精确勾勒出缺陷几何形状的详细掩码（Mask）。U-Net架构是该领域公认的基石。其应用十分广泛，正如**Giannuzzi等人[v1_74]** 在关于历史建筑环境评估的综述中所展示的，他们记录了U-Net及其变体如何成为从图像数据中分类裂缝等劣化现象的主要工具，并构成了自动化诊断系统的基础。同样，**Khan在其关于水资源管理的综述中[v1_75]** 指出，分割模型是监测和检查水工结构的关键技术，其中精确的裂缝描绘对于评估结构完整性至关重要。**文献【104】** 则提出了一个具体的改进案例，通过引入沙漏形深度可分离卷积改进U-Net模型，成功用于桥梁混凝土裂缝的像素级分割，实现了高精度的缺陷描绘。

#### 3.2.2 Transformer与混合架构的兴起

尽管CNN通过其卷积核擅长提取局部特征，但其固有的局部感受野在模拟连续、曲折的裂缝结构所特有的长程依赖关系时可能成为一个限制。这一架构上的约束为**视觉Transformer（ViT）**的引入铺平了道路。Transformer的核心机制——**自注意力（Self-attention）**，使其能够建模图像所有部分之间的上下文关系，从而更好地感知缺陷的全局连续性。然而，标准ViT在模拟CNN所擅长的细粒度局部纹理方面常常遇到困难。**Qiao等人[v1_76]** 在关于金属表面缺陷检测的综述中提到，基于Transformer的模型（如引入了层级结构和移位窗口以改善局部上下文建模的Swin Transformer）的出现，代表了当前最先进的方法。因此，研究前沿越来越关注于**混合架构**，这种架构能协同发挥两种范式的优势。一个典型的例子是**文献【98】** 提出的Defect Transformer（DefT），它将CNN作为强大的特征编码器，而将Transformer作为具备上下文感知能力的解码器，有效地结合了局部细节捕捉和全局关系建模的能力，从而在表面缺陷检测任务中取得了卓越的性能。随着这些模型变得日益复杂，一个并行的研究趋势是开发轻量化的Transformer（例如MobileViT），旨在将它们的全局推理能力应用于资源受限的场景。

#### 3.2.3 面向实际部署的轻量化模型

对更高精度的追求往往导致模型变得更大、更复杂。然而，对于许多涉及移动或嵌入式系统（如无人机）的真实世界应用而言，计算资源受到严格限制，这使得**模型轻量化与优化**成为一个关键的研究焦点。现有文献提供了一个清晰的策略工具箱来实现这一目标，主要集中在采用高效的主干网络、集成模块化的轻量化设计原则以及利用自动化的架构搜索。

一个主要策略是用专为效率而设计的架构替换标准的主干网络（如ResNet）。例如，通过廉价线性运算生成更多特征图的**GhostNet**已被广泛采用，**文献【99】** 和 **文献【106】** 分别将其应用于YOLOv4和自定义的YOLO模型中，以实现对绝缘子缺陷的轻量化检测。其他流行的轻量化主干网络包括**ShuffleNetV2**【102】和**MobileNet**，后者的变体被用于从轨道扣件检测到裂缝分类的各种任务【100】。

除了选择高效的主干网络，研究人员还专注于通过用更高效的替代品替换标准卷积来进行细粒度的模块化优化，例如**深度可分离卷积**【104, 105】、**Ghost卷积**和**GSConv**【106, 79】。另一项强大的技术是**结构重参数化**，它将一个复杂的训练时模块融合成一个简单、快速的推理时卷积。这一原则在`RepBSB`模块【110】和一系列研究中都有体现【109】。这些具体的、有针对性的优化对于开发可部署在NVIDIA Jetson等边缘平台上、并能达到适合实际现场使用的实时推理速度的模型至关重要。

尽管如此，在模型精度和效率之间取得平衡仍然是一个充满挑战的权衡过程，尤其是在具有动态实时约束的边缘计算场景中。

<br>

**表1：轻量化技术及其应用总结**

| 轻量化策略 |核心原则 | 代表性模型/模块 | 应用案例 | 检测目标 | 部署平台 | 参考文献 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **高效主干网络** | 设计计算高效的基础网络。 | GhostNet | GhostNet-YOLOV4 | 绝缘子及自爆缺陷 | 无人机 | 【99】,【106】 |
| | | ShuffleNetV2 | YOLO-LRDD | 道路损伤 | 移动设备 | 【102】 |
| | | MobileNet | MobileCrack | 沥青路面裂缝 | - | 【100】 |
| **模块化优化** | 用更轻量的组件替换标准组件。 | 深度可分离卷积 | 改进U-Net | 桥梁裂缝 | UAS | 【104】 |
| | | GSConv | IDD-YOLO | 绝缘子缺陷 | - | 【106】 |
| **结构重参数化** | 将复杂的训练时模块融合成简单的推理时模块。 | RepBSB模块 / RepGhost | EMB-YOLO / CDDCR-YOLOv8 | 电表箱缺陷 / 绝缘子缺陷 | - | 【110】,【114】 |
| **自动化搜索** | 自动搜索最优的轻量级架构。 | NAS (例如 MobileNetV3) | - | 铁轨表面缺陷 | - | [v1_81] |

<br>

#### 3.2.4 架构增强技术：注意力与特征融合

为了在架构修改之外进一步提升模型性能，研究人员日益关注于集成能够智能处理特征和融合互补数据源的机制。两种关键策略已变得尤为突出：利用注意力机制动态地聚焦计算资源，以及融合来自多种传感器模态的数据以创建对缺陷性质更全面的理解。

##### ******* 注意力机制：聚焦显著特征

注意力机制受人类视觉认知启发，使网络能够动态地权衡不同特征或空间位置的重要性，从而让模型能够专注于最显著的信息并抑制无关的背景噪声。所审查的文献揭示了各种注意力模块在增强缺陷检测方面的显著和创新性应用，这些模块可大致分为通道注意力、空间与通道混合注意力，以及Transformer固有的自注意力机制。

一种常见的策略是将成熟的、轻量级的注意力模块集成到现有的网络主干中，以在不显著增加计算成本的情况下改善特征表示。**通道注意力**是一种流行的选择，它能自适应地重新校准通道间的特征响应。例如，像Squeeze-and-Excitation (SE)【99】和Efficient Channel Attention (ECA)【102】这样的模块已被成功整合以提升模型性能。在此基础上，能够同时学习关注"什么"和"哪里"的**混合通道与空间注意力**模块，如Convolutional Block Attention Module (CBAM)【110, 113】和Coordinate Attention (CA)【114】，在增强复杂背景下特征的可辨识性方面表现出强大的效果。

除了简单地应用现有模块，多项研究提出了为特定缺陷检测任务量身定制的**新颖或改进的注意力机制**。例如，**Zhang等人[v1_87]** 设计了一个双分支轻量级Transformer模块（LGT），它利用坐标注意力处理局部特征，并利用双层路由注意力（Bi-Level Routing Attention）捕捉全局上下文。此外，诸如Lightweight Channel-Spatial Attention (LCSA)【106】和Mixed Local Channel Attention (MLCA)【116】等定制模块被专门设计出来，以平衡在移动平台上部署时的性能与效率。这些创新清晰地展示了设计专门化注意力策略以应对工业缺陷检测独特挑战的趋势。

*当注意力机制优化模型处理单一数据源信息的方式时，多模态融合则通过结合来自完全不同传感器类型的数据，为实现更全面的理解提供了一条路径。*

##### 3.2.4.2 多模态融合：构建整体视图

在审查的文献中，更受重视的是**多模态特征融合**，它通过整合来自不同传感器的信息来提供一个整体视图。融合策略可大致根据整合发生的层面进行分类：**早期融合**在输入层合并原始数据，**中期融合**在网络内部的不同深度合并特征，而**晚期融合**则合并独立模型的输出或决策。单一传感器模态往往只能提供不完整的画面。通过融合这些数据流，系统可以做出更可靠、更全面的评估。文献揭示了在NDT/SHM领域中丰富的融合策略：

*   **视觉与热成像**：将RGB图像与红外热成像（IRT）数据融合，对于检测能产生热异常的次表面缺陷非常有效。**Alsuhaibani等人[v1_69]** 关于FRP-混凝土结构无损检测的综述强调了这种组合的力量。主动热成像技术（施加外部热源）可以揭示分层和空洞，将这些热数据与视觉图像融合，可以精确定位次表面缺陷。**Oswald-Tranta [v1_89]**详细介绍了一个具体应用，他回顾了用于金属部件的感应热成像。感应涡流加热组件，IRT相机捕捉热响应；裂缝会扰乱热流，产生清晰的热模式，当映射到视觉图像上时，便可提供完整的诊断。

*   **视觉与声学/超声波**：将视觉证据与声发射（AE）或导波超声检测（GUT）的数据相关联，可以深入了解缺陷的活动性和严重性。例如，**Ding等人[v1_57]** 关于风力涡轮机叶片监测的综述解释说，AE传感器可以在损伤变得可见之前很久就检测到叶片复合结构内部活跃裂纹扩展释放的高频应力波。将这种早期预警与随后的无人机视觉检查相融合，可以实现有针对性且高效的维护。同样，**Nuthalapati的综述[v1_90]** 在讨论应力腐蚀开裂时指出，AE可以实时监测不锈钢部件中微裂纹的萌生和扩展。

*   **视觉与电磁**：对于金属结构，将目视检查与涡流检测（ECT）等方法相结合，可以提供全面的诊断。正如**Machado等人[v1_91]** 所述，ECT探头对导电材料的表面和近表面裂缝高度敏感。通过将ECT的精确裂缝检测与视觉系统的更广泛上下文感知相结合，检查员可以更全面地了解关键金属部件的结构完整性。

### 3.3 超越监督学习：新兴范式

尽管缺陷检测领域的大多数研究都基于监督学习，但对大量、精细标注的数据集的依赖仍然是一个巨大的瓶颈。这激发了对替代学习范式的探索，这些范式旨在减少对标注数据的依赖，并增强模型的适应性。

**迁移学习**无疑是这些范式中应用最广、影响最大的一种。该方法并非从零开始训练模型，而是使用在大型通用数据集（如ImageNet）上预训练过的权重来初始化网络。由于模型已经学习了一套丰富的通用视觉特征层次结构，因此它可以在一个规模小得多的、领域特定的缺陷数据集上进行微调。这种策略极大地减少了所需的标注数据量和训练时间，同时通常还能提高最终性能。它的普遍性使其成为许多应用导向论文中一个基础性（尽管常常是默认的）技术，例如那些采用U-Net或YOLO等成熟架构进行特定检测任务的研究【105, 117】。

在标准迁移学习之外，一个充满希望的前沿领域是将**强化学习（RL）**应用于创建更智能、更高效的检测工作流，特别是对于机器人代理。基于RL的代理可以通过试错来学习最优的检测策略。正如**Zhang等人[v1_92]** 所述，无人机在像隧道这样复杂的、GPS信号缺失的环境中导航是一个重大挑战。他们强调深度强化学习（DRL）是一项关键的使能技术。DRL代理可以在仿真环境中进行训练，学习如何导航、避开障碍物并保持最佳的相机角度进行检测，通过高效、全面的数据采集获得奖励。这种"主动视觉"方法标志着一个转变，即从仅仅构建精确的检测器，转向创建能够以最少的人工监督进行学习和适应的、真正智能和自主的检测系统。

### 3.4 本章小结

综上所述，智能缺陷感知的算法支柱发展迅速，已从传统的基于图像块的分类器演进为适用于嵌入式平台部署的、复杂的混合架构和优化的轻量化模型。注意力机制和多模态数据融合的整合，进一步增强了这些系统以更高的精度处理复杂现实世界场景的能力。然而，在平衡模型复杂性、数据多样性和泛化能力方面仍然存在挑战——这些问题正日益通过新兴的学习范式得到解决，并将在后续章节中作为未来挑战和研究方向讨论的核心。 